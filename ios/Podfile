# Resolve react_native_pods.rb with node to allow for hoisting
require Pod::Executable.execute_command('node', ['-p',
  'require.resolve(
    "react-native/scripts/react_native_pods.rb",
    {paths: [process.argv[1]]},
  )', __dir__]).strip

project 'DelicutFitnessApp.xcodeproj' # <-- Add this line

platform :ios, min_ios_version_supported
prepare_react_native_project!
linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

target 'DelicutFitnessApp' do
  config = use_native_modules!
  pod 'clevertap-react-native', :path => '../node_modules/clevertap-react-native'
  pod 'BVLinearGradient', :path => '../node_modules/react-native-linear-gradient'
  pod 'RNFBAnalytics', :path => '../node_modules/@react-native-firebase/analytics'
  pod 'FirebaseCoreInternal', :modular_headers => true
  pod 'GoogleUtilities', :modular_headers => true
  pod 'FirebaseCore', :modular_headers => true
  pod 'RNScreens', :path => '../node_modules/react-native-screens'
  pod 'Firebase', :modular_headers => true
  pod 'FirebaseMessaging', :modular_headers => true
  pod 'RNVectorIcons', :path => '../node_modules/react-native-vector-icons'

  use_react_native!(
    :path => config[:reactNativePath],
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  target 'DelicutFitnessAppTests' do
    inherit! :complete
    # Pods for testing
  end

  post_install do |installer|
    # https://github.com/facebook/react-native/blob/main/packages/react-native/scripts/react_native_pods.rb#L197-L202
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false,
      # :ccache_enabled => true
    )
  end
end
