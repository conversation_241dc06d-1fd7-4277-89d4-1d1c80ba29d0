module.exports = {
  root: true, // Ensures ESLint applies settings only to this project
  parser: '@typescript-eslint/parser', // Parse TypeScript code
  plugins: [
    '@typescript-eslint', // For TypeScript-specific rules
    'react', // For React-specific linting
    'react-native', // For React Native-specific rules
    'prettier', // For Prettier integration
  ],
  extends: [
    'eslint:recommended', // Recommended ESLint rules
    'plugin:@typescript-eslint/recommended', // Recommended rules from @typescript-eslint
    'plugin:react/recommended', // React-specific linting
    'plugin:react-hooks/recommended', // Linting for React Hooks
    'plugin:react-native/all', // React Native-specific rules
    'prettier', // Disables ESLint rules that conflict with <PERSON><PERSON><PERSON>
  ],
  env: {
    browser: true,
    node: true,
    'react-native/react-native': true, // React Native-specific globals
  },
  settings: {
    react: {
      version: 'detect', // Detect the React version
    },
  },
  rules: {
    'prefer-const': 'off',
    // General ESLint Rules
    'no-console': 'off', // Warn on console.log statements
    'no-unused-vars': 'warn', // Warn on unused variables
    'no-undef': 'error', // Disallow undefined variables

    // TypeScript-Specific Rules
    '@typescript-eslint/no-unused-vars': ['warn'], // Type-safe unused vars rule
    '@typescript-eslint/explicit-function-return-type': 'off', // Disable explicit return types
    '@typescript-eslint/no-explicit-any': 'error', // Discourage `any` type usage

    // React Rules
    'react/prop-types': 'off', // Disable prop-types as we use TypeScript
    'react/react-in-jsx-scope': 'off', // React 17+ doesn't require React import in scope
    'react/display-name': 'off', // No need to enforce display names for components

    // React Native Rules
    'react-native/no-unused-styles': 'warn', // Warn on unused styles
    'react-native/split-platform-components': 'warn', // Encourage splitting platform-specific components
    'react-native/no-inline-styles': 'warn', // Discourage inline styles
    'react-native/no-color-literals': 'off', // Allow color literals for flexibility

    // Hooks Rules
    'react-hooks/rules-of-hooks': 'error', // Ensure proper use of hooks
    'react-hooks/exhaustive-deps': 'warn', // Warn on missing dependencies in hooks

    // Prettier Integration
    'prettier/prettier': 'error', // Ensure Prettier formatting
    // Enforce single quotes for strings
    quotes: ['error', 'single'], // Enforce single quotes
    semi: ['error', 'always'], // Enforce semicolons
    // indent: ['error', 2], // Enforce 2-space indentation
    'comma-dangle': ['error', 'always-multiline'], // Enforce comma at the end of multiline lists
    'template-curly-spacing': ['error', 'never'], // No spaces around template literals
  },
  overrides: [
    {
      files: ['*.ts', '*.tsx'], // Specific settings for TypeScript files
      rules: {
        '@typescript-eslint/explicit-module-boundary-types': 'off', // Disable explicit types for module boundaries
      },
    },
  ],
};
