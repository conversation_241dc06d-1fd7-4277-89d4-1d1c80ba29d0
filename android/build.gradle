buildscript {
    ext {
        googlePlayServicesVersion = "+" // default: "+"
        firebaseMessagingVersion = "21.1.0" // default: "21.1.0"
        buildToolsVersion = "35.0.0"
        minSdkVersion = 26
        compileSdkVersion = 35
        targetSdkVersion = 35
        ndkVersion = "26.1.10909125"
        kotlinVersion = "1.9.24"
        multiDexEnabled = true
        supportLibVersion = "28.0.0"
        googlePlayServicesAuthVersion = "20.7.0"
    }
    repositories {
        google()
        mavenCentral()
        maven { url = uri("https://jitpack.io") }
        maven { url = uri("https://maven.fpregistry.io/releases") }
        maven {url 'https://developer.huawei.com/repo/'}
    }
    dependencies {
        classpath 'com.google.gms:google-services:4.4.0'
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
        classpath("com.google.gms:google-services:4.4.2")
        classpath("com.huawei.agconnect:agcp:1.9.1.301")
    }
}

allprojects {
        repositories {
            google()
            mavenCentral()
            maven { url = uri("https://jitpack.io") }
            maven { url = uri("https://maven.fpregistry.io/releases") }
            maven {url 'https://developer.huawei.com/repo/'}
        }
}
apply plugin: "com.facebook.react.rootproject"
