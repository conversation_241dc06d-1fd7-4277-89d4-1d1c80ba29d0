<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- Background Image -->
    <ImageView
        android:id="@+id/background_image"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:src="@drawable/background_image"
        android:scaleType="centerCrop"
        android:contentDescription="@null" />

    <!-- Light/Dark Overlay -->
    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/overlay_color" />

    <!-- Center Logo -->
    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/splash_logo"
        android:scaleType="fitCenter"
        android:layout_centerInParent="true" />
</RelativeLayout>
