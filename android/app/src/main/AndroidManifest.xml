<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="com.huawei.android.permission.APP_PLATFORM" />
    <uses-permission android:name="com.huawei.permission.external_app_settings.USE_COMPONENT" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
    <uses-permission android:name="android.permission.BODY_SENSORS"/>
    <uses-permission android:name="android.permission.health.WRITE_HEART_RATE"/>
    <uses-permission android:name="android.permission.health.WRITE_STEPS"/>
    <uses-permission android:name="android.permission.ACTIVITY_RECOGNITION" />
    <uses-permission android:name="android.permission.health.READ_STEPS" />
    <uses-permission android:name="android.permission.health.READ_DISTANCE" />
    <uses-permission android:name="android.permission.health.READ_HEART_RATE" />
    <uses-permission android:name="android.permission.health.READ_SLEEP" />
    <uses-permission android:name="android.permission.health.READ_WEIGHT" />
    <uses-permission android:name="android.permission.health.READ_ACTIVE_CALORIES_BURNED" />
    <uses-permission android:name="android.permission.health.READ_BLOOD_PRESSURE" />
    <uses-permission android:name="android.permission.health.READ_BLOOD_GLUCOSE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-feature android:name="android.hardware.camera.any" />
    <uses-permission android:name="android.permission.health.READ_TOTAL_CALORIES_BURNED" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />



    <application
        android:name=".MainApplication"
        android:label="@string/app_name"
        android:icon="@mipmap/ic_launcher"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:allowBackup="false"
        android:theme="@style/AppTheme"
        android:supportsRtl="true"
        android:usesCleartextTraffic="true">
        <activity
            android:name=".MainActivity"
            android:label="@string/app_name"
            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
            android:launchMode="singleTask"
            android:windowSoftInputMode="adjustResize"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <!-- Supported web addresses -->
                <data android:scheme="https"
                    android:host="delicut.ae"
                    android:pathPrefix="/" />
                <data android:scheme="https"
                    android:host="pre.delicut.click"
                    android:pathPrefix="/" />
            </intent-filter>
        </activity>

            <activity
    android:name=".PermissionsRationaleActivity"
    android:exported="true">
    <intent-filter>
        <action android:name="androidx.health.ACTION_SHOW_PERMISSIONS_RATIONALE" />
    </intent-filter>
    </activity>

    <activity-alias
    android:name="ViewPermissionUsageActivity"
    android:exported="true"
    android:targetActivity=".MainActivity"
    android:permission="android.permission.START_VIEW_PERMISSION_USAGE">
    <intent-filter>
        <action android:name="android.intent.action.VIEW_PERMISSION_USAGE" />
        <category android:name="android.intent.category.HEALTH_PERMISSIONS" />
    </intent-filter>
    </activity-alias>

        <meta-data android:name="com.google.android.gsm.wallet.api.enabled" android:value="true" />
        <!-- <meta-data
            android:name="CLEVERTAP_PROVIDER_1"
            android:value="@string/hps_manifest_entry" /> -->
        <!-- Push Notification Metadata -->
        <meta-data android:name="com.dieam.reactnativepushnotification.notification_channel_name"
            android:value="fcm_fallback_notification_channel" />
        <meta-data
            android:name="com.dieam.reactnativepushnotification.notification_channel_description"
            android:value="YOUR NOTIFICATION CHANNEL DESCRIPTION" />
        <meta-data android:name="com.dieam.reactnativepushnotification.notification_foreground"
            android:value="true" />
        <meta-data android:name="com.dieam.reactnativepushnotification.channel_create_default"
            android:value="false" />
        <meta-data android:name="com.dieam.reactnativepushnotification.notification_color"
            android:resource="@color/white" />

        <!-- Push Notification Receivers -->
        <receiver
            android:name="com.clevertap.android.sdk.pushnotification.CTPushNotificationReceiver"
            android:exported="false"
            android:enabled="true">
        </receiver>
        <receiver
            android:name="com.dieam.reactnativepushnotification.modules.RNPushNotificationActions" />
        <receiver
            android:name="com.dieam.reactnativepushnotification.modules.RNPushNotificationPublisher" />
        <receiver
            android:name="com.dieam.reactnativepushnotification.modules.RNPushNotificationBootEventReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>

        <!-- Push Notification Service -->
        <service
            android:name="com.clevertap.android.sdk.pushnotification.fcm.FcmMessageListenerService"
            android:exported="true">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>
        <service
            android:name="com.clevertap.android.sdk.pushnotification.amp.CTBackgroundJobService"
            android:exported="false"
            android:enabled="true" />
        <service
            android:name="com.clevertap.android.sdk.pushnotification.CTPushMessageListenerService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>
        <service
            android:name="com.dieam.reactnativepushnotification.modules.RNPushNotificationListenerService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>
        <service
            android:name="com.clevertap.android.sdk.pushnotification.hms.HmsMessageService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.huawei.push.action.MESSAGING_EVENT" />
            </intent-filter>
        </service>
        <service
            android:name="com.clevertap.android.geofence.CTGeofenceTaskService"
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:exported="true" />

        <meta-data
            android:name="CLEVERTAP_ACCOUNT_ID"
            android:value="${CLEVERTAP_ACCOUNT_ID}" />
        <meta-data
            android:name="CLEVERTAP_TOKEN"
            android:value="${CLEVERTAP_ACCOUNT_TOKEN}" />
        <meta-data android:name="com.google.android.gms.wallet.api.enabled" android:value="true" />
    </application>
</manifest>
