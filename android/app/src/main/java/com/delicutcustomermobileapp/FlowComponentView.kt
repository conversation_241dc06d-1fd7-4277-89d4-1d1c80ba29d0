package com.fitness.app

import android.content.Context
import android.util.Log
import android.widget.FrameLayout
import com.checkout.components.core.CheckoutComponentsFactory
import com.checkout.components.interfaces.Environment
import com.checkout.components.interfaces.api.CheckoutComponents
import com.checkout.components.interfaces.component.CheckoutComponentConfiguration
import com.checkout.components.interfaces.component.ComponentCallback
import com.checkout.components.interfaces.error.CheckoutError
import com.checkout.components.interfaces.model.PaymentMethodName
import com.checkout.components.interfaces.model.PaymentSessionResponse
import com.checkout.components.interfaces.uicustomisation.BorderRadius
import com.checkout.components.interfaces.uicustomisation.designtoken.ColorTokens
import com.checkout.components.interfaces.uicustomisation.designtoken.DesignTokens
import com.facebook.react.bridge.ReactContext
import com.facebook.react.uimanager.events.RCTEventEmitter
import com.checkout.components.interfaces.uicustomisation.font.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class FlowComponentView(context: Context) : FrameLayout(context) {

    private lateinit var checkoutComponents: CheckoutComponents
    private var flowContainer: FrameLayout? = null

    private var paymentSessionID: String? = null
    private var paymentSessionToken: String? = null
    private var paymentSessionSecret: String? = null

    init {
        // setBackgroundColor(android.graphics.Color.LTGRAY)
        Log.d("✅ Step 3", "FlowComponentView Initialized")
    }

    fun setPaymentSessionID(id: String) {
        this.paymentSessionID = id
        triggerPaymentIfReady()
    }

    fun setPaymentSessionToken(token: String) {
        this.paymentSessionToken = token
        triggerPaymentIfReady()
    }

    fun setPaymentSessionSecret(secret: String) {
        this.paymentSessionSecret = secret
        triggerPaymentIfReady()
    }




    private fun triggerPaymentIfReady() {
        if (!paymentSessionID.isNullOrEmpty() &&
            !paymentSessionToken.isNullOrEmpty() &&
            !paymentSessionSecret.isNullOrEmpty()
        ) {
            startPaymentSession(paymentSessionID!!, paymentSessionToken!!, paymentSessionSecret!!)
        }
    }

    fun startPaymentSession(paymentSessionID: String, paymentSessionToken: String, paymentSessionSecret: String) {
                Log.d("✅ Step 4", "Received Payment Session ID: $paymentSessionID")
                Log.d("✅ Step 4", "Received Payment Session Token: $paymentSessionToken")
                Log.d("✅ Step 4", "Received Payment Session Secret: $paymentSessionSecret")
                Log.d("✅ Step 4", "Initializing Payment Session")

                flowContainer = FrameLayout(context).apply {
                    layoutParams = LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT)
                    // setBackgroundColor(android.graphics.Color.WHITE)
                }
                <EMAIL>()
                <EMAIL>(flowContainer)

                val designTokens = DesignTokens(
                    colorTokens = ColorTokens(
                    colorPrimary = 0xFFEA5D29.toLong(),
                    colorAction = 0xFFEA5D29.toLong(),
                    colorBackground = 0xFFFFFFFF.toLong(),
                    colorBorder = 0xFFEA5D29.toLong(),
                    colorDisabled = 0xFFB8B8B8.toLong(),
                    colorFormBackground = 0xFFFFFFFF.toLong(),
                    colorFormBorder = 0xFFC9C9C9.toLong(),
                    colorInverse = 0xFFFFFFFF.toLong(),
                    colorOutline = 0xFFEA5D29.toLong(),
                    colorSecondary = 0xFF000000.toLong(),
                    colorSuccess = 0xFFEA5D29.toLong(),
                    colorError = 0xFFFF0000.toLong(),
                    ),
                    borderButtonRadius = BorderRadius(all = 20),
                    borderFormRadius = BorderRadius(all = 12),
                    fonts = mapOf(
                        FontName.Subheading to Font(
                            fontStyle = FontStyle.Normal,
                            fontWeight = FontWeight.ExtraBold,
                            fontFamily = FontFamily.SansSerif,
                        ),
                        FontName.Input to Font(
                            fontStyle = FontStyle.Normal,
                            fontWeight = FontWeight.ExtraBold,
                            fontFamily = FontFamily.SansSerif,
                        ),
                        FontName.Button to Font(
                            fontStyle = FontStyle.Normal,
                            fontWeight = FontWeight.ExtraBold,
                            fontFamily = FontFamily.SansSerif,
                        ),
                        FontName.Label to Font(
                            fontStyle = FontStyle.Normal,
                            fontWeight = FontWeight.Light,
                            fontFamily = FontFamily.SansSerif,
                        ),
                    )
                )

                val configuration = CheckoutComponentConfiguration(
                    context = (context as? ReactContext)?.currentActivity ?: context,
                    paymentSession = PaymentSessionResponse(
                        id = paymentSessionID,
                        paymentSessionToken = paymentSessionToken,
                        paymentSessionSecret = paymentSessionSecret
                    ),
                    componentCallback = ComponentCallback(
                        onReady = { component ->
                        Log.d("✅ Step 5", "Component Ready: ${component.name}")

                        val reactContext = context as? ReactContext
                        reactContext?.getJSModule(RCTEventEmitter::class.java)
                            ?.receiveEvent(<EMAIL>, "onFlowComponentReady", null)

                        },
                        onSubmit = { component ->
                            Log.d("✅ Step 6", "Component Submitted: ${component}")
                        },
                        onSuccess = { component, paymentID ->
                            Log.d("✅ Step 7", "Payment Success: ${component.name}, Payment ID: $paymentID")
                        },
                        onError = { component, checkoutError ->
                            Log.e("❌ Step 8", "Payment Error: ${checkoutError.message}")
                        }
                    ),
                    publicKey = BuildConfig.FLOW_API_KEY,
                    environment = Environment.SANDBOX,
                    appearance = designTokens,
                )

                CoroutineScope(Dispatchers.IO).launch {
                    try {
                        checkoutComponents = CheckoutComponentsFactory(config = configuration).create()
                        val flowComponent = checkoutComponents.create(PaymentMethodName.Card)
                        withContext(Dispatchers.Main) {
                            flowContainer?.post {
                                val view = flowComponent.provideView(flowContainer!!)

                                view.layoutParams = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT)

                                flowContainer?.addView(view)

                                view.requestLayout()
                                view.invalidate()

                                <EMAIL>()
                                <EMAIL>()
                                flowContainer?.requestLayout()
                                flowContainer?.invalidate()

                                Log.d("✅ Step 8", "Added Flow Component to View")

                                view.viewTreeObserver.addOnGlobalLayoutListener {
                                    Log.d(
                                        "🧩 ViewDraw",
                                        "Flow view layout: width=${view.width}, height=${view.height}, visibility=${view.visibility}, alpha=${view.alpha}"
                                    )
                                }
                            }
                        }
                    } catch (checkoutError: CheckoutError) {
                        Log.e("❌ Error", "Error initializing Flow component", checkoutError)
                    }
                }
    }

}
