package com.fitness.app

import android.util.Log
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.NativeModule
import com.facebook.react.uimanager.ViewManager
import com.facebook.react.ReactPackage
import com.fitness.app.GpayFlowViewManager




class GpayFlowPackage : ReactPackage {
    override fun createNativeModules(reactContext: ReactApplicationContext): List<NativeModule> {
        Log.d("✅ Step 9", "FlowPackage: Native Modules Registered")
        return listOf(GpayFlowViewManager())
    }

    override fun createViewManagers(reactContext: ReactApplicationContext): List<ViewManager<*, *>> {
        Log.d("✅ Step 10", "FlowPackage: View Managers Registered")
        return listOf(GpayFlowViewManager())
    }
}
