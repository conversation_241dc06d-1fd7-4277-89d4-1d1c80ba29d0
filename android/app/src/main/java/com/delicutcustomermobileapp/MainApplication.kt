package com.fitness.app

import android.app.Application
import com.facebook.react.PackageList
import com.facebook.react.ReactApplication
import com.facebook.react.ReactHost
import com.facebook.react.ReactNativeHost
import com.facebook.react.ReactPackage
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint.load
import com.facebook.react.defaults.DefaultReactHost.getDefaultReactHost
import com.facebook.react.defaults.DefaultReactNativeHost
import com.facebook.react.soloader.OpenSourceMergedSoMapping
import com.facebook.soloader.SoLoader
import com.lugg.RNCConfig.RNCConfigPackage
import com.dieam.reactnativepushnotification.ReactNativePushNotificationPackage;
import com.clevertap.react.CleverTapPackage;
import com.clevertap.react.CleverTapRnAPI;
import com.clevertap.android.sdk.ActivityLifecycleCallback;
import com.clevertap.android.sdk.CleverTapAPI;
import com.clevertap.android.sdk.CleverTapAPI.LogLevel;
import com.proyecto26.inappbrowser.RNInAppBrowserPackage;
import org.devio.rn.splashscreen.SplashScreenReactPackage;
import com.fitness.app.FlowPackage
import com.fitness.app.GpayFlowPackage
import com.clevertap.android.geofence.CTGeofenceAPI
import com.clevertap.android.geofence.CTGeofenceSettings
// import com.baidu.android.pushservice.PushManager;
class MainApplication : Application(), ReactApplication {

  override val reactNativeHost: ReactNativeHost =
      object : DefaultReactNativeHost(this) {
        override fun getPackages(): List<ReactPackage> =
            PackageList(this).packages.apply {
              // Packages that cannot be autolinked yet can be added manually here, for example:
              // add(MyReactNativePackage())
                 add(CleverTapPackage())
                 add(FlowPackage())
                 add(GpayFlowPackage())
            }

        override fun getJSMainModuleName(): String = "index"

        override fun getUseDeveloperSupport(): Boolean = BuildConfig.DEBUG

        override val isNewArchEnabled: Boolean = BuildConfig.IS_NEW_ARCHITECTURE_ENABLED
        override val isHermesEnabled: Boolean = BuildConfig.IS_HERMES_ENABLED
      }

  override val reactHost: ReactHost
    get() = getDefaultReactHost(applicationContext, reactNativeHost)

  override fun onCreate() {
    CleverTapAPI.setDebugLevel(LogLevel.VERBOSE); // Optional: for debug logs
    ActivityLifecycleCallback.register(this);
    CleverTapRnAPI.initReactNativeIntegration(this);
    super.onCreate()

        // Initialize CleverTap instance
        val cleverTapInstance = CleverTapAPI.getDefaultInstance(applicationContext)

        cleverTapInstance?.let { nonNullCT ->
            val geofenceSettings = CTGeofenceSettings.Builder()
                .enableBackgroundLocationUpdates(true)
                .setGeofenceMonitoringCount(10)
                .setLocationAccuracy(CTGeofenceSettings.ACCURACY_HIGH)//byte value for Location Accuracy
                .setLocationFetchMode(CTGeofenceSettings.FETCH_LAST_LOCATION_PERIODIC)//byte value for Fetch Mode
                .setInterval(1800)
                .build()

            CTGeofenceAPI.getInstance(applicationContext).init(geofenceSettings, nonNullCT)
        }



    // Register for Baidu Push
    // PushManager.startWork(
    //     getApplicationContext(),
    //     PushConstants.LOGIN_TYPE_API_KEY,
    //     "YOUR_BAIDU_PUSH_API_KEY"
    // );
    SoLoader.init(this, OpenSourceMergedSoMapping)
    if (BuildConfig.IS_NEW_ARCHITECTURE_ENABLED) {
      // If you opted-in for the New Architecture, we load the native entry point for this app.
      load()
    }
  }
}
