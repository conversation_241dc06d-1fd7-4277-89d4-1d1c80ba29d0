package com.fitness.app

import com.facebook.react.bridge.ReadableArray
import com.facebook.react.common.MapBuilder
import com.facebook.react.uimanager.SimpleViewManager
import com.facebook.react.uimanager.ThemedReactContext
import com.facebook.react.uimanager.annotations.ReactProp

class GpayFlowViewManager : SimpleViewManager<GpayFlowComponentView>() {

    companion object {
        private const val COMMAND_SUBMIT_PAYMENT = 1
    }

    override fun getName(): String {
        return "GpayFlowComponent"
    }

    override fun createViewInstance(reactContext: ThemedReactContext): GpayFlowComponentView {
        return GpayFlowComponentView(reactContext)
    }

    // Props from JS
    @ReactProp(name = "paymentSessionID")
    fun setPaymentSessionID(view: GpayFlowComponentView, paymentSessionID: String) {
        view.setPaymentSessionID(paymentSessionID)
    }

    @ReactProp(name = "paymentSessionToken")
    fun setPaymentSessionToken(view: GpayFlowComponentView, paymentSessionToken: String) {
        view.setPaymentSessionToken(paymentSessionToken)
    }

    @ReactProp(name = "paymentSessionSecret")
    fun setPaymentSessionSecret(view: GpayFlowComponentView, paymentSessionSecret: String) {
        view.setPaymentSessionSecret(paymentSessionSecret)
    }

    // Add command map
    override fun getCommandsMap(): Map<String, Int> {
        return MapBuilder.of("submitPayment", COMMAND_SUBMIT_PAYMENT)
    }

    // Handle commands
    override fun receiveCommand(
        view: GpayFlowComponentView,
        commandId: Int,
        args: ReadableArray?
    ) {
        when (commandId) {
            COMMAND_SUBMIT_PAYMENT -> view.submitPayment()
        }
    }

    // Send events to JS
    override fun getExportedCustomDirectEventTypeConstants(): MutableMap<String, Any> {
        return mutableMapOf(
            "onFlowComponentReady" to mapOf("registrationName" to "onFlowComponentReady")
        )
    }
}
