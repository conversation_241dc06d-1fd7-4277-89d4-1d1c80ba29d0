package com.fitness.app
import android.content.Intent
import android.os.Build
import android.os.Bundle;
import com.facebook.react.ReactActivity
import org.devio.rn.splashscreen.SplashScreen;
import com.facebook.react.ReactActivityDelegate
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint.fabricEnabled
import com.facebook.react.defaults.DefaultReactActivityDelegate
import com.clevertap.android.sdk.CleverTapAPI
import dev.matinzd.healthconnect.permissions.HealthConnectPermissionDelegate

class MainActivity : ReactActivity() {
  override fun onCreate(savedInstanceState: Bundle?) {
    SplashScreen.show(this)
    HealthConnectPermissionDelegate.setPermissionDelegate(this)
    super.onCreate(savedInstanceState)
    }

  /**
   * Returns the name of the main component registered from JavaScript. This is used to schedule
   * rendering of the component.
   */
  override fun getMainComponentName(): String = "Delicut-FitnessApp"

override fun onNewIntent(intent: Intent) {
   super.onNewIntent(intent)

   // On Android 12 and onwards, raise notification clicked event and get the click callback
   if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
        CleverTapAPI.getDefaultInstance(this)?.pushNotificationClickedEvent(intent?.extras)
        CleverTapAPI.getDefaultInstance(this)?.pushNotificationViewedEvent(intent?.extras)
        }
    }

  /**
   * Returns the instance of the [ReactActivityDelegate]. We use [DefaultReactActivityDelegate]
   * which allows you to enable New Architecture with a single boolean flags [fabricEnabled]
   */
  override fun createReactActivityDelegate(): ReactActivityDelegate =
      DefaultReactActivityDelegate(this, mainComponentName, fabricEnabled)
}
