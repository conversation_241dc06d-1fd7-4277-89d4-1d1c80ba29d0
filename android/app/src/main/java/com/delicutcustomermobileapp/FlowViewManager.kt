package com.fitness.app

import com.facebook.react.uimanager.SimpleViewManager
import com.facebook.react.uimanager.ThemedReactContext
import com.facebook.react.uimanager.annotations.ReactProp

class FlowViewManager : SimpleViewManager<FlowComponentView>() {

    override fun getName(): String {
        return "FlowComponent"
    }

    override fun createViewInstance(reactContext: ThemedReactContext): FlowComponentView {
        return FlowComponentView(reactContext)
    }

    @ReactProp(name = "paymentSessionID")
    fun setPaymentSessionID(view: FlowComponentView, paymentSessionID: String) {
        view.setPaymentSessionID(paymentSessionID)
    }

    @ReactProp(name = "paymentSessionToken")
    fun setPaymentSessionToken(view: FlowComponentView, paymentSessionToken: String) {
        view.setPaymentSessionToken(paymentSessionToken)
    }

    @ReactProp(name = "paymentSessionSecret")
    fun setPaymentSessionSecret(view: FlowComponentView, paymentSessionSecret: String) {
        view.setPaymentSessionSecret(paymentSessionSecret)
    }

    override fun getExportedCustomDirectEventTypeConstants(): MutableMap<String, Any>? {
        return mutableMapOf(
            "onFlowComponentReady" to mapOf("registrationName" to "onFlowComponentReady")

        )
    }
}
