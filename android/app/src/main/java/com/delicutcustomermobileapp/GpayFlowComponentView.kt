package com.fitness.app

import android.app.Activity
import android.content.Context
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.FrameLayout
import com.checkout.components.core.CheckoutComponentsFactory
import com.checkout.components.interfaces.Environment
import com.checkout.components.interfaces.api.CheckoutComponents
import com.checkout.components.interfaces.component.CheckoutComponentConfiguration
import com.checkout.components.interfaces.component.ComponentCallback
import com.checkout.components.interfaces.error.CheckoutError
import com.checkout.components.interfaces.model.PaymentMethodName
import com.checkout.components.interfaces.model.PaymentSessionResponse
import com.checkout.components.interfaces.uicustomisation.BorderRadius
import com.checkout.components.interfaces.uicustomisation.designtoken.ColorTokens
import com.checkout.components.interfaces.uicustomisation.designtoken.DesignTokens
import com.checkout.components.interfaces.uicustomisation.font.*
import com.checkout.components.wallet.wrapper.GooglePayFlowCoordinator
import com.facebook.react.bridge.ReactContext
import com.facebook.react.uimanager.events.RCTEventEmitter
import kotlinx.coroutines.*

class GpayFlowComponentView(context: Context) : FrameLayout(context) {

    private lateinit var checkoutComponents: CheckoutComponents
    private var flowContainer: FrameLayout? = null

    private var paymentSessionID: String? = null
    private var paymentSessionToken: String? = null
    private var paymentSessionSecret: String? = null

    fun setPaymentSessionID(id: String) {
        this.paymentSessionID = id
        triggerPaymentIfReady()
    }

    fun setPaymentSessionToken(token: String) {
        this.paymentSessionToken = token
        triggerPaymentIfReady()
    }

    fun setPaymentSessionSecret(secret: String) {
        this.paymentSessionSecret = secret
        triggerPaymentIfReady()
    }

    private fun triggerPaymentIfReady() {
        if (!paymentSessionID.isNullOrEmpty() &&
            !paymentSessionToken.isNullOrEmpty() &&
            !paymentSessionSecret.isNullOrEmpty()
        ) {
            startPaymentSession(paymentSessionID!!, paymentSessionToken!!, paymentSessionSecret!!)
        }
    }

   // Add this method to GpayFlowComponentView class
    fun submitPayment() {
        android.util.Log.d("GpayFlow", "🔘 Attempting smart submit")

        try {
            // Step 1: Try to get the actual GooglePay component
            val gpayComponent = checkoutComponents.create(PaymentMethodName.GooglePay)
            if (gpayComponent == null) {
                android.util.Log.e("GpayFlow", "❌ Google Pay component is null")
                return
            }

            android.util.Log.d("GpayFlow", "✅ Got Google Pay component: ${gpayComponent.javaClass.name}")

            // Step 2: Look for a method that accepts a PaymentComponent
            for (method in checkoutComponents.javaClass.methods) {
                val methodName = method.name
                if (methodName.contains("submit", ignoreCase = true)) {
                    val paramTypes = method.parameterTypes

                    if (paramTypes.size == 1 &&
                        paramTypes[0].isAssignableFrom(gpayComponent.javaClass.interfaces.firstOrNull())) {

                        android.util.Log.d("GpayFlow", "🟢 Invoking $methodName with Google Pay component")

                        method.invoke(checkoutComponents, gpayComponent)

                        android.util.Log.d("GpayFlow", "✅ Submit invoked successfully via $methodName")
                        return
                    } else {
                        android.util.Log.d(
                            "GpayFlow",
                            "⚠️ Method $methodName has unsupported param types: ${paramTypes.joinToString { it.name }}"
                        )
                    }
                }
            }

            android.util.Log.e("GpayFlow", "❌ No compatible submit method found")
        } catch (e: Exception) {
            android.util.Log.e("GpayFlow", "❌ Error during smart submit logic", e)
        }
    }



    private fun startPaymentSession(paymentSessionID: String, paymentSessionToken: String, paymentSessionSecret: String) {
        val activity = (context as? ReactContext)?.currentActivity
        if (activity == null) {
            Log.e("GpayFlow", "❌ No current activity found for GooglePayFlowCoordinator")
            return
        }

        flowContainer = FrameLayout(context).apply {
            layoutParams = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT)
        }
        removeAllViews()
        addView(flowContainer)

        val designTokens = DesignTokens(
            colorTokens = ColorTokens(
                colorPrimary = 0xFFEA5D29.toLong(),
                colorAction = 0xFFEA5D29.toLong(),
                colorBackground = 0xFFFFFFFF.toLong(),
                colorBorder = 0xFFEA5D29.toLong(),
                colorDisabled = 0xFFB8B8B8.toLong(),
                colorFormBackground = 0xFFFFFFFF.toLong(),
                colorFormBorder = 0xFFC9C9C9.toLong(),
                colorInverse = 0xFFFFFFFF.toLong(),
                colorOutline = 0xFFEA5D29.toLong(),
                colorSecondary = 0xFF000000.toLong(),
                colorSuccess = 0xFF00C853.toLong(),
                colorError = 0xFFFF1744.toLong()
            ),
            borderButtonRadius = BorderRadius(all = 20),
            borderFormRadius = BorderRadius(all = 12),
            fonts = mapOf(
                FontName.Subheading to Font(
                    fontStyle = FontStyle.Normal,
                    fontWeight = FontWeight.ExtraBold,
                    fontFamily = FontFamily.SansSerif
                ),
                FontName.Label to Font(
                    fontStyle = FontStyle.Normal,
                    fontWeight = FontWeight.Light,
                    fontFamily = FontFamily.SansSerif
                )
            )
        )

        val coordinator = GooglePayFlowCoordinator(
            context = activity,
            handleActivityResult = { resultCode, data ->
                checkoutComponents.handleActivityResult(resultCode, data)
            }
        )

        val configuration = CheckoutComponentConfiguration(
            context = activity,
            paymentSession = PaymentSessionResponse(
                id = paymentSessionID,
                paymentSessionToken = paymentSessionToken,
                paymentSessionSecret = paymentSessionSecret
            ),
            componentCallback = ComponentCallback(
                onReady = { component ->
                    Log.d("GpayFlow", "✅ Component ready")
                    // Notify React Native to trigger payment
                    (context as? ReactContext)?.getJSModule(RCTEventEmitter::class.java)
                        ?.receiveEvent(<EMAIL>, "onFlowComponentReady", null)
                },
                onSubmit = { component ->
                    Log.d("GpayFlow", "📦 Component Submitted: $component")
                },
                onSuccess = { component, paymentID ->
                    Log.d("GpayFlow", "✅ Payment Success: ${component.name}, ID: $paymentID")
                },
                onError = { _, error ->
                    Log.e("GpayFlow", "❌ Payment Error: ${error.message}")
                }
            ),
            publicKey = BuildConfig.FLOW_API_KEY,
            environment = Environment.SANDBOX,
            appearance = designTokens,
            flowCoordinators = mapOf(PaymentMethodName.GooglePay to coordinator)
        )

        CoroutineScope(Dispatchers.IO).launch {
            try {
                checkoutComponents = CheckoutComponentsFactory(config = configuration).create()
                val flowComponent = checkoutComponents.create(PaymentMethodName.GooglePay)

                if (!flowComponent.isAvailable()) {
                    Log.e("GpayFlow", "❌ Google Pay component is not available")
                    return@launch
                }

                withContext(Dispatchers.Main) {
                    val view = flowComponent.provideView(flowContainer!!)
                    view.layoutParams = LayoutParams(
                        LayoutParams.MATCH_PARENT,
                        (420 * resources.displayMetrics.density).toInt()
                    )

                    flowContainer?.apply {
                        removeAllViews()
                        addView(view)
                        requestLayout()
                        invalidate()
                    }

                    // Log view class for debugging
                    Log.d("GpayFlow", "👉 Provided View Class: ${view::class.java.simpleName}")
                }

            } catch (error: CheckoutError) {
                Log.e("GpayFlow", "❌ Checkout Error", error)
            }
        }
    }

    private fun findButtonInView(view: View): Button? {
        if (view is Button) return view
        if (view is ViewGroup) {
            for (i in 0 until view.childCount) {
                val result = findButtonInView(view.getChildAt(i))
                if (result != null) return result
            }
        }
        return null
    }
}
