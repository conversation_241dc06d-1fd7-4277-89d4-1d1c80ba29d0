{"name": "delicut-fitnessapp", "version": "0.0.1", "private": true, "scripts": {"android": "ENVFILE=.env.dev react-native run-android", "ios": "ENVFILE=.env.dev react-native run-ios", "lint": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "start": "react-native start", "test": "jest", "prepare": "husky install", "clean:android": "cd android && ./gradlew clean", "build:android:prod": "cd android && ./gradlew clean && ENVFILE=.env.prod ./gradlew assembleRelease", "build:android:dev:debug": "cd android && ./gradlew clean && ENVFILE=.env.dev ./gradlew assembleDebug"}, "dependencies": {"@hmscore/react-native-hms-push": "^6.12.0-303", "@hookform/resolvers": "^3.10.0", "@react-native-async-storage/async-storage": "^2.1.0", "@react-native-community/push-notification-ios": "^1.11.0", "@react-native-cookies/cookies": "^6.2.1", "@react-native-firebase/analytics": "^21.12.0", "@react-native-firebase/app": "^21.12.0", "@react-native-firebase/messaging": "^21.12.0", "@react-native-google-signin/google-signin": "^14.0.1", "@react-native/gradle-plugin": "^0.77.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/drawer": "^7.1.1", "@react-navigation/native": "^7.0.14", "@react-navigation/stack": "^7.1.1", "@reduxjs/toolkit": "^2.5.0", "@supabase/supabase-js": "^2.49.4", "@types/react-native": "^0.72.8", "@types/react-navigation": "^3.0.8", "@types/react-redux": "^7.1.34", "axios": "^1.7.9", "clevertap-react-native": "^3.4.0", "csv-parser": "^3.2.0", "date-fns": "^4.1.0", "eslint-plugin-prettier": "^5.2.1", "formik": "^2.4.6", "i18next": "^24.2.1", "libphonenumber-js": "^1.11.20", "lodash": "^4.17.21", "moment": "^2.30.1", "moment-timezone": "^0.5.46", "pg": "^8.16.0", "query-string": "^9.1.1", "react": "18.3.1", "react-error-boundary": "^5.0.0", "react-hook-form": "^7.56.4", "react-i18next": "^15.4.0", "react-native": "0.76.5", "react-native-calendars": "^1.1309.1", "react-native-collapsible": "^1.6.2", "react-native-config": "^1.5.3", "react-native-confirmation-code-field": "^7.4.0", "react-native-country-codes-picker": "^2.3.5", "react-native-date-picker": "^5.0.10", "react-native-element-dropdown": "^2.12.4", "react-native-geolocation-service": "^5.3.1", "react-native-gesture-handler": "^2.22.1", "react-native-google-fit": "^0.21.0", "react-native-health": "^1.19.0", "react-native-health-connect": "^3.3.2", "react-native-inappbrowser-reborn": "^3.7.0", "react-native-linear-gradient": "^2.8.3", "react-native-modal": "^13.0.1", "react-native-modal-datetime-picker": "^18.0.0", "react-native-payment-icons": "^1.0.11", "react-native-push-notification": "^8.1.1", "react-native-raw-bottom-sheet": "^3.0.0", "react-native-reanimated": "^3.16.7", "react-native-reanimated-carousel": "^3.5.1", "react-native-responsive-fontsize": "^0.5.1", "react-native-safe-area-context": "^5.0.0", "react-native-screens": "^4.4.0", "react-native-share": "^12.0.9", "react-native-splash-screen": "^3.3.0", "react-native-star-rating-widget": "^1.9.2", "react-native-super-grid": "^6.0.1", "react-native-toast-message": "^2.2.1", "react-native-url-polyfill": "^2.0.0", "react-native-vector-icons": "^10.2.0", "react-native-vision-camera": "^4.6.4", "react-redux": "^9.2.0", "redux-saga": "^1.3.0", "yup": "^1.6.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.26.0", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.26.0", "@babel/runtime": "^7.25.0", "@commitlint/cli": "^19.6.1", "@commitlint/config-conventional": "^19.6.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.76.5", "@react-native/eslint-config": "0.76.5", "@react-native/metro-config": "0.76.5", "@react-native/typescript-config": "0.76.5", "@types/lodash": "^4.17.17", "@types/react": "^18.3.18", "@types/react-native-calendar-picker": "^8.0.0", "@types/react-native-push-notification": "^8.1.4", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^18.0.0", "@typescript-eslint/eslint-plugin": "^8.19.1", "@typescript-eslint/parser": "^8.19.1", "babel-jest": "^29.7.0", "eslint": "^8.19.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.37.3", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-native": "^5.0.0", "husky": "^9.1.7", "jest": "^29.6.3", "prettier": "^3.4.2", "react-native-dotenv": "^3.4.11", "react-native-svg": "^15.12.0", "react-native-svg-transformer": "^1.5.1", "ts-jest": "^29.2.5", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}