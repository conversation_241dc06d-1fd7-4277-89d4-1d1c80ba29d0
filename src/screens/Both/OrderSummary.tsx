import React from 'react';
import { ScrollView, View } from 'react-native';
import BottomPayView from '../../components/OrderSummary/BottomPayView';
import CouponView from '../../components/OrderSummary/CouponView';
import PaymentMethos from '../../components/OrderSummary/PaymentMethos';
import SelectedMeal from '../../components/OrderSummary/SelectedMeal';
import SummaryView from '../../components/OrderSummary/SummaryView';
import YourDetails from '../../components/OrderSummary/YourDetails';
import { useTheme } from '../../theme';
import { RFont } from '../../theme/fonts';
import {
  gapStyles,
  paddingStyles,
} from '../../theme/styles/commonMarginPadding';
import { globalStyles } from '../../theme/styles/globalStyles';

const OrderSummary = () => {
  const colors = useTheme();
  return (
    <View
      style={[
        globalStyles.flex1,
        paddingStyles.p16,
        { backgroundColor: colors?.primary_cream },
      ]}
    >
      <ScrollView
        showsVerticalScrollIndicator={false}
        persistentScrollbar
        contentContainerStyle={[
          gapStyles.gap_16,
          { paddingBottom: RFont(150) },
        ]}
      >
        <SelectedMeal />
        <YourDetails />
        <CouponView />
        <PaymentMethos />
        <SummaryView />
      </ScrollView>
      <BottomPayView />
    </View>
  );
};

export default OrderSummary;
