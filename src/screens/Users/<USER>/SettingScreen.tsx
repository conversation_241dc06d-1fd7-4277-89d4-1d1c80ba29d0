import { GoogleSignin } from '@react-native-google-signin/google-signin';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import React, { useCallback, useMemo } from 'react';
import {
  Al<PERSON>,
  ScrollView,
  Text,
  TextStyle,
  View,
  ViewStyle,
} from 'react-native';
import {
  Bell,
  Feedback,
  Headphone,
  InfoColoredIcon,
  KcaConsumedIcon3,
  Walk,
  Weight,
} from '../../../assets/images';
import { PrimaryBtn } from '../../../components/Buttons/Btns';
import {
  CardBorder,
  CardTitle,
  CardValueContent,
  GreetingCard,
} from '../../../components/Settings/Card';
import { useTheme } from '../../../theme';
import { fontStyles } from '../../../theme/fonts';
import {
  gapStyles,
  paddingStyles,
} from '../../../theme/styles/commonMarginPadding';
import {
  borderWidthStyles,
  globalStyles,
} from '../../../theme/styles/globalStyles';
import { supabase } from '../../fitness/supabaseClient';

// Type definitions
interface Theme {
  primary_cream: string;
  grey_7A7A7A: string;
  primary_grenade: string;
  transparent: string;
}

// Use the actual SvgProps from react-native-svg
interface SvgIconProps {
  height?: string | number;
  width?: string | number;
  color?: string;
  [key: string]: any; // Allow other SVG props
}

type IconComponent = React.ComponentType<SvgIconProps>;

interface UserData {
  name: string;
  email: string;
  phone?: string;
}

interface BaseItem {
  Icon: any;
  text: string;
  showBorder: boolean;
}

interface GoalItem extends BaseItem {
  id: 'food' | 'steps' | 'weight';
  value: string;
}

interface AppSettingItem extends BaseItem {
  id: 'coach' | 'health';
  value: string;
}

interface SupportItem extends BaseItem {
  id: 'feedback' | 'contact' | 'about';
}

type GoalId = GoalItem['id'];
type SettingId = AppSettingItem['id'];
type SupportId = SupportItem['id'];

// Event handler types
type GoalPressHandler = (goalId: GoalId) => void;
type SettingPressHandler = (settingId: SettingId) => void;
type SupportPressHandler = (supportId: SupportId) => void;
type LogoutHandler = () => void;

// Component prop types
interface UserProfileProps {
  color: Theme;
}

interface GoalsSectionProps {
  color: Theme;
  onGoalPress: GoalPressHandler;
}

interface AppSettingsSectionProps {
  color: Theme;
  onSettingPress: SettingPressHandler;
}

interface SupportSectionProps {
  color: Theme;
  onSupportPress: SupportPressHandler;
}

interface LogoutButtonProps {
  color: Theme;
  onLogout: LogoutHandler;
}

// Constants with proper typing
const USER_DATA: UserData = {
  name: 'Jonathan Green',
  email: '<EMAIL>',
  // phone: '+971549424373', // Commented as in original
};

const GOALS_DATA: readonly GoalItem[] = [
  {
    id: 'food',
    Icon: KcaConsumedIcon3,
    text: 'Food',
    value: '1200 kcal/day',
    showBorder: true,
  },
  {
    id: 'steps',
    Icon: Walk,
    text: 'Steps',
    value: '6000 steps/day',
    showBorder: true,
  },
  {
    id: 'weight',
    Icon: Weight,
    text: 'Weight',
    value: '64.5kg',
    showBorder: false,
  },
] as const;

const APP_SETTINGS_DATA: readonly AppSettingItem[] = [
  {
    id: 'coach',
    Icon: Bell,
    text: 'Coach Preference',
    value: 'Alex',
    showBorder: true,
  },
  {
    id: 'health',
    Icon: Bell,
    text: 'Health data',
    value: 'Apple Health connected',
    showBorder: false,
  },
] as const;

const SUPPORT_DATA: readonly SupportItem[] = [
  {
    id: 'feedback',
    Icon: Feedback,
    text: 'Send feedback',
    showBorder: true,
  },
  {
    id: 'contact',
    Icon: Headphone,
    text: 'Contact us',
    showBorder: true,
  },
  {
    id: 'about',
    Icon: InfoColoredIcon,
    text: 'About',
    showBorder: false,
  },
] as const;

// Memoized components with proper typing
const UserProfile: React.FC<UserProfileProps> = React.memo(({ color }) => (
  <CardBorder style={globalStyles.flexDirectionRow}>
    <View style={gapStyles.gap_16}>
      <Text style={fontStyles.Maison_600_18PX_22LH}>{USER_DATA.name}</Text>
      <Text
        style={[
          fontStyles.Maison_400_14PX_16LH,
          { color: color.grey_7A7A7A } as TextStyle,
        ]}
      >
        {USER_DATA.email}
      </Text>
    </View>
  </CardBorder>
));

UserProfile.displayName = 'UserProfile';

const GoalsSection: React.FC<GoalsSectionProps> = React.memo(
  ({ color, onGoalPress }) => (
    <CardBorder>
      <CardTitle title="Manage goals" />
      {GOALS_DATA.map((goal: GoalItem) => (
        <CardValueContent
          key={goal.id}
          Icon={
            <goal.Icon height={25} width={25} color={color.primary_grenade} />
          }
          text={goal.text}
          value={goal.value}
          showBorder={goal.showBorder}
          onPress={() => onGoalPress(goal.id)}
        />
      ))}
    </CardBorder>
  ),
);

GoalsSection.displayName = 'GoalsSection';

const AppSettingsSection: React.FC<AppSettingsSectionProps> = React.memo(
  ({ color, onSettingPress }) => (
    <CardBorder>
      <CardTitle title="App Settings" />
      {APP_SETTINGS_DATA.map((setting: AppSettingItem) => (
        <CardValueContent
          key={setting.id}
          Icon={
            <setting.Icon
              height={20}
              width={20}
              color={color.primary_grenade}
            />
          }
          text={setting.text}
          value={setting.value}
          showBorder={setting.showBorder}
          onPress={() => onSettingPress(setting.id)}
        />
      ))}
    </CardBorder>
  ),
);

AppSettingsSection.displayName = 'AppSettingsSection';

const SupportSection: React.FC<SupportSectionProps> = React.memo(
  ({ color, onSupportPress }) => (
    <CardBorder>
      {SUPPORT_DATA.map((item: SupportItem) => (
        <CardValueContent
          key={item.id}
          Icon={
            <item.Icon height={20} width={20} color={color.primary_grenade} />
          }
          text={item.text}
          showBorder={item.showBorder}
          onPress={() => onSupportPress(item.id)}
        />
      ))}
    </CardBorder>
  ),
);

SupportSection.displayName = 'SupportSection';

const LogoutButton: React.FC<LogoutButtonProps> = React.memo(
  ({ color, onLogout }) => (
    <PrimaryBtn
      style={[
        {
          backgroundColor: color.transparent,
          borderColor: color.primary_grenade,
        } as ViewStyle,
        borderWidthStyles.bw2,
      ]}
      text="Logout"
      textStyle={[
        { color: color.primary_grenade } as TextStyle,
        fontStyles.Maison_600_18PX_22LH,
      ]}
      onPress={onLogout}
    />
  ),
);

LogoutButton.displayName = 'LogoutButton';

const SettingScreen: React.FC = () => {
  const color = useTheme() as Theme;
  const navigation = useNavigation<
    StackNavigationProp<
      {
        Login_: { tokenRefresher: string };
      },
      'Login_'
    >
  >();
  // Memoized styles with proper typing
  const containerStyle = useMemo(
    (): ViewStyle[] => [
      paddingStyles.p20,
      globalStyles.flex1,
      { backgroundColor: color.primary_cream },
    ],
    [color.primary_cream],
  );

  // Optimized event handlers with proper typing
  const handleGoalPress = useCallback<GoalPressHandler>((goalId: GoalId) => {
    console.log(`Goal pressed: ${goalId}`);
    // Add navigation or modal logic here
    switch (goalId) {
      case 'food':
        // Handle food goal navigation
        break;
      case 'steps':
        // Handle steps goal navigation
        break;
      case 'weight':
        // Handle weight goal navigation
        break;
      default:
        // TypeScript ensures this case is never reached
        const _exhaustiveCheck: never = goalId;
        return _exhaustiveCheck;
    }
  }, []);

  const handleSettingPress = useCallback<SettingPressHandler>(
    (settingId: SettingId) => {
      console.log(`Setting pressed: ${settingId}`);
      // Add navigation or modal logic here
      switch (settingId) {
        case 'coach':
          // Handle coach preference navigation
          break;
        case 'health':
          // Handle health data navigation
          break;
        default:
          // TypeScript ensures this case is never reached
          const _exhaustiveCheck: never = settingId;
          return _exhaustiveCheck;
      }
    },
    [],
  );

  const handleSupportPress = useCallback<SupportPressHandler>(
    (supportId: SupportId) => {
      console.log(`Support pressed: ${supportId}`);
      // Add navigation or modal logic here
      switch (supportId) {
        case 'feedback':
          // Handle feedback navigation
          break;
        case 'contact':
          // Handle contact us navigation
          break;
        case 'about':
          // Handle about navigation
          break;
        default:
          // TypeScript ensures this case is never reached
          const _exhaustiveCheck: never = supportId;
          return _exhaustiveCheck;
      }
    },
    [],
  );

  const handleLogout = useCallback<LogoutHandler>(async () => {
    try {
      await supabase.auth.signOut(); // Supabase sign out
      await GoogleSignin.revokeAccess(); // Revoke Google access
      await GoogleSignin.signOut(); // Google sign out
      navigation.navigate('Login_', { tokenRefresher: 'tokenRefresh' });
    } catch (error) {
      console.error('Error signing out:', error);
      Alert.alert('Error', 'Failed to sign out. Please try again.');
    }
  }, []);

  return (
    <ScrollView
      removeClippedSubviews
      showsVerticalScrollIndicator={false}
      bounces={false}
    >
      <View style={containerStyle}>
        <View style={gapStyles.gap_16}>
          <GreetingCard />

          <UserProfile color={color} />

          <GoalsSection color={color} onGoalPress={handleGoalPress} />

          <AppSettingsSection
            color={color}
            onSettingPress={handleSettingPress}
          />

          <SupportSection color={color} onSupportPress={handleSupportPress} />

          <LogoutButton color={color} onLogout={handleLogout} />
        </View>
      </View>
    </ScrollView>
  );
};

export default SettingScreen;
