import moment from 'moment';
import React, { useEffect } from 'react';
import { ScrollView, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import DailyTargetView from '../../../components/Account/DailyTargetView';
import DateHeader from '../../../components/Account/DateHeader';
import {
  DelicutBenefits,
  DelicutExpectNext,
} from '../../../components/Account/DelicutBenefits';
import FutureSuscriptionCard from '../../../components/Account/FutureSuscriptionCard';
import MealsView from '../../../components/Account/MealsView';
import ReferFriendCard from '../../../components/Account/ReferFriendCard';
import SkippedMealCard from '../../../components/Account/SkippedMealCard';
import SubscriptionEndCard from '../../../components/Account/SubscriptionEndCard';
import WeeklyMeals from '../../../components/Account/WeeklyMeals';
import { PrimaryBtn } from '../../../components/Buttons/Btns';
import { MealItemSkeletonLoader } from '../../../components/Card/AccountMealItemcard';
import { CommonCard } from '../../../components/Card/Card';
import KnowTragetGreenCard from '../../../components/Card/KnowTragetGreenCard';
import SkeletonLoader from '../../../components/SkeletonLoader';
import { getDeliveryByDate } from '../../../redux/action/deliveryActions';
import { getweeklyRecipes } from '../../../redux/action/recipesActions';
import { clanDeliveryByDate } from '../../../redux/slices/deliverySlice';
import { cleanweeklyRecipes } from '../../../redux/slices/recipesSlice';
import { RootState } from '../../../redux/store';
import { useTheme } from '../../../theme';
import { RFont } from '../../../theme/fonts';
import {
  gapStyles,
  paddingStyles,
} from '../../../theme/styles/commonMarginPadding';
import {
  borderWidthStyles,
  globalStyles,
} from '../../../theme/styles/globalStyles';
import { daysPending, sumVariantsField } from '../../../utils/functions';
const Account = () => {
  const colors = useTheme();
  const { authUser } = useSelector((state: RootState) => state.auth);
  const { subscriptionDetails } = useSelector(
    (state: RootState) => state.subscription,
  );
  const { deliverybyDate, selectedDate, listLoader, loader } = useSelector(
    (state: RootState) => state.delivery,
  );
  const dispatch = useDispatch();
  useEffect(() => {
    if (authUser?._id) dispatch(getDeliveryByDate(selectedDate, authUser?._id));
  }, [authUser?._id, dispatch, selectedDate]);
  let macros = subscriptionDetails?.find(
    (e) => e._id === deliverybyDate?.subscription_id,
  )?.macros;
  let pending = daysPending(subscriptionDetails[0]?.end_date || '');
  useEffect(() => {
    dispatch(getweeklyRecipes());
    return () => {
      dispatch(clanDeliveryByDate());
      dispatch(cleanweeklyRecipes());
    };
  }, [dispatch]);
  return (
    <View
      style={[globalStyles.flex1, { backgroundColor: colors?.primary_cream }]}
    >
      <DateHeader />
      {listLoader.find((e) => e == 'delivery/getDeliveryByDate') &&
      !deliverybyDate ? (
        <View style={[paddingStyles.px16, paddingStyles.py24]}>
          <CommonCard style={gapStyles.gap_20}>
            <SkeletonLoader height={RFont(28)} />
            <MealItemSkeletonLoader />
            <MealItemSkeletonLoader />
          </CommonCard>
        </View>
      ) : (
        <ScrollView
          contentContainerStyle={[
            paddingStyles.px16,
            paddingStyles.py24,
            gapStyles.gap_20,
          ]}
        >
          {subscriptionDetails?.length == 1 && (
            <SubscriptionEndCard
              isExpired={moment(subscriptionDetails?.[0]?.end_date).isBefore(
                moment(),
              )}
            />
          )}
          {subscriptionDetails.length === 1 && pending < 0 && (
            <React.Fragment>
              <WeeklyMeals />
              <DelicutBenefits />
            </React.Fragment>
          )}
          {deliverybyDate?.is_delivery_freezed ? null : deliverybyDate &&
            macros ? (
            <DailyTargetView
              subscription_id={deliverybyDate?.subscription_id}
              totalKcal={macros?.calories}
              consumeKcal={sumVariantsField(deliverybyDate, 'kcal')}
              totalProten={macros?.protein}
              consumeProten={sumVariantsField(deliverybyDate, 'protein')}
              totalCarbs={macros?.carbs}
              consumeCarbs={sumVariantsField(deliverybyDate, 'carb')}
              totalFat={macros?.fat}
              consumeFat={sumVariantsField(deliverybyDate, 'fat')}
            />
          ) : (
            loader == false &&
            subscriptionDetails.length > 0 &&
            !subscriptionDetails[0]?.macros && (
              <KnowTragetGreenCard
                title={'Know your daily target?'}
                desc={
                  'Add your age, height, weight & gender to get your recommended calorie intake.'
                }
                titleStyle={[globalStyles?.letterSpacingN1]}
              >
                <View style={[globalStyles.flexDirectionRow, gapStyles.gap_12]}>
                  <PrimaryBtn
                    text="Not now"
                    style={[
                      globalStyles.flex1,
                      borderWidthStyles.bw1,
                      {
                        backgroundColor: colors?.transparent,
                        borderColor: colors?.primary_spinach_10_p,
                      },
                    ]}
                    textStyle={{ color: colors?.primary_spinach }}
                  />
                  <PrimaryBtn
                    text="Let’s calculate"
                    style={[
                      globalStyles.flex1,
                      { backgroundColor: colors?.primary_spinach },
                    ]}
                  />
                </View>
              </KnowTragetGreenCard>
            )
          )}
          {deliverybyDate?.is_delivery_freezed ? (
            <SkippedMealCard
              item={deliverybyDate}
              onSuccessResume={() => {
                if (authUser?._id)
                  dispatch(getDeliveryByDate(selectedDate, authUser?._id));
              }}
            />
          ) : (
            <MealsView />
          )}
          {subscriptionDetails?.length > 0 &&
            moment()
              .tz('Asia/Dubai')
              .isAfter(
                moment.tz(
                  moment(
                    subscriptionDetails?.[subscriptionDetails?.length - 1]
                      ?.end_date,
                  ),
                  'Asia/Dubai',
                ),
              ) && (
              <React.Fragment>
                <FutureSuscriptionCard />
                <DelicutExpectNext />
              </React.Fragment>
            )}
          <ReferFriendCard />
        </ScrollView>
      )}
    </View>
  );
};

export default Account;
