import { AddEditAddressPayload } from 'actions';
import { FC, useEffect } from 'react';
import { useFormContext } from 'react-hook-form';
import { ScrollView, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { ScreenProps } from '../../../../@types/navigation';
import Address from '../../../components/AddNewAddress/Address';
import AddSpecialInstructions from '../../../components/AddNewAddress/AddSpecialInstructions';
import DeliveryLabel from '../../../components/AddNewAddress/DeliveryLabel';
import BottomFloatingCard from '../../../components/Card/BottomFloatingCard';
import { ScreenHeader } from '../../../components/ScreenHeader/ScreenHeader';
import {
  StoreUserAddress,
  updateUserAddressById,
} from '../../../redux/action/authAction';
import { clearddressById } from '../../../redux/slices/authSlice';
import { RootState } from '../../../redux/store';
import { useTheme } from '../../../theme';
import {
  gapStyles,
  marginStyles,
} from '../../../theme/styles/commonMarginPadding';
import { globalStyles } from '../../../theme/styles/globalStyles';
import { mealPlanStyles } from '../../../theme/styles/mealPlanStyles';

const AddNewAddress: FC<ScreenProps<'AddNewAddress'>> = ({ navigation }) => {
  const colors = useTheme();
  const { addressByID, translation } = useSelector(
    (state: RootState) => state?.auth,
  );
  const dispatch = useDispatch();
  const { handleSubmit } = useFormContext<AddEditAddressPayload>();
  const onSubmit = (data: AddEditAddressPayload) => {
    if (addressByID?._id) {
      dispatch(
        updateUserAddressById(addressByID?._id, data, (res) => {
          console.log('updateUserAddressById', res);
        }),
      );
    } else {
      dispatch(
        StoreUserAddress(data, (res) => {
          console.log('StoreUserAddress', res);
        }),
      );
    }
  };
  useEffect(() => {
    return () => {
      dispatch(clearddressById());
    };
  }, [dispatch]);
  return (
    <View
      style={[globalStyles.flex1, { backgroundColor: colors?.primary_cream }]}
    >
      <ScrollView showsVerticalScrollIndicator={false} persistentScrollbar>
        <View
          style={[
            mealPlanStyles.container,
            marginStyles.mb_90,
            marginStyles.mt_0,
            gapStyles.gap_16,
          ]}
        >
          <ScreenHeader
            title={translation?.ADD_NEW_ADDRESS}
            isBack={navigation.canGoBack()}
            onPressBack={() => navigation.goBack()}
          />
          <DeliveryLabel />
          <Address />
          <AddSpecialInstructions />
        </View>
      </ScrollView>
      <BottomFloatingCard
        btnText="Add address"
        onPress={handleSubmit(onSubmit)}
      />
    </View>
  );
};

export default AddNewAddress;
