import { useState } from 'react';
import { <PERSON><PERSON>View, Text, View } from 'react-native';
import { useSelector } from 'react-redux';
import { PlusOrangeIcon } from '../../../assets/images';
import {
  MultiSelectPillGroup,
  PrimaryBtn,
} from '../../../components/Buttons/Btns';
import BottomFloatingCard from '../../../components/Card/BottomFloatingCard';
import { CommonCard } from '../../../components/Card/Card';
import SelectInputField from '../../../components/SelectInputField/SelectInputField';
import { TextInputField } from '../../../components/TextInputField/TextInputField';
import { RootState } from '../../../redux/store';
import { useTheme } from '../../../theme';
import { lightTheme } from '../../../theme/colors';
import { fontStyles } from '../../../theme/fonts';
import { buttonStyles } from '../../../theme/styles/buttonStyles';
import {
  gapStyles,
  marginStyles,
  paddingStyles,
} from '../../../theme/styles/commonMarginPadding';
import { globalStyles } from '../../../theme/styles/globalStyles';
import { avilableSlotsData } from '../../../utils/global';

const DeliveryDetails = () => {
  const { translation } = useSelector((state: RootState) => state?.auth);
  const [selectedValues, setSelectedValues] = useState('');
  const colors = useTheme();
  return (
    <View
      style={[
        globalStyles.flex1,
        {
          backgroundColor: colors?.primary_cream,
        },
      ]}
    >
      <ScrollView>
        <View style={[paddingStyles.p16, marginStyles.mb_90]}>
          <CommonCard
            title="Enter delivery details"
            titleStyle={[
              fontStyles.Maison_600_32PX_40LH,
              { color: colors?.grey_900 },
            ]}
            style={[paddingStyles.px20, paddingStyles.py32]}
          >
            <View style={[gapStyles.gap_12, marginStyles.mt_32]}>
              <Text
                style={[
                  fontStyles.Maison_500_20PX_28LH,
                  { color: colors?.grey_900 },
                ]}
              >
                Address
              </Text>

              <TextInputField placeholder="Door number" />

              <TextInputField placeholder="Building/Community name" />
              <View style={[gapStyles.gap_12, globalStyles.row]}>
                <SelectInputField
                  value={undefined}
                  placeholder="City"
                  onChange={() => null}
                  containerStyle={globalStyles.flex1}
                />

                <SelectInputField
                  value={undefined}
                  placeholder="Area"
                  onChange={() => null}
                  containerStyle={globalStyles.flex1}
                />
              </View>
            </View>
            <View style={[gapStyles.gap_12, marginStyles.mt_24]}>
              <Text
                style={[
                  fontStyles.Maison_500_20PX_28LH,
                  { color: colors?.grey_900 },
                ]}
              >
                Selected delivery slot
              </Text>
              <MultiSelectPillGroup
                options={avilableSlotsData.map((itm) => ({
                  label: itm.value,
                }))}
                selectedValues={selectedValues as string}
                onSelect={(val) => {
                  setSelectedValues(val);
                }}
                isScrollable
              />
            </View>
            <PrimaryBtn
              leftIcon={<PlusOrangeIcon />}
              textStyle={[
                fontStyles?.Maison_600_18PX_21_6LH,
                { color: lightTheme?.primary_grenade },
              ]}
              text={translation?.ADD_SPECIAL_INSTRUCTIONS}
              style={[
                buttonStyles?.BorderLessBtn,
                marginStyles?.mt_24,
                globalStyles.justifyContentFlexStart,
                paddingStyles.px12,
                paddingStyles.py8,
              ]}
            />
          </CommonCard>
        </View>
      </ScrollView>
      <BottomFloatingCard btnText="Confirm Delivery details" />
    </View>
  );
};
export default DeliveryDetails;
