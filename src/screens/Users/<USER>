import {
  KeyValueProps,
  MealType,
  Portion,
  Size,
  SubscriptionPriceProps,
} from 'cart-slice';
import { useFormik } from 'formik';
import { cartFormProps } from 'forms';
import { intersection } from 'lodash';
import React, { useCallback, useEffect } from 'react';
import { ScrollView, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { BuildPlanImg, CheckIcon } from '../../assets/images';
import DeliverySlot from '../../components/BuildYourPlan/DeliverySlot';
import SelectCalorie from '../../components/BuildYourPlan/SelectCalorie';
import SelectDailyMeal from '../../components/BuildYourPlan/SelectDailyMeal';
import SelectDurationPlan from '../../components/BuildYourPlan/SelectDurationPlan';
import SelectIngredients from '../../components/BuildYourPlan/SelectIngredients';
import SelectPreferredDiet from '../../components/BuildYourPlan/SelectPreferredDiet';
import StartYourPlan from '../../components/BuildYourPlan/StartYourPlan';
import CallToActionBanner from '../../components/Card/CallToActionBanner';
import WhyChooseUs from '../../components/Card/WhyChooseUs';
import {
  getDeliveryArea,
  getDeliverySlot,
} from '../../redux/action/appActions';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontStyles } from '../../theme/fonts';
import { globalStyles } from '../../theme/styles/globalStyles';
import { mealPlanStyles } from '../../theme/styles/mealPlanStyles';
import {
  arraysAreEqual,
  mergeArrays,
  removeDuplicates,
} from '../../utils/functions';
import { meal, snacks, WhyDelicutData } from '../../utils/global';

const MealPlan = () => {
  const dispatch = useDispatch();
  const colors = useTheme();
  const { cartDetails, nutritionPlan } = useSelector(
    (state: RootState) => state?.cart,
  );
  const { translation } = useSelector((state: RootState) => state?.auth);
  const { masterIng, masterRmsData } = useSelector(
    (state: RootState) => state.app,
  );
  const { priceData } = useSelector((state: RootState) => state?.subscription);
  const cart_item = cartDetails?.cart_item?.[0];

  const initialValues: cartFormProps = {
    is_recommended: false,
    avoid_category: cart_item?.avoid_category || [],
    avoid_ingredients: cart_item?.avoid_ingredients || [],
    body_metrics: cart_item?.body_metrics || {},
    delivery_details: {
      city: cart_item?.delivery_details?.city || '',
      province: cart_item?.delivery_details?.province || '',
      slot: cart_item?.delivery_details?.slot || '',
      instruction: cart_item?.delivery_details?.instruction || [],
    },
    delivery_start_date: cart_item?.delivery_start_date?.toString() || '',
    is_vegetarian: cart_item?.is_vegetarian || false,
    delivery_days: parseInt(cart_item?.delivery_days || '5'),
    no_of_breakfast: cart_item?.no_of_breakfast || 0,
    no_of_meals: cart_item?.no_of_meals || 2,
    no_of_snacks: cart_item?.no_of_snacks || 0,
    per_day_price: parseFloat(cart_item?.per_day_price || '0'),
    plan_duration_in_days: parseInt(cart_item?.plan_duration_in_days || '20'),
    price: cart_item?.price || 0,
    protein_category: cart_item?.protein_category || 'low',
    selected_meal: cart_item?.selected_meal || ['lunch', 'dinner'],
    kcal: cart_item?.selected_meal_type?.[0]?.kcal || '',
    kcal_range: cart_item?.selected_meal_type?.[0]?.kcal_range || 'Medium',
    qty: 1,
    dayArray: [],
  };
  const cartFormik = useFormik<cartFormProps>({
    enableReinitialize: true,
    initialValues: initialValues,
    onSubmit: () => {},
  });
  const { values, setValues } = cartFormik;

  const macro_guideline = masterRmsData
    ?.find((e) => e.key === 'macro_guideline')
    ?.value?.find((e) => e.key == values.protein_category)
    ?.value as unknown as MealType[];

  const totalKcalInMeal = useCallback(
    (size: string) => {
      function generateMealPlan() {
        const mealPlan = [];
        for (let i = 0; i < values.no_of_meals; i++) {
          mealPlan.push('Meal');
        }
        for (let i = 0; i < values.no_of_breakfast; i++) {
          mealPlan.push('Breakfast');
        }
        for (let i = 0; i < values.no_of_snacks; i++) {
          mealPlan.push('Snack');
        }
        return mealPlan;
      }
      const names = generateMealPlan();
      let totalKcalStart = 0;
      let totalKcalEnd = 0;
      let portioning: Portion[] = [];

      names.forEach((name) => {
        const item = macro_guideline?.find((d) => d.name === name);
        const sizeItem = item?.sizes?.find((e: Size) =>
          name !== 'Meal' ? 'standard' : e.size === size?.toLowerCase(),
        );
        if (item) {
          totalKcalStart += sizeItem?.kcalRange?.start as number;
          totalKcalEnd += sizeItem?.kcalRange?.end as number;
          portioning = item?.portioning;
        }
      });
      return {
        totalKcalStart,
        totalKcalEnd,
        portioning,
      };
    },
    [
      macro_guideline,
      values.no_of_breakfast,
      values.no_of_meals,
      values.no_of_snacks,
    ],
  );
  const getPriceObj = useCallback(
    (kcal_range: string, no_of_meals: string, protein_category: string) => {
      return priceData?.subscription_price?.find(
        (e: SubscriptionPriceProps) =>
          e?.meal_type == kcal_range &&
          e?.meal_tag == 'None' &&
          e?.number_of_meal == no_of_meals &&
          e?.protein_category === protein_category,
      );
    },
    [priceData?.subscription_price],
  );

  const getTotalPrice = useCallback(
    (
      selectedPrice: number,
      no_of_breakfast: number,
      no_of_snacks: number,
      plan_duration_in_days: number,
    ) => {
      return (
        (selectedPrice || 0) +
        (no_of_breakfast == 1
          ? parseFloat(priceData?.subscription_fix_price?.breakfast || '0') *
            plan_duration_in_days
          : 0) +
        (no_of_snacks == 1
          ? values?.selected_meal?.find((e) => e === 'morning_snack')
            ? parseFloat(
                priceData?.subscription_fix_price?.morning_snack || '0',
              ) * plan_duration_in_days
            : parseFloat(
                priceData?.subscription_fix_price?.evening_snack || '0',
              ) * plan_duration_in_days
          : no_of_snacks == 2
            ? (parseFloat(
                priceData?.subscription_fix_price?.morning_snack || '0',
              ) +
                parseFloat(
                  priceData?.subscription_fix_price?.evening_snack || '0',
                )) *
              plan_duration_in_days
            : 0)
      );
    },
    [
      priceData?.subscription_fix_price?.breakfast,
      priceData?.subscription_fix_price?.evening_snack,
      priceData?.subscription_fix_price?.morning_snack,
      values?.selected_meal,
    ],
  );

  const cartOnchange = useCallback(
    (
      field: keyof KeyValueProps | Partial<KeyValueProps>,
      value?: KeyValueProps[keyof KeyValueProps],
    ) => {
      let newIn = { ...values };

      if (typeof field === 'string' && value !== undefined) {
        newIn = {
          ...newIn,
          [field]: value,
        };
      } else if (typeof field === 'object' && value === undefined) {
        newIn = {
          ...newIn,
          ...field,
        };
      }

      const no_of_meals = intersection(meal, newIn.selected_meal).length;
      const no_of_breakfast = intersection(
        ['breakfast'],
        newIn.selected_meal,
      ).length;
      const no_of_snacks = intersection(snacks, newIn.selected_meal).length;

      const obj = getPriceObj(
        newIn?.kcal_range,
        no_of_meals.toString(),
        newIn?.protein_category,
      );

      if (obj?._id) {
        const dayArray =
          priceData?.subscription_fix_price?.durations?.find(
            (e: { week_day: number }) => e.week_day == newIn.delivery_days,
          )?.days || [];

        const days = dayArray?.includes(newIn.plan_duration_in_days)
          ? newIn.plan_duration_in_days
          : dayArray?.[dayArray.length - 1] || 0;

        const selectedPrice = parseFloat(
          obj?.[('day_' + days) as keyof SubscriptionPriceProps] || '0',
        );
        const totalPrice = getTotalPrice(
          selectedPrice,
          no_of_breakfast,
          no_of_snacks,
          days,
        );

        setValues(
          {
            is_recommended:
              arraysAreEqual(
                newIn?.selected_meal || [],
                nutritionPlan?.mealRecommendations?.[0]?.meal_parts || [],
              ) &&
              newIn?.protein_category ==
                nutritionPlan?.mealRecommendations?.[0]?.diet_type &&
              nutritionPlan?.mealRecommendations?.[0]?.size ===
                newIn?.kcal_range,

            avoid_category: newIn?.avoid_category,
            avoid_ingredients: newIn?.avoid_ingredients,
            body_metrics: newIn?.body_metrics,
            delivery_details: newIn?.delivery_details,
            delivery_start_date: newIn?.delivery_start_date,
            is_vegetarian: newIn?.is_vegetarian,
            delivery_days: newIn?.delivery_days,
            no_of_breakfast,
            no_of_meals,
            no_of_snacks,
            per_day_price: totalPrice / days,
            plan_duration_in_days: days,
            price: totalPrice,
            protein_category: newIn?.protein_category,
            selected_meal: newIn?.selected_meal,
            kcal: `${totalKcalInMeal(newIn.kcal_range)?.totalKcalStart} - ${
              totalKcalInMeal(newIn.kcal_range)?.totalKcalEnd
            } Kcal`,
            kcal_range: newIn?.kcal_range,
            qty: 1,
            dayArray: dayArray,
          },
          true,
        );
      } else {
        console.error('else');
      }
    },
    [
      getPriceObj,
      getTotalPrice,
      nutritionPlan?.mealRecommendations,
      priceData?.subscription_fix_price?.durations,
      setValues,
      totalKcalInMeal,
      values,
    ],
  );
  const addAllNonVegIng = () => {
    const list_Cat = masterIng?.category
      ?.filter((c) => c.is_vegetarian === false && c.is_live)
      .map((e) => e.category_name);
    const all_cat = mergeArrays(list_Cat as string[], values?.avoid_category);
    const All_Cat_Ing = Object.assign([], [] as Array<string>);
    for (let index = 0; index < all_cat.length; index++) {
      const element = all_cat[index];
      const newCatIng = masterIng?.ingredient
        ?.filter((e) => e.category_name_ref.some((r) => element.includes(r)))
        .map((e) => e.ingredient);
      if (newCatIng) {
        for (let index = 0; index < newCatIng.length; index++) {
          All_Cat_Ing.push(newCatIng[index]);
        }
      }
    }
    return {
      avoid_category: all_cat,
      avoid_ingredients: removeDuplicates(
        mergeArrays(All_Cat_Ing, values?.avoid_ingredients),
      ),
    };
  };

  const removeAllNonVegIng = () => {
    const list_Cat = masterIng?.category
      ?.filter((c) => c.is_vegetarian === false && c.is_live)
      .map((e) => e.category_name) as string[];
    const all_cat = values?.avoid_category?.filter(
      (item) => !list_Cat.includes(item),
    );
    const All_Cat_Ing = Object.assign([], [] as Array<string>);
    for (let index = 0; index < list_Cat.length; index++) {
      const element = list_Cat[index];
      const newCatIng = masterIng?.ingredient
        ?.filter((e) => e.category_name_ref.some((r) => element.includes(r)))
        .map((e) => e.ingredient);
      if (newCatIng) {
        for (let index = 0; index < newCatIng.length; index++) {
          All_Cat_Ing.push(newCatIng[index]);
        }
      }
    }
    return {
      avoid_category: all_cat,
      avoid_ingredients: removeDuplicates(
        values?.avoid_ingredients.filter((item) => !All_Cat_Ing.includes(item)),
      ),
    };
  };

  useEffect(() => {
    if (
      values.dayArray.length == 0 &&
      nutritionPlan?.mealRecommendations?.[0]
    ) {
      cartOnchange(
        {
          is_recommended: nutritionPlan?.mealRecommendations?.[0]
            ? true
            : false,
          body_metrics: nutritionPlan
            ? {
                ...nutritionPlan?.body_metrics,
                target_calories: nutritionPlan?.target_calories,
                target_macros: nutritionPlan?.target_macros,
              }
            : cart_item?.body_metrics || {},
          protein_category:
            nutritionPlan?.mealRecommendations?.[0]?.diet_type ||
            cart_item?.protein_category ||
            'low',
          selected_meal: nutritionPlan?.mealRecommendations?.[0]?.meal_parts ||
            cart_item?.selected_meal || ['lunch', 'dinner'],
          kcal_range:
            nutritionPlan?.mealRecommendations?.[0]?.size ||
            cart_item?.selected_meal_type?.[0]?.kcal_range ||
            'Medium',
          kcal: `${nutritionPlan?.mealRecommendations?.[0]?.meal_data?.kcalRange[0]} - ${nutritionPlan?.mealRecommendations?.[0]?.meal_data?.kcalRange[1]} Kcal`,
        },
        undefined,
      );
    } else if (values.dayArray.length == 0) {
      cartOnchange('delivery_days', cart_item?.delivery_days || 5);
    }
  }, [
    values.dayArray.length,
    cart_item?.delivery_days,
    cartOnchange,
    nutritionPlan,
    cart_item?.body_metrics,
    cart_item?.protein_category,
    cart_item?.selected_meal,
    cart_item?.selected_meal_type,
  ]);

  useEffect(() => {
    if (cart_item?.delivery_details?.city && priceData?.subscription_price) {
      dispatch(getDeliveryArea(cart_item?.delivery_details?.city));
      if (cart_item?.delivery_details?.province) {
        dispatch(
          getDeliverySlot({
            city: cart_item?.delivery_details?.city,
            area: cart_item?.delivery_details?.province,
          }),
        );
      }
    }
  }, [
    cart_item?.delivery_details?.city,
    cart_item?.delivery_details?.province,
    dispatch,
    priceData?.subscription_price,
  ]);
  return (
    <View
      style={[globalStyles.flex1, { backgroundColor: colors?.primary_cream }]}
    >
      <ScrollView showsVerticalScrollIndicator={false} persistentScrollbar>
        <View style={mealPlanStyles.container}>
          <CallToActionBanner
            title={translation?.BUILD_YOUR_PLAN}
            titleStyle={[
              fontStyles.Maison_600_32PX_40LH,
              { color: colors?.neutral_white },
            ]}
            imageSource={BuildPlanImg}
          />
          <SelectPreferredDiet
            addAllNonVegIng={addAllNonVegIng}
            removeAllNonVegIng={removeAllNonVegIng}
            cartFormik={cartFormik}
            cartOnchange={cartOnchange}
          />
          <SelectDailyMeal
            cartFormik={cartFormik}
            cartOnchange={cartOnchange}
          />
          <SelectIngredients />
          <SelectCalorie
            totalKcalInMeal={totalKcalInMeal}
            cartFormik={cartFormik}
            cartOnchange={cartOnchange}
          />
          <SelectDurationPlan />
          <StartYourPlan />
          <DeliverySlot />
          <WhyChooseUs
            title={translation?.WHAT_MAKES_DELICUT_BETTER}
            data={WhyDelicutData}
            icon={<CheckIcon />}
          />
        </View>
      </ScrollView>
    </View>
  );
};

export default MealPlan;
