import React, { useCallback, useState } from 'react';
import { StyleSheet, View } from 'react-native';
import DatePicker from 'react-native-date-picker';
import { GorhomBottomSheet } from '../../components/BottomSheet/BottomSheet';
import { PrimaryBtn } from '../../components/Buttons/Btns';

interface Props {
  open: boolean;
  onClose: (date: Date | null) => void; // onClose will return the selected date or null
}

const Dateandtime: React.FC<Props> = ({ open, onClose }) => {
  const currentDate = new Date();
  const [date, setDate] = useState<Date>(currentDate);
  const handleDateChange = useCallback((selectedDate: Date) => {
    setDate(selectedDate);
  }, []);
  return (
    <GorhomBottomSheet
      sheetOpen={open}
      title="Calories & macros"
      closeOnBackdrop
      closeOnPressBack
      sheetClose={() => onClose(null)}
    >
      <View style={styles.container}>
        <DatePicker
          date={date}
          onDateChange={handleDateChange}
          theme="light"
          style={styles.datePicker}
        />
        <PrimaryBtn
          text="Save"
          onPress={() => onClose(date)}
          style={styles.saveButton}
          textStyle={styles.saveButtonText}
          disabled={date.getTime() === currentDate.getTime()}
        />
      </View>
    </GorhomBottomSheet>
  );
};

// Styles for the component
const styles = StyleSheet.create({
  container: {
    padding: 10,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 10,
    textAlign: 'center',
  },
  datePicker: {
    alignSelf: 'center',
    // marginBottom: 20,
  },
  saveButton: {
    marginTop: 12,
    marginHorizontal: 8,
    flex: 1,
  },
  saveButtonText: {
    fontSize: 16,
    lineHeight: 19,
    fontWeight: '600',
  },
});
export default Dateandtime;
