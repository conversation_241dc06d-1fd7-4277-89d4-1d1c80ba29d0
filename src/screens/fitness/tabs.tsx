import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import React from 'react';
import AntDesign from 'react-native-vector-icons/AntDesign';
import {
  default as Icon,
  default as MaterialIcons,
} from 'react-native-vector-icons/MaterialIcons';
import Camera from './camera';
import Chat from './chat';
import MainScreen from './mainscreen';
import Profile from './profile';

const Tab = createBottomTabNavigator();
export default function Tabs() {
  return (
    <Tab.Navigator>
      <Tab.Screen
        name="Home"
        options={{
          headerShown: true,
          tabBarIcon: ({ color, size }) => (
            <Icon name="home" size={size} color={color} />
          ),
        }}
        component={MainScreen}
      />
      <Tab.Screen
        name="Capture"
        options={{
          headerShown: true,
          tabBarIcon: ({ color, size }) => (
            <Icon name="camera" size={size} color={color} />
          ),
        }}
        component={Camera}
      />
      <Tab.Screen
        name="Coach"
        options={{
          headerShown: true,
          tabBarIcon: ({ color, size }) => (
            <MaterialIcons name="assistant" size={size} color={color} />
          ),
        }}
        component={Chat}
      />
      <Tab.Screen
        name="Profile"
        options={{
          headerShown: true,
          tabBarIcon: ({ color, size }) => (
            <AntDesign name="user" size={size} color={color} />
          ),
        }}
        component={Profile}
      />

      {/* <Tab.Screen
        name="Logs"
        options={{
          headerShown: true,
          tabBarIcon: ({ color, size }) => (
            <Icon name="list" size={size} color={color} />
            // You can use any icon name you prefer here
          ),
        }}
        component={Logs}
      /> */}
    </Tab.Navigator>
  );
}
