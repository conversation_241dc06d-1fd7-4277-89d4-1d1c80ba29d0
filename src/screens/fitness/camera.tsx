import { NavigationProp, useNavigation } from '@react-navigation/native';
import React, { useEffect, useRef, useState } from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import AntDesign from 'react-native-vector-icons/AntDesign';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import {
  Camera,
  useCameraDevice,
  useCameraPermission,
} from 'react-native-vision-camera';
import { useDispatch, useSelector } from 'react-redux';
import { FitnessRootStackParamList } from '../../../@types/fitnessnavigator';
import Gallery from '../../assets/images/svgs/gallery.svg';
import Meals from '../../assets/images/svgs/meals.svg';
import { useUserId } from '../../components/FitnessComponent/hooks/useUserId';
import { analyzeFood } from '../../redux/action/appActions';
import { RootState } from '../../redux/store';
import { fontStyles } from '../../theme/fonts';

const CameraScreen = () => {
  const dispatch = useDispatch();
  const navigation = useNavigation<NavigationProp<FitnessRootStackParamList>>();
  const cameraRef = useRef<Camera>(null);
  const { hasPermission, requestPermission } = useCameraPermission();
  const device = useCameraDevice('back');
  const [selectedMeal, setSelectedMeal] = useState('Meal');
  const { loading: userFetchLoading, userId } = useUserId();

  useEffect(() => {
    requestPermission();
  }, []);

  const handleTakePictureAndAnalyze = async () => {
    console.log('📸 Button pressed');

    if (!cameraRef.current) {
      console.warn('Camera not ready');
      return;
    }

    try {
      const photo = await cameraRef.current.takePhoto({
        flash: 'off',
      });

      console.log('📸 Photo taken:', photo);

      const file = {
        uri: `file://${photo.path}`,
        name: 'photo.jpg',
        type: 'image/jpeg',
      };

      navigation.navigate('Mealdata', {
        photoUri: file.uri,
      });

      dispatch(
        analyzeFood({
          file,
          user_id: userId,
          analysis_mode: 'enhanced',
        }),
      );
    } catch (error) {
      console.error('Capture or API call failed:', error);
    }
  };

  const result = useSelector((state: RootState) => state.app.analyzeFoodResult);

  useEffect(() => {
    if (result) {
      console.log('🍱 Food Analysis Result:', result);
    }
  }, [result]);

  const isCameraReady = hasPermission && device;

  return (
    <View style={styles.container}>
      {isCameraReady ? (
        <>
          <Camera
            ref={cameraRef}
            style={StyleSheet.absoluteFill}
            device={device}
            isActive={true}
            photo={true}
          />
        </>
      ) : (
        <Text style={styles.errorText}>Loading camera...</Text>
      )}
      <View style={styles.topBarContent}>
        <TouchableOpacity onPress={() => console.log('Info')}>
          <MaterialIcons name="info-outline" size={22} color="white" />
        </TouchableOpacity>

        <Text style={[fontStyles?.Maison_400_14PX_18LH, styles.topBarText]}>
          Place your meal inside the circle
        </Text>

        <TouchableOpacity onPress={() => navigation.goBack()}>
          <AntDesign name="close" size={20} color="white" />
        </TouchableOpacity>
      </View>
      <View style={styles.circleOverlay} />
      <View style={styles.mealRow}>
        <View style={styles.mealColumn}>
          <TouchableOpacity
            onPress={() => setSelectedMeal('Breakfast')}
            style={styles.mealItem}
          >
            <Meals height={20} width={20} />
            <Text
              style={[
                fontStyles?.Maison_500_16PX_22LH,
                styles.mealText,
                selectedMeal === 'Breakfast' && styles.mealTextSelected,
              ]}
            >
              Breakfast
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.mealColumn}>
          <TouchableOpacity
            onPress={() => setSelectedMeal('Meal')}
            style={styles.mealItem}
          >
            <Meals height={20} width={20} />
            <Text
              style={[
                fontStyles?.Maison_500_16PX_22LH,
                styles.mealText,
                selectedMeal === 'Meal' && styles.mealTextSelected,
              ]}
            >
              Meal
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.mealColumn}>
          <TouchableOpacity
            onPress={() => setSelectedMeal('Snack')}
            style={styles.mealItem}
          >
            <Meals height={20} width={20} />
            <Text
              style={[
                fontStyles?.Maison_500_16PX_22LH,
                styles.mealText,
                selectedMeal === 'Snack' && styles.mealTextSelected,
              ]}
            >
              Snack
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.bottomControls}>
        <View style={styles.mealColumn}>
          <TouchableOpacity
            onPress={() => console.log('Gallery')}
            style={styles.controlItem}
          >
            <Gallery height={24} width={20} />
            <Text
              style={[styles.controlText, fontStyles?.Maison_500_12PX_16LH]}
            >
              Gallery
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.mealColumn}>
          <TouchableOpacity
            onPress={handleTakePictureAndAnalyze}
            style={styles.shutterOuter}
          >
            <View style={styles.shutterInner} />
          </TouchableOpacity>
        </View>

        <View style={styles.mealColumn}>
          <TouchableOpacity
            onPress={() => console.log('Type')}
            style={styles.controlItem}
          >
            <AntDesign name="search1" size={24} color="white" />
            <Text
              style={[styles.controlText, fontStyles?.Maison_500_12PX_16LH]}
            >
              Type your meal
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },
  errorText: {
    flex: 1,
    textAlign: 'center',
    textAlignVertical: 'center',
    color: 'white',
  },
  topBar: {
    position: 'absolute',
    top: 0,
    width: '100%',
    backgroundColor: 'rgba(0,0,0,0.4)',
  },
  topBarContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 25,
    alignItems: 'center',
    marginTop: 20,
  },
  topBarText: {
    color: 'white',
    fontSize: 14,
  },
  circleOverlay: {
    alignSelf: 'center',
    width: 360,
    height: 360,
    borderRadius: 180,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.6)',
    position: 'absolute',
    top: '20%',
  },
  shutterWrapper: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  mealTypeRow: {
    position: 'absolute',
    bottom: 160,
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  mealRow: {
    position: 'absolute',
    bottom: 180,
    flexDirection: 'row',
    width: '100%',
  },
  bottomControls: {
    position: 'absolute',
    bottom: 0,
    width: '100%',
    backgroundColor: 'black',
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 40,
  },

  mealColumn: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  mealItem: {
    alignItems: 'center',
  },
  mealText: {
    color: 'white',
    fontSize: 12,
    marginTop: 4,
  },
  mealTextSelected: {
    color: '#f44336',
    fontWeight: 'bold',
  },
  controlItem: {
    alignItems: 'center',
  },
  controlText: {
    color: 'white',
    marginTop: 4,
  },
  shutterOuter: {
    width: 65,
    height: 65,
    borderRadius: 35,
    borderWidth: 0.7,
    borderColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
  },
  shutterInner: {
    width: 57,
    height: 57,
    borderRadius: 28,
    backgroundColor: 'white',
  },
});

export default CameraScreen;
