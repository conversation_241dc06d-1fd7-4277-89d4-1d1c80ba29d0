// ✅ Updated Chat.tsx (with history integration + scroll + text input support + API call on send)
import React, { useEffect, useLayoutEffect, useRef, useState } from 'react';
import {
  ActivityIndicator,
  FlatList,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import ChatUI from '../../components/FitnessComponent/chatui';
import { supabase } from './supabaseClient';
const Chat = () => {
  const [userData, setUserData] = useState<any>(null);
  const [coachPersonas, setCoachPersonas] = useState<any[]>([]);
  const [userHasCoach, setUserHasCoach] = useState<boolean | null>(null);
  const [initialMessage, setInitialMessage] = useState<string | null>(null);
  const [messageOptions, setMessageOptions] = useState<string[]>([]);
  const [chatStarted, setChatStarted] = useState<boolean>(false);
  const [chatMessages, setChatMessages] = useState<
    { sender: 'user' | 'ai'; text: string }[]
  >([]);
  const [inputValue, setInputValue] = useState('');
  const [loading, setLoading] = useState(false);
  const scrollViewRef = useRef<ScrollView>(null);
  useEffect(() => {
    fetchUserData();
  }, []);
  useEffect(() => {
    if (userData?.user_id) {
      fetchCoach_User_Data(userData.user_id);
    }
  }, [userData]);
  useEffect(() => {
    if (userHasCoach === false) {
      fetchCoachData();
    }
  }, [userHasCoach]);
  const fetchUserData = async () => {
    try {
      const {
        data: { user },
        error: userError,
      } = await supabase.auth.getUser();
      if (userError || !user) return;
      const { data, error } = await supabase
        .from('user_fitness_profiles')
        .select('user_id')
        .eq('user_id', user.id)
        .single();
      if (!error) setUserData(data);
    } catch (err) {
      console.error('Unexpected error:', err);
    }
  };
  const fetchCoachData = async () => {
    const { data, error } = await supabase
      .from('coach_persona')
      .select('id, persona_name');
    if (!error) setCoachPersonas(data);
  };
  const fetchCoach_User_Data = async (userId: string) => {
    const { data, error } = await supabase
      .from('user_coach_map')
      .select('user_id, coach_id');
    if (!error) {
      const userExists = data.some((entry) => entry.user_id === userId);
      setUserHasCoach(userExists);
    }
  };
  const fetchChatHistory = async (userId: string) => {
    try {
      const res = await fetch(
        `https://ai-coach.delicut.click/chat_history?user_id=${userId}&page=1`,
      );
      const json = await res.json();
      if (json?.messages) {
        const formatted = json.messages.map((msg: any) => ({
          sender: msg.role === 'ai' ? 'ai' : 'user',
          text: msg.message,
        }));
        setChatMessages(formatted);
        setTimeout(
          () => scrollViewRef.current?.scrollToEnd({ animated: false }),
          0,
        );
      }
    } catch (err) {
      console.error('Failed to load chat history:', err);
    }
  };
  const handleCoachSelect = async (id: string) => {
    setLoading(true);
    try {
      const res = await fetch('https://ai-coach.delicut.click/select_coach', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ user_id: userData.user_id, coach_id: id }),
      });
      const json = await res.json();
      setInitialMessage(json.content);
      setMessageOptions(json.options);
      setChatMessages((prev) => [
        ...prev,
        { sender: 'ai', text: json.content },
      ]);
      setChatStarted(true);
      setTimeout(
        () => scrollViewRef.current?.scrollToEnd({ animated: true }),
        100,
      );
    } catch (err) {
      console.error('Error selecting coach:', err);
    } finally {
      setLoading(false);
    }
  };
  const handleUserOptionSelect = async (query: string) => {
    if (!userData?.user_id) return;
    setLoading(true);
    setChatMessages((prev) => [...prev, { sender: 'user', text: query }]);
    try {
      const res = await fetch('https://ai-coach.delicut.click/ai_coach', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ user_id: userData.user_id, query }),
      });
      const json = await res.json();
      if (json?.response) {
        setInitialMessage(json.response.content);
        setMessageOptions(json.response.options || []);
        setChatMessages((prev) => {
          const updated = [
            ...prev,
            { sender: 'ai', text: json.response.content },
          ];
          setTimeout(
            () => scrollViewRef.current?.scrollToEnd({ animated: true }),
            100,
          );
          return updated;
        });
      }
    } catch (err) {
      console.error('AI coach error:', err);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    if (userHasCoach === true && userData?.user_id) {
      setChatStarted(true);
      fetchChatHistory(userData.user_id);
    }
  }, [userHasCoach]);
  useLayoutEffect(() => {
    if (chatStarted && chatMessages.length > 0) {
      scrollViewRef.current?.scrollToEnd({ animated: false });
    }
  }, [chatMessages]);
  return (
    <View style={styles.selectCoachDiv}>
      {loading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="orange" />
          <Text style={{ marginTop: 8, color: 'black', fontWeight: '600' }}>
            Please wait...
          </Text>
        </View>
      )}
      {chatStarted ? (
        <ChatUI
          message={initialMessage || 'Welcome back!'}
          options={messageOptions}
          onOptionSelect={handleUserOptionSelect}
          chatMessages={chatMessages}
          scrollViewRef={scrollViewRef}
          inputValue={inputValue}
          setInputValue={setInputValue}
        />
      ) : (
        <View style={{ flex: 1 }}>
          {userHasCoach === null ? (
            <View style={styles.centeredMessage}>
              <ActivityIndicator size="small" color="white" />
              <Text style={{ marginTop: 8, color: 'white' }}>
                Checking coach assignment...
              </Text>
            </View>
          ) : !userHasCoach && coachPersonas.length === 0 ? (
            <View style={styles.centeredMessage}>
              <Text style={{ color: 'white' }}>
                No coach profiles available.
              </Text>
            </View>
          ) : (
            <>
              <Text style={styles.title}>Select Coach</Text>
              <FlatList
                data={coachPersonas}
                keyExtractor={(item) => item.id}
                contentContainerStyle={{ padding: 16 }}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    onPress={() => handleCoachSelect(item.id)}
                    style={styles.card}
                  >
                    <Text style={styles.name}>{item.persona_name}</Text>
                  </TouchableOpacity>
                )}
              />
            </>
          )}
        </View>
      )}
    </View>
  );
};
export default Chat;
const styles = StyleSheet.create({
  card: {
    backgroundColor: 'orange',
    borderRadius: 2,
    marginBottom: 12,
    padding: 16,
  },
  centeredMessage: {
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
    padding: 24,
  },
  loadingContainer: {
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.5)',
    bottom: 0,
    justifyContent: 'center',
    left: 0,
    position: 'absolute',
    right: 0,
    top: 0,
    zIndex: 10,
  },
  name: { fontSize: 15, fontWeight: 'bold' },
  selectCoachDiv: { backgroundColor: 'black', flex: 1 },
  title: {
    color: 'white',
    fontSize: 22,
    fontWeight: 'bold',
    marginLeft: 18,
    marginTop: 10,
  },
});
