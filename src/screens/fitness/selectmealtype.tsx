import React from 'react';
import { StyleSheet, View } from 'react-native';
import { GorhomBottomSheet } from '../../components/BottomSheet/BottomSheet';

interface Props {
  open: boolean;
  onClose: () => void;
}

const Selectmealtype: React.FC<Props> = ({ open, onClose }) => {
  return (
    <GorhomBottomSheet
      sheetOpen={open}
      sheetClose={onClose}
      title="Select meal type"
      closeOnBackdrop
      closeOnPressBack
    >
      <View style={styles.container}></View>
    </GorhomBottomSheet>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 10,
    paddingTop: 4,
  },
  touchableArea: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    borderRadius: 12,
  },

  textSize: {
    textAlign: 'center',
    marginTop: 10,
  },
  topRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  bottomRow: {
    alignItems: 'flex-start',
  },
  card: {
    width: '30%',
    borderRadius: 12,
    paddingVertical: 20,
    // paddingHorizontal: 5,
    alignItems: 'center',
    justifyContent: 'center',
  },
  singleCard: {
    width: '30%',
    borderRadius: 12,
    paddingVertical: 20,
    paddingHorizontal: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default Selectmealtype;
