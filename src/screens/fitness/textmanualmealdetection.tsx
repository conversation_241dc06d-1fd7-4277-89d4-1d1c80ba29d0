import { NavigationProp, useNavigation } from '@react-navigation/native';
import { StackScreenProps } from '@react-navigation/stack';
import React from 'react';
import {
  Image,
  StyleSheet,
  Text,
  TextInput,
  ToastAndroid,
  TouchableOpacity,
  View,
} from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { useDispatch } from 'react-redux';
import { RootFitnessStackParamList } from '../../../@types/navigation';
import { PrimaryBtn } from '../../components/Buttons/Btns';
import { useUserId } from '../../components/FitnessComponent/hooks/useUserId';
import Header from '../../components/Header/Header';
import { logMeal } from '../../redux/action/appActions';
import { fontStyles } from '../../theme/fonts';
import { Calorieandmacro } from './calorieandmacro';
import Dateandtime from './datetime';
import Selectmealtype from './selectmealtype';
import SimpleCameraComponent from './simplecamera';

const TextManualMealDetection: React.FC<
  StackScreenProps<RootFitnessStackParamList, 'TextManualMealDetection'>
> = ({ route }) => {
  const dispatch = useDispatch();
  const navigation = useNavigation<NavigationProp<RootFitnessStackParamList>>();
  const { loading: userFetchLoading, userId } = useUserId();

  // State for modal visibility
  const [iscaloriesheet, setiscaloriesheet] = React.useState(false);
  const [isdateTime, setisdateTime] = React.useState(false);
  const [ismealtype, setismealtype] = React.useState(false);
  const [isCameraOpen, setIsCameraOpen] = React.useState(false);

  // State for food details
  const [foodName, setFoodName] = React.useState('');
  const [selectedImageUri, setSelectedImageUri] = React.useState<string | null>(
    null,
  );
  const [editableNutrition, setEditableNutrition] = React.useState({
    calories: 0,
    protein: 0,
    carbs: 0,
    fats: 0,
  });

  // State for meal details
  const [selectedMealType, setSelectedMealType] = React.useState('Meal');
  const [selectedDateTime, setSelectedDateTime] = React.useState(new Date());

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    });
  };

  const validateInputs = () => {
    if (!foodName.trim()) {
      ToastAndroid.show('Please enter food name', ToastAndroid.SHORT);
      return false;
    }

    if (editableNutrition.calories <= 0) {
      ToastAndroid.show('Please enter valid calories', ToastAndroid.SHORT);
      return false;
    }

    return true;
  };

  // Handle image selection from camera component
  const handleImageSelected = (imageUri: string) => {
    console.log('🖼️ Image selected in parent:', imageUri);
    setSelectedImageUri(imageUri);
    ToastAndroid.show('Image added successfully!', ToastAndroid.SHORT);
  };

  // Handle remove image
  const handleRemoveImage = () => {
    console.log('🗑️ Removing image');
    setSelectedImageUri(null);
    ToastAndroid.show('Image removed', ToastAndroid.SHORT);
  };

  // Handle opening camera
  const handleOpenCamera = () => {
    console.log('📸 Opening camera');
    setIsCameraOpen(true);
  };

  // Handle closing camera
  const handleCloseCamera = () => {
    console.log('🚪 Closing camera');
    setIsCameraOpen(false);
  };

  const handleManualLogMeal = () => {
    if (!validateInputs()) {
      return;
    }

    // For manual entry, we'll use a standard weight of 100g
    const standardWeight = 100;

    const payload = {
      analysis_data: {
        display_data: {
          food_name: foodName.trim(),
          calories: editableNutrition.calories,
          protein_g: editableNutrition.protein,
          carbs_g: editableNutrition.carbs,
          fat_g: editableNutrition.fats,
        },
        detailed_breakdown: {
          portion_breakdown: [], // Empty array for manual entry
          total_weight_grams: standardWeight,
          preparation_notes: 'Manually entered meal',
        },
        confidence_metrics: {
          overall_confidence: 1.0, // Perfect confidence for manual entry
          ingredient_confidence: 1.0,
          portion_confidence: 1.0,
          nutrition_confidence: 1.0,
        },
        detection: {
          food_items: [foodName.trim()],
          confidence_score: 1.0,
        },
        nutrition: {
          macros: {
            calories: editableNutrition.calories,
            protein_g: editableNutrition.protein,
            carbs_g: editableNutrition.carbs,
            fat_g: editableNutrition.fats,
          },
          per_100g: {
            calories: Math.round(
              (editableNutrition.calories / standardWeight) * 100,
            ),
            protein_g:
              Math.round(
                (editableNutrition.protein / standardWeight) * 100 * 100,
              ) / 100,
            carbs_g:
              Math.round(
                (editableNutrition.carbs / standardWeight) * 100 * 100,
              ) / 100,
            fat_g:
              Math.round(
                (editableNutrition.fats / standardWeight) * 100 * 100,
              ) / 100,
          },
        },
        analysis_version: 'manual-entry-v1.0',
        processing_time_ms: 0,
      },
      image_url: selectedImageUri, // Include the selected image
      user_id: userId,
      meal_type: selectedMealType.toLowerCase(),
      date_time: selectedDateTime.toISOString(),
      notes: 'Manually entered meal',
    };

    console.log(
      '[LogMeal Payload - Manual Entry]',
      JSON.stringify(payload, null, 2),
    );
    dispatch(logMeal(payload));
    ToastAndroid.show('Meal logged successfully!', ToastAndroid.SHORT);
    navigation.navigate('Home');
  };

  return (
    <>
      <Header
        headerTitle="Add Meal Manually"
        showDivider={false}
        textStyle={{ textAlign: 'center', flex: 0.9 }}
      />
      <View style={styles.headerDivider} />

      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.container}>
          {/* Time and Meal Type Selection */}
          <View style={styles.box}>
            <View style={styles.headerRow}>
              <TouchableOpacity
                style={styles.timeBox}
                onPress={() => setisdateTime(true)}
              >
                <Text style={fontStyles.Maison_600_14PX_16_8LH}>
                  {formatTime(selectedDateTime)}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.timeBox}
                onPress={() => setismealtype(true)}
              >
                <Text style={fontStyles.Maison_600_14PX_16_8LH}>
                  {selectedMealType.charAt(0).toUpperCase() +
                    selectedMealType.slice(1)}
                </Text>
              </TouchableOpacity>
            </View>

            {/* Food Image Section */}
            <View style={styles.foodNameContainer}>
              {selectedImageUri ? (
                <View style={styles.selectedImageContainer}>
                  <Image
                    source={{ uri: selectedImageUri }}
                    style={styles.selectedImage}
                    resizeMode="cover"
                  />
                  <TouchableOpacity
                    style={styles.removeImageButton}
                    onPress={handleRemoveImage}
                    activeOpacity={0.7}
                  >
                    <Text style={styles.removeImageText}>×</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.changeImageButton}
                    onPress={handleOpenCamera}
                    activeOpacity={0.7}
                  >
                    <Text style={styles.changeImageText}>Change</Text>
                  </TouchableOpacity>
                </View>
              ) : (
                <TouchableOpacity
                  style={styles.addPhotoPlaceholder}
                  onPress={handleOpenCamera}
                  activeOpacity={0.7}
                >
                  <Text style={styles.addPhotoText}>+ Add photo</Text>
                </TouchableOpacity>
              )}
            </View>

            <View style={styles.foodInputContainer}>
              <TextInput
                placeholder="Enter food name"
                value={foodName}
                onChangeText={setFoodName}
                style={[fontStyles.Maison_500_20PX_28LH, styles.foodNameInput]}
                placeholderTextColor="#999"
                textAlign="center"
              />
            </View>

            <Text style={[fontStyles.Maison_400_16PX_20LH, styles.subTitle]}>
              Meal name entered manually
            </Text>
          </View>

          {/* Calories & Macros Section */}
          <View style={styles.box}>
            <View style={styles.sectionHeader}>
              <Text style={fontStyles.Maison_600_18PX_24LH}>
                Calories & macros
              </Text>
              <Text
                style={[fontStyles.Maison_600_14PX_24LH, styles.editButton]}
                onPress={() => setiscaloriesheet(true)}
              >
                Edit
              </Text>
            </View>

            <View style={styles.calorieContainer}>
              <Text
                style={[fontStyles.Maison_600_32PX_40LH, styles.calorieText]}
              >
                {editableNutrition.calories}
              </Text>
              <Text style={[fontStyles.Maison_400_16PX_22LH, styles.kcal]}>
                kcal
              </Text>
            </View>

            {/* Macro Bar */}
            <View style={styles.macroBarContainer}>
              <View
                style={[
                  styles.macroSegment,
                  {
                    flex: editableNutrition.protein || 1,
                    backgroundColor: '#F7931A',
                  },
                ]}
              />
              <View
                style={[
                  styles.macroSegment,
                  {
                    flex: editableNutrition.carbs || 1,
                    backgroundColor: '#6B8E23',
                  },
                ]}
              />
              <View
                style={[
                  styles.macroSegment,
                  {
                    flex: editableNutrition.fats || 1,
                    backgroundColor: '#C2B90A',
                  },
                ]}
              />
            </View>

            {/* Macro Legend */}
            <View style={styles.macroLegendRow}>
              <View style={styles.macroLegendColumn}>
                <View style={styles.labelRow}>
                  <View style={[styles.dot, { backgroundColor: '#F7931A' }]} />
                  <Text style={styles.macroLabel}>Protein</Text>
                </View>
                <Text style={styles.macroValue}>
                  {editableNutrition.protein}g
                </Text>
              </View>

              <View style={styles.macroLegendColumn}>
                <View style={styles.labelRow}>
                  <View style={[styles.dot, { backgroundColor: '#6B8E23' }]} />
                  <Text style={styles.macroLabel}>Carbs</Text>
                </View>
                <Text style={styles.macroValue}>
                  {editableNutrition.carbs}g
                </Text>
              </View>

              <View style={styles.macroLegendColumn}>
                <View style={styles.labelRow}>
                  <View style={[styles.dot, { backgroundColor: '#C2B90A' }]} />
                  <Text style={styles.macroLabel}>Fats</Text>
                </View>
                <Text style={styles.macroValue}>{editableNutrition.fats}g</Text>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Bottom Log Meal Button */}
      <View style={styles.buttonContainer}>
        <PrimaryBtn text="Log meal" onPress={handleManualLogMeal} />
      </View>

      {/* Modals */}
      <Calorieandmacro
        open={iscaloriesheet}
        initialValues={{
          calories: editableNutrition.calories.toString(),
          protein: editableNutrition.protein.toString(),
          carbs: editableNutrition.carbs.toString(),
          fat: editableNutrition.fats.toString(),
        }}
        onClose={(values) => {
          setiscaloriesheet(false);
          if (values) {
            const newNutrition = {
              calories: Math.max(0, parseFloat(values.calories) || 0),
              protein: Math.max(0, parseFloat(values.protein) || 0),
              carbs: Math.max(0, parseFloat(values.carbs) || 0),
              fats: Math.max(0, parseFloat(values.fat) || 0),
            };
            setEditableNutrition(newNutrition);
            console.log('Updated nutrition values:', newNutrition);
          }
        }}
      />

      <Selectmealtype
        open={ismealtype}
        selectedMealType={selectedMealType}
        onClose={(mealType) => {
          setismealtype(false);
          if (mealType) {
            setSelectedMealType(mealType);
          }
        }}
      />

      <Dateandtime
        open={isdateTime}
        initialDate={selectedDateTime}
        onClose={(date) => {
          setisdateTime(false);
          if (date) {
            setSelectedDateTime(date);
          }
        }}
      />

      {/* Camera Component */}
      <SimpleCameraComponent
        open={isCameraOpen}
        onClose={handleCloseCamera}
        onImageSelected={handleImageSelected}
      />
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9F4ED',
    paddingHorizontal: 24,
  },
  scrollContainer: {
    paddingBottom: 20,
    backgroundColor: '#F9F4ED',
  },
  headerDivider: {
    height: 0.1,
    backgroundColor: '#00000005',
    width: '100%',
    marginTop: 1.5,
  },
  box: {
    backgroundColor: '#fff',
    borderRadius: 20,
    marginVertical: 12,
    padding: 16,
    width: '100%',
    gap: 16,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
    paddingHorizontal: 5,
  },
  timeBox: {
    backgroundColor: 'white',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    borderColor: '#E5E5E6',
    borderWidth: 1,
  },
  foodNameContainer: {
    alignItems: 'center',
    marginVertical: 12,
  },
  addPhotoPlaceholder: {
    width: 160,
    height: 160,
    borderRadius: 80,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#E5E5E6',
    borderStyle: 'dashed',
  },
  addPhotoText: {
    fontSize: 16,
    color: '#999',
    fontWeight: '500',
  },
  selectedImageContainer: {
    position: 'relative',
    width: 160,
    height: 160,
    borderRadius: 80,
    overflow: 'hidden',
  },
  selectedImage: {
    width: '100%',
    height: '100%',
    borderRadius: 80,
  },
  removeImageButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(255,0,0,0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  removeImageText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  changeImageButton: {
    position: 'absolute',
    bottom: 8,
    left: '50%',
    transform: [{ translateX: -25 }],
    backgroundColor: 'rgba(0,0,0,0.7)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
  },
  changeImageText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
  },
  foodInputContainer: {
    alignItems: 'center',
    marginVertical: 8,
  },
  foodNameInput: {
    textAlign: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E6',
    minWidth: 200,
    backgroundColor: 'transparent',
  },
  subTitle: {
    color: '#999',
    textAlign: 'center',
    marginTop: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 5,
    marginBottom: 10,
  },
  editButton: {
    color: '#FF3F1F',
  },
  calorieContainer: {
    flexDirection: 'row',
    gap: 7,
  },
  calorieText: {
    color: '#FF3F1F',
  },
  kcal: {
    marginTop: 10,
    marginLeft: 2,
  },
  macroBarContainer: {
    flexDirection: 'row',
    height: 10,
    borderRadius: 5,
    overflow: 'hidden',
    marginVertical: 10,
  },
  macroSegment: {
    height: '100%',
  },
  macroLegendRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  macroLegendColumn: {
    alignItems: 'center',
    flex: 1,
  },
  labelRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  dot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginRight: 4,
  },
  macroLabel: {
    fontSize: 14,
    color: '#555',
  },
  macroValue: {
    marginTop: 6,
    fontSize: 14,
    fontWeight: '600',
  },
  buttonContainer: {
    paddingHorizontal: 22,
    paddingBottom: 20,
  },
  cameraModal: {
    zIndex: 1000,
    elevation: 1000,
  },
});

export default TextManualMealDetection;
