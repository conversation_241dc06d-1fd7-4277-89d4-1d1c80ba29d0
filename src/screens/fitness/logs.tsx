// // Logs.tsx
// import React from 'react';
// import {
//   View,
//   Text,
//   ScrollView,
//   StyleSheet,
//   TouchableOpacity,
//   SafeAreaView,
//   Alert,
//   PermissionsAndroid,
//   Platform,
// } from 'react-native';
// import RNFS from 'react-native-fs';
// import Share from 'react-native-share'; // Import react-native-share
// import { useLogger, LogEntry } from './logcontext';

// const Logs: React.FC = () => {
//   // const { logs, clearLogs } = useLogger();

//   const formatTimestamp = (timestamp: Date) => {
//     return (
//       timestamp.toLocaleTimeString() + '.' + timestamp.getMilliseconds().toString().padStart(3, '0')
//     );
//   };

//   const formatTimestampForExport = (timestamp: Date) => {
//     return timestamp.toISOString();
//   };

//   const getLevelColor = (level: LogEntry['level']) => {
//     switch (level) {
//       case 'error':
//         return '#ff4444';
//       case 'warn':
//         return '#ffaa00';
//       case 'info':
//         return '#4444ff';
//       case 'debug':
//         return '#888888';
//       default:
//         return '#000000';
//     }
//   };

//   const getLevelBadgeStyle = (level: LogEntry['level']) => {
//     return {
//       ...styles.levelBadge,
//       backgroundColor: getLevelColor(level),
//     };
//   };

//   const shareFile = async (
//     filePath: string,
//     fileName: string,
//     mimeType: 'text/plain' | 'application/json',
//   ) => {
//     try {
//       // Verify file exists
//       const fileExists = await RNFS.exists(filePath);
//       if (!fileExists) {
//         throw new Error(`File does not exist at path: ${filePath}`);
//       }

//       // Get file stats
//       const fileStat = await RNFS.stat(filePath);
//       if (fileStat.size === 0) {
//         throw new Error('File is empty');
//       }

//       console.log(`Sharing file: ${filePath}, Size: ${fileStat.size} bytes`);

//       const shareOptions = {
//         title: `Share ${fileName}`,
//         message: `Health Connect App Logs`,
//         url: `file://${filePath}`,
//         type: mimeType,
//         filename: fileName,
//         // For Android, you might need to specify the package
//         ...(Platform.OS === 'android' && {
//           subject: `Health Connect App Logs`,
//         }),
//       };

//       const result = await Share.open(shareOptions);
//       console.log('Share result:', result);
//       return result;
//     } catch (error) {
//       console.error('Error sharing file:', error);

//       if (error.message && error.message.includes('User did not share')) {
//         // User cancelled, not an error
//         return;
//       }

//       throw new Error(`Could not share file: ${error.message || 'Unknown error'}`);
//     }
//   };

//   const exportLogsAsText = async (shouldShare: boolean = false) => {
//     if (logs.length === 0) {
//       Alert.alert('No Logs', 'There are no logs to export.');
//       return;
//     }

//     try {
//       const logsText = logs
//         .map(log => {
//           return `[${formatTimestampForExport(log.timestamp)}] [${log.level.toUpperCase()}] [${
//             log.component
//           }] ${log.message}`;
//         })
//         .join('\n');

//       const exportContent = `Health Connect App Logs\nExported at: ${new Date().toISOString()}\nTotal logs: ${
//         logs.length
//       }\n\n${logsText}`;

//       const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
//       const timeString = new Date().toTimeString().slice(0, 8).replace(/:/g, '-');
//       const fileName = `health_connect_logs_${timestamp}_${timeString}.txt`;

//       let filePath;
//       let saveLocation;

//       try {
//         // Save to Documents directory
//         filePath = `${RNFS.DocumentDirectoryPath}/${fileName}`;
//         await RNFS.writeFile(filePath, exportContent, 'utf8');
//         saveLocation = 'App Documents folder';

//         console.log(`File saved to Documents: ${filePath}`);

//         // Try to copy to Downloads as well
//         try {
//           if (Platform.OS === 'android') {
//             const downloadPath = `${RNFS.DownloadDirectoryPath}/${fileName}`;
//             await RNFS.copyFile(filePath, downloadPath);
//             saveLocation = 'Downloads folder (and App Documents)';
//             console.log(`File also copied to Downloads: ${downloadPath}`);
//           }
//         } catch (downloadError) {
//           console.log('Could not copy to Downloads:', downloadError);
//         }
//       } catch (error) {
//         console.error('Error saving file:', error);
//         throw error;
//       }

//       if (shouldShare) {
//         try {
//           await shareFile(filePath, fileName, 'text/plain');
//           Alert.alert(
//             'Export & Share Successful',
//             `Logs saved and shared!\nFile: ${fileName}\nSaved in: ${saveLocation}`,
//           );
//         } catch (shareError) {
//           console.error('Error sharing file:', shareError);
//           Alert.alert(
//             'Export Successful, Share Failed',
//             `File saved successfully but sharing failed.\nFile: ${fileName}\nLocation: ${saveLocation}\nError: ${shareError.message}`,
//             [
//               { text: 'OK', style: 'cancel' },
//               {
//                 text: 'Try Share Again',
//                 onPress: () => shareFile(filePath, fileName, 'text/plain'),
//               },
//             ],
//           );
//         }
//       } else {
//         Alert.alert(
//           'Export Successful',
//           `Logs exported successfully!\nFile: ${fileName}\nLocation: ${saveLocation}`,
//           [
//             { text: 'OK', style: 'cancel' },
//             {
//               text: 'Share Now',
//               onPress: () => shareFile(filePath, fileName, 'text/plain'),
//             },
//           ],
//         );
//       }
//     } catch (error) {
//       console.error('Error exporting logs as text:', error);
//       Alert.alert('Export Error', `Failed to export logs as text file.\nError: ${error.message}`);
//     }
//   };

//   const exportLogsAsJSON = async (shouldShare: boolean = false) => {
//     if (logs.length === 0) {
//       Alert.alert('No Logs', 'There are no logs to export.');
//       return;
//     }

//     try {
//       const exportData = {
//         exportedAt: new Date().toISOString(),
//         totalLogs: logs.length,
//         appInfo: {
//           name: 'Health Connect App',
//           platform: Platform.OS,
//           version: Platform.Version.toString(),
//         },
//         logSummary: getLogSummary(),
//         logs: logs.map(log => ({
//           id: log.id,
//           timestamp: formatTimestampForExport(log.timestamp),
//           level: log.level,
//           component: log.component,
//           message: log.message,
//         })),
//       };

//       const jsonContent = JSON.stringify(exportData, null, 2);

//       const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
//       const timeString = new Date().toTimeString().slice(0, 8).replace(/:/g, '-');
//       const fileName = `health_connect_logs_${timestamp}_${timeString}.json`;

//       let filePath;
//       let saveLocation;

//       try {
//         // Save to Documents directory
//         filePath = `${RNFS.DocumentDirectoryPath}/${fileName}`;
//         await RNFS.writeFile(filePath, jsonContent, 'utf8');
//         saveLocation = 'App Documents folder';

//         console.log(`File saved to Documents: ${filePath}`);

//         // Try to copy to Downloads as well
//         try {
//           if (Platform.OS === 'android') {
//             const downloadPath = `${RNFS.DownloadDirectoryPath}/${fileName}`;
//             await RNFS.copyFile(filePath, downloadPath);
//             saveLocation = 'Downloads folder (and App Documents)';
//             console.log(`File also copied to Downloads: ${downloadPath}`);
//           }
//         } catch (downloadError) {
//           console.log('Could not copy to Downloads:', downloadError);
//         }
//       } catch (error) {
//         console.error('Error saving file:', error);
//         throw error;
//       }

//       if (shouldShare) {
//         try {
//           await shareFile(filePath, fileName, 'application/json');
//           Alert.alert(
//             'Export & Share Successful',
//             `Logs saved and shared!\nFile: ${fileName}\nSaved in: ${saveLocation}`,
//           );
//         } catch (shareError) {
//           console.error('Error sharing file:', shareError);
//           Alert.alert(
//             'Export Successful, Share Failed',
//             `File saved successfully but sharing failed.\nFile: ${fileName}\nLocation: ${saveLocation}\nError: ${shareError.message}`,
//             [
//               { text: 'OK', style: 'cancel' },
//               {
//                 text: 'Try Share Again',
//                 onPress: () => shareFile(filePath, fileName, 'application/json'),
//               },
//             ],
//           );
//         }
//       } else {
//         Alert.alert(
//           'Export Successful',
//           `Logs exported successfully!\nFile: ${fileName}\nLocation: ${saveLocation}`,
//           [
//             { text: 'OK', style: 'cancel' },
//             {
//               text: 'Share Now',
//               onPress: () => shareFile(filePath, fileName, 'application/json'),
//             },
//           ],
//         );
//       }
//     } catch (error) {
//       console.error('Error exporting logs as JSON:', error);
//       Alert.alert('Export Error', `Failed to export logs as JSON file.\nError: ${error.message}`);
//     }
//   };

//   const showExportOptions = () => {
//     Alert.alert('Export Logs', `Choose export format and action:\n(${logs.length} logs total)`, [
//       {
//         text: 'Cancel',
//         style: 'cancel',
//       },
//       {
//         text: 'Save as Text (.txt)',
//         onPress: () => exportLogsAsText(false),
//       },
//       {
//         text: 'Save as JSON (.json)',
//         onPress: () => exportLogsAsJSON(false),
//       },
//       {
//         text: 'Save & Share Text',
//         onPress: () => exportLogsAsText(true),
//       },
//       {
//         text: 'Save & Share JSON',
//         onPress: () => exportLogsAsJSON(true),
//       },
//     ]);
//   };

//   const getLogSummary = () => {
//     const summary = logs.reduce((acc, log) => {
//       acc[log.level] = (acc[log.level] || 0) + 1;
//       return acc;
//     }, {} as Record<string, number>);

//     return summary;
//   };

//   const logSummary = getLogSummary();

//   return (
//     <SafeAreaView style={styles.container}>
//       <View style={styles.header}>
//         <Text style={styles.headerTitle}>Application Logs</Text>
//         <View style={styles.buttonContainer}>
//           <TouchableOpacity style={styles.exportButton} onPress={showExportOptions}>
//             <Text style={styles.exportButtonText}>Export</Text>
//           </TouchableOpacity>
//           <TouchableOpacity style={styles.clearButton} onPress={clearLogs}>
//             <Text style={styles.clearButtonText}>Clear</Text>
//           </TouchableOpacity>
//         </View>
//       </View>

//       {logs.length > 0 && (
//         <View style={styles.summaryContainer}>
//           <Text style={styles.summaryTitle}>Log Summary:</Text>
//           <View style={styles.summaryRow}>
//             {Object.entries(logSummary).map(([level, count]) => (
//               <View key={level} style={styles.summaryItem}>
//                 <View
//                   style={[
//                     styles.summaryBadge,
//                     { backgroundColor: getLevelColor(level as LogEntry['level']) },
//                   ]}
//                 >
//                   <Text style={styles.summaryBadgeText}>{level.toUpperCase()}</Text>
//                 </View>
//                 <Text style={styles.summaryCount}>{count}</Text>
//               </View>
//             ))}
//             <View style={styles.summaryItem}>
//               <Text style={styles.summaryTotal}>Total: {logs.length}</Text>
//             </View>
//           </View>
//         </View>
//       )}

//       <View style={styles.logContainer}>
//         {logs.length === 0 ? (
//           <View style={styles.emptyState}>
//             <Text style={styles.emptyStateText}>No logs available</Text>
//             <Text style={styles.emptyStateSubtext}>
//               Logs from Login and MainScreen will appear here
//             </Text>
//           </View>
//         ) : (
//           <ScrollView
//             style={styles.scrollView}
//             contentContainerStyle={styles.scrollContent}
//             showsVerticalScrollIndicator={true}
//           >
//             {logs.map(log => (
//               <View key={log.id} style={styles.logEntry}>
//                 <View style={styles.logHeader}>
//                   <View style={getLevelBadgeStyle(log.level)}>
//                     <Text style={styles.levelText}>{log.level.toUpperCase()}</Text>
//                   </View>
//                   <Text style={styles.componentText}>[{log.component}]</Text>
//                   <Text style={styles.timestampText}>{formatTimestamp(log.timestamp)}</Text>
//                 </View>
//                 <Text style={styles.messageText}>{log.message}</Text>
//               </View>
//             ))}
//           </ScrollView>
//         )}
//       </View>
//     </SafeAreaView>
//   );
// };

// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//     backgroundColor: '#f5f5f5',
//   },
//   header: {
//     flexDirection: 'row',
//     justifyContent: 'space-between',
//     alignItems: 'center',
//     paddingHorizontal: 16,
//     paddingVertical: 12,
//     backgroundColor: '#ffffff',
//     borderBottomWidth: 1,
//     borderBottomColor: '#e0e0e0',
//   },
//   headerTitle: {
//     fontSize: 18,
//     fontWeight: 'bold',
//     color: '#333333',
//     flex: 1,
//   },
//   buttonContainer: {
//     flexDirection: 'row',
//     gap: 8,
//   },
//   exportButton: {
//     backgroundColor: '#4CAF50',
//     paddingHorizontal: 12,
//     paddingVertical: 6,
//     borderRadius: 4,
//   },
//   exportButtonText: {
//     color: '#ffffff',
//     fontSize: 12,
//     fontWeight: '600',
//   },
//   clearButton: {
//     backgroundColor: '#ff4444',
//     paddingHorizontal: 12,
//     paddingVertical: 6,
//     borderRadius: 4,
//   },
//   clearButtonText: {
//     color: '#ffffff',
//     fontSize: 12,
//     fontWeight: '600',
//   },
//   summaryContainer: {
//     backgroundColor: '#ffffff',
//     marginHorizontal: 8,
//     marginTop: 8,
//     padding: 12,
//     borderRadius: 8,
//     shadowColor: '#000',
//     shadowOffset: {
//       width: 0,
//       height: 1,
//     },
//     shadowOpacity: 0.1,
//     shadowRadius: 2,
//     elevation: 2,
//   },
//   summaryTitle: {
//     fontSize: 14,
//     fontWeight: '600',
//     color: '#333333',
//     marginBottom: 8,
//   },
//   summaryRow: {
//     flexDirection: 'row',
//     flexWrap: 'wrap',
//     alignItems: 'center',
//     gap: 8,
//   },
//   summaryItem: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     gap: 4,
//   },
//   summaryBadge: {
//     paddingHorizontal: 4,
//     paddingVertical: 2,
//     borderRadius: 2,
//   },
//   summaryBadgeText: {
//     color: '#ffffff',
//     fontSize: 9,
//     fontWeight: '600',
//   },
//   summaryCount: {
//     fontSize: 12,
//     color: '#666666',
//     fontWeight: '600',
//   },
//   summaryTotal: {
//     fontSize: 12,
//     color: '#333333',
//     fontWeight: 'bold',
//   },
//   logContainer: {
//     flex: 1,
//     backgroundColor: '#ffffff',
//     margin: 8,
//     borderRadius: 8,
//     shadowColor: '#000',
//     shadowOffset: {
//       width: 0,
//       height: 2,
//     },
//     shadowOpacity: 0.1,
//     shadowRadius: 3.84,
//     elevation: 5,
//   },
//   emptyState: {
//     flex: 1,
//     justifyContent: 'center',
//     alignItems: 'center',
//     padding: 32,
//   },
//   emptyStateText: {
//     fontSize: 16,
//     color: '#666666',
//     marginBottom: 8,
//   },
//   emptyStateSubtext: {
//     fontSize: 14,
//     color: '#999999',
//     textAlign: 'center',
//   },
//   scrollView: {
//     flex: 1,
//   },
//   scrollContent: {
//     padding: 12,
//   },
//   logEntry: {
//     marginBottom: 12,
//     padding: 12,
//     backgroundColor: '#f9f9f9',
//     borderRadius: 6,
//     borderLeftWidth: 3,
//     borderLeftColor: '#e0e0e0',
//   },
//   logHeader: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     marginBottom: 6,
//     flexWrap: 'wrap',
//   },
//   levelBadge: {
//     paddingHorizontal: 6,
//     paddingVertical: 2,
//     borderRadius: 3,
//     marginRight: 8,
//     minWidth: 50,
//     alignItems: 'center',
//   },
//   levelText: {
//     color: '#ffffff',
//     fontSize: 10,
//     fontWeight: '600',
//   },
//   componentText: {
//     fontSize: 12,
//     fontWeight: '600',
//     color: '#666666',
//     marginRight: 8,
//   },
//   timestampText: {
//     fontSize: 11,
//     color: '#999999',
//     fontFamily: 'monospace',
//   },
//   messageText: {
//     fontSize: 14,
//     color: '#333333',
//     fontFamily: 'monospace',
//     lineHeight: 20,
//   },
// });

// export default Logs;
