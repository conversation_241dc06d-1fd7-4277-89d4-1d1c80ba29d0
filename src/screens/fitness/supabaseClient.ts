import AsyncStorage from '@react-native-async-storage/async-storage';
import { createClient } from '@supabase/supabase-js';
import moment from 'moment-timezone';
import 'react-native-url-polyfill/auto';
export const modules = ['steps', 'calories_burned', 'sleep', 'weight'];

export const supabase = createClient(
  'https://ertqxifvqfavjjnezbfz.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVydHF4aWZ2cWZhdmpqbmV6YmZ6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDczMDA0NzQsImV4cCI6MjA2Mjg3NjQ3NH0.AGBK1RdyzOCj4WDZ-SvcblLTTSozMqem2oez03U-630',
  {
    auth: {
      storage: AsyncStorage,
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: false,
    },
  },
);

export const getUserId = async () => {
  const { data } = await supabase.auth.getUser();
  console.log('data---------------------------------------------', data);
  return data?.user?.id;
};

// export const syncStepsToSupabase = async (stepsRecords: any[], userId: string) => {
//   if (!userId || !Array.isArray(stepsRecords) || stepsRecords.length === 0) {return;}

//   // Prepare record_metadata insert data
//   const metadataInserts = stepsRecords.map(step => ({
//     record_type: 'Steps',
//     user_id: userId,
//     device_id: null,
//     recording_method: 1,
//     client_record_version: 1,
//     client_record_id: step.metadata?.id || '',
//     data_origin: step.metadata?.dataOrigin || '',
//     last_modified_time: step.metadata?.lastModifiedTime,
//     synced_at: new Date().toISOString(),
//   }));

//   // Bulk insert record_metadata rows
//   const { data: metaDataArray, error: metaErr } = await supabase
//     .from('record_metadata')
//     .insert(metadataInserts)
//     .select('id, client_record_id');

//   if (metaErr || !metaDataArray) {
//     console.error('Metadata bulk insert failed:', metaErr);
//     return;
//   }

//   // Create map from client_record_id to inserted record_metadata id
//   const metadataIdMap = new Map<string, number>();
//   metaDataArray.forEach(meta => {
//     if (meta.client_record_id) {
//       metadataIdMap.set(meta.client_record_id, meta.id);
//     }
//   });

//   // Prepare step inserts with corresponding metadata IDs
//   const stepInserts = stepsRecords.map(step => {
//     const record_metadata_id = metadataIdMap.get(step.metadata?.id || '') || null;
//     return {
//       user_id: userId,
//       record_metadata_id,
//       count: step.count,
//       start_time: step.startTime,
//       end_time: step.endTime,
//       position: 0,
//     };
//   }).filter(step => step.record_metadata_id !== null); // only insert those with valid metadata IDs

//   if (stepInserts.length === 0) {
//     console.log('No valid steps to insert');
//     return;
//   }

//   // Bulk insert steps
//   const { error: stepErr, data: stepData } = await supabase
//     .from('steps')
//     .insert(stepInserts);

//   if (stepErr) {
//     console.error('Steps bulk insert failed:', stepErr);
//   } else {
//     console.log('Steps inserted successfully:', stepData);
//   }
// };

// Todo
export const syncStepsToSupabase = async (
  stepsRecords: any[],
  userId: string,
  start: string,
  end: string,
) => {
  if (!userId || !Array.isArray(stepsRecords) || stepsRecords.length === 0) {
    return;
  }
  console.log('stepsRecords', stepsRecords);
  try {
    // Call the PostgreSQL RPC function with JSONB array and userId
    // if issue arise revert this function name upsert_steps_with_metadata
    const { data, error } = await supabase.rpc(
      'new_android_sync_steps_data_from_json',
      {
        p_user_id: userId,
        p_steps: stepsRecords,
        p_start_filter: start,
        p_end_filter: end,
      },
    );
    console.log(
      'check data ----------------------->',
      userId,
      data,
      error,
      start,
      end,
    );

    if (error) {
      console.error('RPC new_android_sync_steps_data_from_json error:', error);
    } else {
      console.log('Steps and metadata upserted successfully via RPC.');
    }
  } catch (err) {
    console.error('Unexpected error calling RPC:', err);
  }
};
export const syncIosStepsToSupabase = async (
  stepsRecords: any[],
  userId: string,
  start: string,
  end: string,
) => {
  if (!userId || !Array.isArray(stepsRecords) || stepsRecords.length === 0) {
    return;
  }
  console.log('stepsRecords', JSON.stringify(stepsRecords));
  try {
    // Call the PostgreSQL RPC function with JSONB array and userId
    // if issue arise revert this function name upsert_steps_with_metadata
    const { data, error } = await supabase.rpc(
      'sync_ios_steps_data_from_json',
      {
        p_steps: stepsRecords,
        p_user_id: userId,
        p_start_filter: start,
        p_end_filter: end,
      },
    );
    console.log('check data ----------------------->', data, error);

    if (error) {
      console.error('RPC sync_ios_steps_data_from_json error:', error);
    } else {
      console.log('Steps and metadata upserted successfully via RPC.');
    }
  } catch (err) {
    console.error('Unexpected error calling RPC:', err);
  }
};

export const fetchLastSyncedAt = async (userId: string, moduleName: string) => {
  const { data, error } = await supabase
    .from('user_module_sync')
    .select('last_synced_at')
    .eq('user_id', userId)
    .eq('module_name', moduleName)
    .single();

  if (error && error.code !== 'PGRST116') {
    // ignore no rows found error
    console.error('Error fetching last sync:', error);
    return null;
  }
  return data?.last_synced_at ?? null;
};

export const ensureUserModuleSyncEntries = async (userId: string) => {
  const { data: existingEntries, error: fetchError } = await supabase
    .from('user_module_sync')
    .select('module_name')
    .eq('user_id', userId)
    .in('module_name', modules);

  if (fetchError) {
    console.error('Error fetching user_module_sync entries:', fetchError);
    return;
  }

  const existingModules = existingEntries
    ? existingEntries.map((e) => e.module_name)
    : [];
  const missingModules = modules.filter(
    (m: any) => !existingModules.includes(m),
  );

  if (missingModules.length === 0) {
    return; // All modules already have sync entries
  }

  const now = new Date().toISOString();

  const recordsToInsert = missingModules.map((moduleName: any) => ({
    user_id: userId,
    module_name: moduleName,
    last_synced_at: null,
    created_at: now,
    updated_at: now,
  }));

  const { error: insertError } = await supabase
    .from('user_module_sync')
    .insert(recordsToInsert);

  if (insertError) {
    console.error(
      'Error inserting missing user_module_sync entries:',
      insertError,
    );
  }
};

export async function updateLastSyncedAt(
  userId: string,
  moduleName: string,
  dateISOString: string,
): Promise<boolean> {
  try {
    const { error, count } = await supabase
      .from('user_module_sync')
      .update({
        last_synced_at: dateISOString,
        updated_at: new Date().toISOString(),
      })
      .eq('user_id', userId)
      .eq('module_name', moduleName);

    if (error) {
      console.error('Error updating last synced at:', error);
      return false;
    }
    if (count === 0) {
      console.warn(
        `No rows updated for user_id=${userId} module=${moduleName}`,
      );
      // Optionally handle: row does not exist yet!
    }
    return true;
  } catch (err) {
    console.error('Exception in updateLastSyncedAt:', err);
    return false;
  }
}

export async function updateUserTimeZone(userId: string): Promise<boolean> {
  try {
    const { error, count } = await supabase
      .from('user_fitness_profiles')
      .update({
        timezone: moment.tz.guess(),
        updated_at: new Date().toISOString(),
      })
      .eq('user_id', userId);

    if (error) {
      console.error('Error updating user timezone:', error);
      return false;
    }
    if (count === 0) {
      console.warn(`No rows updated for user_id=${userId}`);
      // Optionally handle: row does not exist yet!
    }
    console.log('User timezone updated successfully.');
    return true;
  } catch (err) {
    console.error('Exception in update user timezone:', err);
    return false;
  }
}

// export const syncWeightToSupabase = async (weightRecords: any[], userId: string) => {
//   if (!userId || !Array.isArray(weightRecords) || weightRecords.length === 0) {return;}

//   // Prepare record_metadata insert data for weight records
//   const metadataInserts = weightRecords.map(record => ({
//     record_type: 'Weight',
//     user_id: userId,
//     device_id: null,
//     recording_method: record.metadata?.recordingMethod ?? 0,
//     client_record_version: record.metadata?.clientRecordVersion ?? 0,
//     client_record_id: record.metadata?.id || '',
//     data_origin: record.metadata?.dataOrigin || '',
//     last_modified_time: record.metadata?.lastModifiedTime,
//     synced_at: new Date().toISOString(),
//   }));

//   // Bulk insert record_metadata rows
//   const { data: metaDataArray, error: metaErr } = await supabase
//     .from('record_metadata')
//     .insert(metadataInserts)
//     .select('id, client_record_id');

//   if (metaErr || !metaDataArray) {
//     console.error('Weight metadata bulk insert failed:', metaErr);
//     return;
//   }

//   // Create map from client_record_id to inserted record_metadata id (UUID)
//   const metadataIdMap = new Map<string, string>();
//   metaDataArray.forEach(meta => {
//     if (meta.client_record_id) {
//       metadataIdMap.set(meta.client_record_id, meta.id);
//     }
//   });

//   // Prepare weight inserts with corresponding metadata IDs
//   const weightInserts = weightRecords.map(record => {
//     const record_metadata_id = metadataIdMap.get(record.metadata?.id || '') || null;
//     const w = record.weight || {};

//     return {
//       user_id: userId,
//       record_metadata_id,
//       weight_kg: w.inKilograms ?? null,
//       weight_lb: w.inPounds ?? null,
//       time: record.time,
//       position: 0,
//     };
//   }).filter(record => record.record_metadata_id !== null);

//   if (weightInserts.length === 0) {
//     console.log('No valid weight records to insert');
//     return;
//   }

//   // Bulk insert weight records
//   const { error: weightErr, data: weightData } = await supabase
//     .from('weight')
//     .insert(weightInserts);

//   if (weightErr) {
//     console.error('Weight bulk insert failed:', weightErr);
//   } else {
//     console.log('Weight records inserted successfully:', weightData);
//   }
// };

export const syncWeightToSupabase = async (
  weightRecords: any[],
  userId: string,
) => {
  if (!userId || !Array.isArray(weightRecords) || weightRecords.length === 0) {
    return;
  }
  try {
    // Call the PostgreSQL RPC function with JSONB array and userId
    const { error } = await supabase.rpc('upsert_weight_with_metadata', {
      p_weights: weightRecords,
      p_user_id: userId,
    });

    if (error) {
      console.error('RPC upsert_weight_with_metadata error:', error);
    } else {
      console.log('Weight and metadata upserted successfully via RPC.');
    }
  } catch (err) {
    console.error('Unexpected error calling RPC:', err);
  }
};

// supabaseClient.ts

// export const syncSleepSessionsToSupabase = async (sleepRecords: any[], userId: string) => {
//   if (!userId || !Array.isArray(sleepRecords) || sleepRecords.length === 0) {
//     return;
//   }

// // Prepare record_metadata insert data for weight records
//   const metadataInserts = sleepRecords.map(record => ({
//     record_type: 'Sleep',
//     user_id: userId,
//     device_id: null,
//     recording_method: record.metadata?.recordingMethod ?? 0,
//     client_record_version: record.metadata?.clientRecordVersion ?? 0,
//     client_record_id: record.metadata?.id || '',
//     data_origin: record.metadata?.dataOrigin || '',
//     last_modified_time: record.metadata?.lastModifiedTime,
//     synced_at: new Date().toISOString(),
//   }));

//   // Bulk insert record_metadata rows
//   const { data: metaDataArray, error: metaErr } = await supabase
//     .from('record_metadata')
//     .insert(metadataInserts)
//     .select('id, client_record_id');

//   if (metaErr || !metaDataArray) {
//     console.error('Sleep metadata bulk insert failed:', metaErr);
//     return;
//   }

//   console.log(sleepRecords,'check289');

//   // Prepare sleep session inserts
//   const sleepInserts = sleepRecords.map(record => {
//     const startTime = new Date(record.startTime);
//     const endTime = new Date(record.endTime);
//     const minutes = Math.round((endTime.getTime() - startTime.getTime()) / 60000); // duration in minutes

//     return {
//       user_id: userId,
//       start_time: record.startTime,
//       end_time: record.endTime,
//       minutes,
//     };
//   });

//   // Bulk insert sleep sessions
//   const { error, data } = await supabase
//     .from('sleep_sessions')
//     .insert(sleepInserts);

//   if (error) {
//     console.error('Sleep sessions bulk insert failed:', error);
//   } else {
//     console.log('Sleep sessions inserted successfully:', data);
//   }
// };

export const syncSleepSessionsToSupabase = async (
  sleepRecords: any[],
  userId: string,
) => {
  if (!userId || !Array.isArray(sleepRecords) || sleepRecords.length === 0) {
    return;
  }

  const sleepInserts = sleepRecords.map((record) => {
    const startTime = new Date(record.startTime);
    const endTime = new Date(record.endTime);
    const minutes = Math.round(
      (endTime.getTime() - startTime.getTime()) / 60000,
    ); // duration in minutes

    return {
      ...record,
      minutes,
      startTime: startTime.toISOString(),
      endTime: endTime.toISOString(),
    };
  });

  try {
    // Call the PostgreSQL RPC function with JSONB array and userId
    const { error } = await supabase.rpc(
      'upsert_sleep_sessions_with_metadata',
      { p_sleep: sleepInserts, p_user_id: userId },
    );

    if (error) {
      console.error('RPC upsert_sleep_sessions_with_metadata error:', error);
    } else {
      console.log('Sleep sessions and metadata upserted successfully via RPC.');
    }
  } catch (err) {
    console.error('Unexpected error calling RPC:', err);
  }
};

export const syncCaloriesToSupabase = async (
  calorieRecords: any[],
  userId: string,
  start: string,
  end: string,
) => {
  if (
    !userId ||
    !Array.isArray(calorieRecords) ||
    calorieRecords.length === 0
  ) {
    console.warn(
      '[syncCaloriesToSupabase] Missing userId or empty calorie records',
    );
    return;
  }

  console.log(calorieRecords, 'check396');

  try {
    const { data, error } = await supabase.rpc('sync_calories_data_from_json', {
      p_user_id: userId,
      p_calories: calorieRecords,
      p_start_filter: start,
      p_end_filter: end,
    });

    if (error) {
      console.error('[syncCaloriesToSupabase] RPC error:', error);
    } else {
      console.log(
        '[syncCaloriesToSupabase] Calories synced successfully:',
        data,
      );
    }
  } catch (err) {
    console.error('[syncCaloriesToSupabase] Unexpected error:', err);
  }
};

export const syncIosCaloriesToSupabase = async (
  calorieRecords: any[],
  userId: string,
  start: string,
  end: string,
) => {
  if (
    !userId ||
    !Array.isArray(calorieRecords) ||
    calorieRecords.length === 0
  ) {
    console.warn(
      '[syncCaloriesToSupabase] Missing userId or empty calorie records',
    );
    return;
  }

  console.log(JSON.stringify(calorieRecords), 'check478');

  try {
    const { data, error } = await supabase.rpc(
      'sync_ios_calories_data_from_json',
      {
        p_user_id: userId,
        p_calories: calorieRecords,
        p_start_filter: start,
        p_end_filter: end,
      },
    );

    if (error) {
      console.error('[syncCaloriesToSupabase] RPC error:', error);
    } else {
      console.log(
        '[syncCaloriesToSupabase] Calories synced successfully:',
        data,
      );
    }
  } catch (err) {
    console.error('[syncCaloriesToSupabase] Unexpected error:', err);
  }
};

export const getDashboardDataFromSupabase = async (
  userId: string,
  start: string,
  end: string,
) => {
  if (!userId || !start || !end) {
    console.warn('[getDashboardDataFromSupabase] Missing required parameters');
    return null;
  }

  try {
    const { data, error } = await supabase.rpc('get_user_dashboard_data', {
      p_user_id: userId,
      p_start_time: start,
      p_end_time: end,
    });

    if (error) {
      console.error('[getDashboardDataFromSupabase] RPC error:', error);
      return null;
    }

    console.log('[getDashboardDataFromSupabase] Dashboard data:', data);
    return data;
  } catch (err) {
    console.error('[getDashboardDataFromSupabase] Unexpected error:', err);
    return null;
  }
};

export const getDashBoardData = async (
  userId: string,
  start: string,
  end: string,
) => {
  try {
    const { data, error } = await supabase.rpc('get_user_activity_summary', {
      p_user_id: userId,
      p_start_time: start,
      p_end_time: end,
    });
    console.log(
      JSON.stringify(data || error),
      'get_user_activity_summary check data 54',
    );

    if (error) {
      console.error('[useDashboardData] RPC error:', error);
    } else {
      return data;
    }
  } catch (err: any) {
    console.error('[useDashboardData] Unexpected error:', err);
  }
};
