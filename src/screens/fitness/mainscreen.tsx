// MainScreen.js
import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  Alert,
  AppState,
  AppStateStatus,
  NativeModules,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import BrokenHealthKit, { HealthKitPermissions } from 'react-native-health';

import {
  initialize,
  RecordResult,
  requestPermission,
} from 'react-native-health-connect';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

import SimpleLineIcons from 'react-native-vector-icons/SimpleLineIcons';

import moment from 'moment';
import { useDispatch, useSelector } from 'react-redux';
import { FitnessScreenProps } from '../../../@types/navigation';
import {
  InfoIcon,
  KcaConsumedIcon1,
  KcaConsumedIcon2,
  KcaConsumedIcon3,
  LeftArrow,
} from '../../assets/images';
import { CommonCard } from '../../components/Card/Card';
import CheesyOmeletteInfo from '../../components/FitnessComponent/Cards/CheesyOmeletteInfo';
import EnergyMeter from '../../components/FitnessComponent/Cards/EnergyMeter';
import MotivationCard from '../../components/FitnessComponent/Cards/MotivationCard';
import SyncCard from '../../components/FitnessComponent/Cards/syncCard';
import { useFetchStepData } from '../../components/FitnessComponent/hooks/fetchStepData';
import { useStepSync } from '../../components/FitnessComponent/hooks/useStepSync';
import { useUserId } from '../../components/FitnessComponent/hooks/useUserId';
import CircularProgress from '../../components/FitnessComponent/progressCards/circularProgress';
import { storeNotificationMessage } from '../../redux/slices/appSlice';
import { RootState } from '../../redux/store';
import { fontStyles } from '../../theme/fonts';
import {
  getDataFromAsyncStorage,
  storeDataToAsyncStorage,
} from '../../utils/functions';
import { handleHealthDataError } from '../../utils/healthDataUtils';
import { fetchLastSyncedAt } from './supabaseClient';

interface FitnessData {
  steps: number;
  distance: number;
  calories: { active: number; total: number };
  heartRate: any[]; // you can type better if you want
  sleep: any[];
  activities: any[];
  weight: RecordResult<'Weight'>[]; // <-- explicitly declare this type
  bloodPressure: any[];
  bloodGlucose: any[];
}
interface Activity {
  carbs_grams: number | null;
  fat_grams: number | null;
  meal_name: string | null;
  protein_grams: number | null;
  time: string;
  total_calories: number;
  type: string;
}

interface UserHealthData {
  activities: Activity[];
  calorie_balance: number;
  calories_burned: number;
  calories_consumed: number;
  last_synced_at: string | null;
  steps: number;
  target_calories: number;
  target_intake_calories: number;
  target_steps: number;
  user_name: string;
}

const MainScreen: React.FC<FitnessScreenProps<'Home'>> = ({
  navigation,
  route,
}) => {
  let AppleHealthKit: typeof BrokenHealthKit | undefined;
  const dispatch = useDispatch();
  const scrollViewRef = useRef<ScrollView>(null);

  if (Platform.OS === 'ios') {
    AppleHealthKit = NativeModules.AppleHealthKit as typeof BrokenHealthKit;
    AppleHealthKit.Constants = BrokenHealthKit.Constants;
  }
  const { theme } = route.params;
  const colors = theme?.colors;
  const [authorized, setAuthorized] = useState(false);
  const [startDate, setStartDate] = useState<any>(null);
  const [endDate, setEndDate] = useState(new Date());
  const [sleepStartDate, setSleepStartDate] = useState<Date | null>(null);
  const [sleepEndDate, setSleepEndDate] = useState(new Date());

  const { loading: userFetchLoading, userId } = useUserId();
  // const [loading, setLoading] = useState(true);
  const [loadingCalories, setLoadingCalories] = useState(false);
  const [loadingSteps, setLoadingSteps] = useState(false);
  const [loadingWeight, setLoadingWeight] = useState(false);
  const [loadingSleep, setLoadingSleep] = useState(false);
  const appState = useRef<AppStateStatus>(AppState.currentState);
  // Combined loading if you want a single flag
  const loading =
    loadingSteps || loadingWeight || loadingSleep || loadingCalories;

  const [fitnessData, setFitnessData] = useState<FitnessData>({
    steps: 0,
    distance: 0,
    calories: { active: 0, total: 0 },
    heartRate: [],
    sleep: [],
    activities: [],
    weight: [],
    bloodPressure: [],
    bloodGlucose: [],
  });
  const { notificationMessage } = useSelector((state: RootState) => state.app);

  const [stepsFromDb, setStepsFromDb] = useState<any>(null);
  // const [caloriesFromDb, setCaloriesFromDb] = useState<any>(null);
  // const [weightFromDb, setWeightFromDb] = useState<any>(null);
  // const [sleepFromDb, setSleepFromDb] = useState<any>(null);
  const [weightStartDate, setWeightStartDate] = useState<Date | null>(null);
  const [weightEndDate, setWeightEndDate] = useState(new Date());
  const [isSyncing, setIsSyncing] = useState<boolean | null>(null);
  const [dashboardData, setDashboardData] = useState<UserHealthData | null>(
    null,
  );

  // const { data, error, loading: dashboardLoading } = useDashboardData(userId);
  const stepSync = useStepSync(setStepsFromDb, setDashboardData, userId);
  // const caloriesSync = useCaloriesSync(setCaloriesFromDb, userId);
  // const weightSync = useWeightSync(setWeightFromDb, userId);
  // const sleepSync = useSleepSync(setSleepFromDb, userId);

  const fetchStepData = useFetchStepData(setFitnessData, setStepsFromDb);
  // const fetchWeightData = useFetchWeightData(setFitnessData, setWeightFromDb);
  // const fetchSleepData = useFetchSleepData(setFitnessData, setSleepFromDb);

  useEffect(() => {
    const subscription = AppState.addEventListener(
      'change',
      async (nextAppState) => {
        if (
          appState.current.match(/inactive|background/) &&
          nextAppState === 'active'
        ) {
          if (authorized && userId) {
            try {
              setLoadingSleep(true);
              await stepSync();
            } catch (e) {
              const errorMessage = handleHealthDataError('AppState', e);
            } finally {
              setLoadingSleep(false);
            }
          }
        }
        appState.current = nextAppState;
      },
    );

    return () => subscription.remove();
  }, [authorized, userId, stepSync]);

  const setupHealthConnect = useCallback(
    async (loginUserId: string) => {
      try {
        if (userFetchLoading || !loginUserId) {
          return;
        }

        const isAvailable = await initialize();
        if (!isAvailable) {
          Alert.alert('Health Connect not available on this device');
          return;
        }

        await requestPermission([
          { accessType: 'read', recordType: 'Steps' },
          { accessType: 'read', recordType: 'Distance' },
          { accessType: 'read', recordType: 'HeartRate' },
          { accessType: 'read', recordType: 'SleepSession' },
          { accessType: 'read', recordType: 'ActiveCaloriesBurned' },
          { accessType: 'read', recordType: 'TotalCaloriesBurned' },
          { accessType: 'read', recordType: 'Weight' },
          { accessType: 'read', recordType: 'BloodPressure' },
          { accessType: 'read', recordType: 'BloodGlucose' },
        ]);

        const lastSyncedStr = await fetchLastSyncedAt(
          loginUserId as string,
          'steps',
        );
        const lastSynced = lastSyncedStr ? new Date(lastSyncedStr) : null;

        if (!lastSynced) {
          const todayStart = new Date();
          todayStart.setHours(0, 0, 0, 0);
          setStartDate(todayStart);
        } else {
          setStartDate(null);
        }
        setEndDate(new Date());

        const lastWeightSyncedStr = await fetchLastSyncedAt(
          loginUserId as string,
          'weight',
        );
        const lastWeightSynced = lastWeightSyncedStr
          ? new Date(lastWeightSyncedStr)
          : null;

        if (!lastWeightSynced) {
          const todayStart = new Date();
          todayStart.setHours(0, 0, 0, 0);
          setWeightStartDate(todayStart);
        } else {
          setWeightStartDate(null);
        }
        setWeightEndDate(new Date());

        const lastSleepSyncedStr = await fetchLastSyncedAt(
          loginUserId as string,
          'sleep',
        );
        const lastSleepSynced = lastSleepSyncedStr
          ? new Date(lastSleepSyncedStr)
          : null;

        if (!lastSleepSynced) {
          const todayStart = new Date();
          todayStart.setHours(0, 0, 0, 0);
          setSleepStartDate(todayStart);
        } else {
          setSleepStartDate(null);
        }
        setSleepEndDate(new Date());

        setAuthorized(true);
      } catch (e) {
        const errorMessage = handleHealthDataError('setupHealthConnect', e);
        Alert.alert('Health Connect Setup Error', 'Failed to connect');
      }
    },
    [userFetchLoading],
  );

  const useHealthKitSetup = useCallback(
    async (loginUserId: string) => {
      if (Platform.OS !== 'ios' || AppleHealthKit === undefined) return;

      try {
        if (userFetchLoading || !loginUserId) {
          return;
        }

        const permissions: HealthKitPermissions = {
          permissions: {
            read: [
              AppleHealthKit.Constants.Permissions.StepCount,
              AppleHealthKit.Constants.Permissions.DistanceWalkingRunning,
              AppleHealthKit.Constants.Permissions.HeartRate,
              AppleHealthKit.Constants.Permissions.SleepAnalysis,
              AppleHealthKit.Constants.Permissions.ActiveEnergyBurned,
              AppleHealthKit.Constants.Permissions.BasalEnergyBurned,
              AppleHealthKit.Constants.Permissions.Weight,
              AppleHealthKit.Constants.Permissions.BloodPressureSystolic,
              AppleHealthKit.Constants.Permissions.BloodPressureDiastolic,
              AppleHealthKit.Constants.Permissions.BloodGlucose,
            ],
            write: [],
          },
        };

        await new Promise<void>((resolve, reject) => {
          AppleHealthKit.initHealthKit(permissions, (error) => {
            if (error) {
              Alert.alert('Apple Health Setup Error', 'Failed to connect');
              reject(error);
            } else {
              resolve();
            }
          });
        });

        const lastSyncedStr = await fetchLastSyncedAt(loginUserId, 'steps');
        const lastSynced = lastSyncedStr ? new Date(lastSyncedStr) : null;

        if (!lastSynced) {
          const todayStart = new Date();
          todayStart.setHours(0, 0, 0, 0);
          setStartDate(todayStart);
        } else {
          setStartDate(null);
        }
        setEndDate(new Date());

        const lastWeightSyncedStr = await fetchLastSyncedAt(
          loginUserId,
          'weight',
        );
        const lastWeightSynced = lastWeightSyncedStr
          ? new Date(lastWeightSyncedStr)
          : null;

        if (!lastWeightSynced) {
          const todayStart = new Date();
          todayStart.setHours(0, 0, 0, 0);
          setWeightStartDate(todayStart);
        } else {
          setWeightStartDate(null);
        }
        setWeightEndDate(new Date());

        const lastSleepSyncedStr = await fetchLastSyncedAt(
          loginUserId,
          'sleep',
        );
        const lastSleepSynced = lastSleepSyncedStr
          ? new Date(lastSleepSyncedStr)
          : null;

        if (!lastSleepSynced) {
          const todayStart = new Date();
          todayStart.setHours(0, 0, 0, 0);
          setSleepStartDate(todayStart);
        } else {
          setSleepStartDate(null);
        }
        setSleepEndDate(new Date());

        setAuthorized(true);
      } catch (e) {
        const errorMessage = handleHealthDataError('useHealthKitSetup', e);
        Alert.alert('Apple Health Setup Error', errorMessage);
      }
    },
    [userFetchLoading],
  );
  async function syncData() {
    scrollViewRef.current?.scrollTo({ y: 0, animated: true });
    setIsSyncing(true);
    setTimeout(() => {
      setIsSyncing(false);
      setTimeout(() => {
        setIsSyncing(null);
      }, 4000);
    }, 4000);
    await stepSync();
  }
  // On mount: setup Health Connect on Android
  useEffect(() => {
    if (Platform.OS === 'android' && !userFetchLoading) {
      console.log(
        '[useEffect] Platform is Android, starting setupHealthConnect...',
        userFetchLoading,
        userId,
      );
      setupHealthConnect(userId as string);
    }
  }, [userId, setupHealthConnect, userFetchLoading]);

  // On mount: setup Health Connect on Android
  useEffect(() => {
    if (Platform.OS === 'ios' && !userFetchLoading) {
      console.log(
        '[useEffect] Platform is Ios, starting useHealthKitSetup...',
        userFetchLoading,
        userId,
      );
      useHealthKitSetup(userId as string);
    }
  }, [userId, useHealthKitSetup, userFetchLoading]);

  // Steps useEffect
  useEffect(() => {
    if (!authorized || !userId) {
      return;
    }
    console.log('1');

    (async () => {
      setLoadingSteps(true);
      setIsSyncing(true);
      setTimeout(() => {
        setIsSyncing(false);
        setTimeout(() => {
          setIsSyncing(null);
        }, 4000);
      }, 4000);
      try {
        syncData();
      } catch (e) {
        console.error('[Steps] Error during sync:', e);
      } finally {
        setLoadingSteps(false);
      }
    })();
  }, [authorized, userId, startDate, endDate, fetchStepData, stepSync]);

  const getGreeting = (): string => {
    const hour = moment().hour();

    if (hour < 12) return 'Good Morning';
    if (hour < 17) return 'Good Afternoon';
    if (hour < 20) return 'Good Evening';
    return 'Good Night';
  };

  const cal_def = dashboardData?.target_calorie_deficit;

  const goal = cal_def; // target deficit
  const balance = dashboardData?.calorie_balance || 0;
  const targetDeficit = -goal;
  const min = -cal_def * 3;
  const max = cal_def * 3;
  const current = balance;
  const target = -cal_def; // Optional: small milestone or use your own logic

  const statusText =
    balance > 0
      ? `You’ve consumed ${balance} more calories than you’ve burned so far today. A short walk or lighter meal can help bring you back on track. 💪`
      : balance < targetDeficit
        ? `You’re ${Math.abs(balance)} kcal below your target — consider a healthy snack or meal to stay fueled! 🥗`
        : `You’re on track — keep it going! ✅`;

  const targetLabel = `🎯 Target: ${goal} kcal deficit`;
  useEffect(() => {
    getDataFromAsyncStorage('dashboardData').then((data) => {
      if (!data) {
        if (dashboardData?.last_synced_at) {
          storeDataToAsyncStorage(
            'dashboardData',
            JSON.stringify(dashboardData),
          );
        }
      } else if (!dashboardData) {
        setDashboardData(JSON.parse(data));
      }
    });
  }, [dashboardData]);
  useEffect(() => {
    getDataFromAsyncStorage('isNewUser').then((data) => {
      if (data === 'true') {
        navigation.navigate('Settings', { newUser: true });
      }
    });
  }, []);
  return (
    <View
      style={[styles.container, { backgroundColor: colors?.primary_cream }]}
    >
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          paddingVertical: 16,
          paddingHorizontal: 20,
          borderBottomWidth: 0.7,
          borderBottomColor: colors?.grey_100,
        }}
      >
        <SimpleLineIcons
          name="refresh"
          onPress={syncData}
          size={20}
          color={route.params.theme?.colors?.grey_700}
        />
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <LeftArrow />
          <Text
            style={[
              fontStyles.Maison_600_18PX_21_6LH,
              { paddingHorizontal: 40 },
            ]}
          >
            Today
          </Text>
          <LeftArrow style={{ transform: [{ rotate: '180deg' }] }} />
        </View>
        <SimpleLineIcons
          name="settings"
          size={20}
          onPress={() => navigation.navigate('Settings')}
          color={route.params.theme?.colors?.grey_700}
        />
      </View>
      <ScrollView
        contentContainerStyle={{ padding: 20, paddingTop: 0 }}
        ref={scrollViewRef}
      >
        {(!(isSyncing === null) || loadingSleep) && (
          <SyncCard
            colors={colors}
            lastSyncedAt={
              dashboardData?.last_synced_at &&
              (moment
                .utc(dashboardData?.last_synced_at)
                .local()
                .format('hh:mm A') ||
                '')
            }
            isSyncing={isSyncing}
            onClose={() => {
              setIsSyncing(null);
            }}
          />
        )}
        <View>
          <Text
            style={[fontStyles.Maison_600_20PX_28LH, { marginVertical: 10 }]}
          >
            {`${getGreeting()}, ${dashboardData?.user_name || ''}!`}
          </Text>
          {notificationMessage && (
            <MotivationCard
              title={notificationMessage?.title}
              message={notificationMessage?.body}
              onClose={() => dispatch(storeNotificationMessage(null))}
            />
          )}

          <EnergyMeter
            target={target}
            current={current}
            min={min}
            max={max}
            text={statusText}
            targetLabel={targetLabel}
          />
          <CommonCard style={{ marginVertical: 10 }}>
            <Text style={fontStyles.Maison_600_18PX_24LH}>Summary</Text>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                padding: 5,
              }}
            >
              {[
                {
                  progress:
                    dashboardData?.calories_consumed &&
                    dashboardData?.target_intake_calories
                      ? Math.min(
                          Math.round(
                            (dashboardData.calories_consumed /
                              dashboardData.target_intake_calories) *
                              100,
                          ),
                          100,
                        )
                      : 0,
                  color: '#FF3F1F',
                  left:
                    dashboardData?.target_intake_calories &&
                    dashboardData?.calories_consumed
                      ? Math.round(
                          dashboardData.target_intake_calories -
                            dashboardData.calories_consumed,
                        )
                      : 0,
                  totalTask: Math.round(
                    dashboardData?.target_intake_calories || 0,
                  ),
                  completed: Math.round(dashboardData?.calories_consumed || 0),
                  icon: KcaConsumedIcon3,
                  title: 'Kcal consumed',
                },
                {
                  title: 'Energy burnt',
                  color: '#AC7950',
                  icon: KcaConsumedIcon2,
                  progress:
                    dashboardData?.calories_burned &&
                    dashboardData?.target_calories
                      ? Math.min(
                          Math.round(
                            (dashboardData.calories_burned /
                              dashboardData.target_calories) *
                              100,
                          ),
                          100,
                        )
                      : 0,
                  left:
                    dashboardData?.target_calories &&
                    dashboardData?.calories_burned
                      ? Math.round(
                          dashboardData.target_calories -
                            dashboardData.calories_burned,
                        )
                      : 0,
                  totalTask: Math.round(dashboardData?.target_calories || 0),
                  completed: Math.round(dashboardData?.calories_burned || 0),
                },
                {
                  title: 'Steps walked',
                  color: '#62656A',
                  icon: KcaConsumedIcon1,
                  progress:
                    dashboardData?.steps && dashboardData?.target_steps
                      ? Math.min(
                          Math.round(
                            (dashboardData.steps / dashboardData.target_steps) *
                              100,
                          ),
                          100,
                        )
                      : 0,
                  left:
                    dashboardData?.target_steps && dashboardData?.steps
                      ? Math.round(
                          dashboardData.target_steps - dashboardData.steps,
                        )
                      : 0,
                  totalTask: Math.round(dashboardData?.target_steps || 0),
                  completed: Math.round(dashboardData?.steps || 0),
                },
              ].map((item, index) => (
                <View
                  key={index}
                  style={{
                    flex: 1,
                    alignItems: 'center',
                    alignContent: 'center',
                  }}
                >
                  <CircularProgress
                    percentage={item?.progress}
                    progressSize={60}
                    label={item.title}
                    left={item?.left?.toString()}
                    total={item?.totalTask?.toString()}
                    consume={item?.completed?.toString()}
                    strokeColor={item?.color}
                  >
                    <item.icon height={25} width={25} />
                  </CircularProgress>
                </View>
              ))}
            </View>
          </CommonCard>
          <CommonCard style={{ marginVertical: 10, marginBottom: 0 }}>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}
            >
              <Text style={fontStyles.Maison_600_18PX_24LH}>
                Food & Activities
              </Text>
              <InfoIcon />
            </View>
            <View style={{ padding: 10, paddingBottom: 0 }}>
              {dashboardData?.activities.map((item, index) => (
                <View
                  key={index + 'matrics'}
                  style={{
                    flexDirection: 'row',
                    borderBottomWidth:
                      index === dashboardData?.activities?.length - 1 ? 0 : 0.5,
                    alignItems: 'center',
                    justifyContent: 'space-evenly',
                    paddingVertical: 12,
                    borderColor: '#DCD5D7',
                  }}
                >
                  <MaterialCommunityIcons
                    name={
                      {
                        meal: 'food',
                        step: 'walk',
                        sleep: 'sleep',
                        activity: 'run',
                        weight: 'scale-bathroom',
                        blood_pressure: 'heart',
                        blood_glucose: 'heart-pulse',
                        bmr: 'scale',
                      }[item?.type]
                    }
                    size={28}
                    style={{ flex: 1 }}
                    color="#FF3F1F"
                  />
                  <View style={{ flex: 4 }}>
                    <Text style={fontStyles.Maison_600_16PX_22LH}>
                      {
                        {
                          step: 'Walking',
                          meal: item?.meal_type,
                          bmr: 'Basal Metabolic Burn',
                        }[item?.type]
                      }
                    </Text>
                    {!['meal', 'bmr'].includes(item?.type) ? (
                      <Text
                        style={styles.boxLabel}
                      >{`${item?.distance_km} KM • ${item?.steps} Step • ${
                        moment.utc(item?.time).local().format('hh:mm A') || ''
                      }`}</Text>
                    ) : item?.type === 'bmr' ? (
                      <Text>
                        {moment.utc(item?.time).local().format('hh:mm A')}
                      </Text>
                    ) : (
                      <CheesyOmeletteInfo
                        colors={colors}
                        decs={item?.meal_name as string}
                        c={item?.carbs_grams}
                        f={item?.fat_grams}
                        p={item?.protein_grams}
                      />
                    )}
                  </View>
                  <View style={{ flex: 2.5 }}>
                    <Text
                      style={[
                        styles.boxLabel,
                        fontStyles.Maison_600_14PX_18LH,
                        {
                          textAlign: 'right',
                          color: ['bmr', 'step'].includes(item?.type)
                            ? colors?.red_600
                            : colors?.secondary_avocado,
                        },
                      ]}
                    >
                      {['bmr', 'step'].includes(item?.type) ? '-' : '+'}
                      {item?.total_calories} kcal
                    </Text>
                  </View>
                </View>
              ))}
            </View>
          </CommonCard>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  box: {
    backgroundColor: '#fff',
    borderRadius: 10,
    marginVertical: 10,
    padding: 16,
  },
  boxLabel: { color: '#555', fontSize: 14, marginTop: 8 },
  boxValue: { color: '#222', fontSize: 20, fontWeight: 'bold', marginTop: 4 },
  container: { flex: 1 },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  header: { color: '#fff', fontSize: 24, marginBottom: 5, textAlign: 'center' },
  loaderContainer: {
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
    marginTop: 100,
  },

  loaderText: { color: '#fff', marginTop: 10 },
});

export default MainScreen;
