import React, { useState } from 'react';
import {
  Al<PERSON>,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import { supabase } from './supabaseClient';
// import { sendLiveDataNotification } from '../api';

const initialLiveData = {
  StepsRecord: 200,
  StepsRecordToday: 200,
  DistanceRecord: 0.2,
  ActiveCaloriesBurnedRecord: 12,
  StartTime: new Date(),
  EndTime: new Date(new Date().getTime() - 30 * 60 * 1000),
  TotalCaloriesBurnedRecord: 2100,
  ExerciseSessionRecord: 2,
  HeartRateRecord: { resting: 58, average: 72, max: 142 },
  BloodPressureRecord: { systolic: 118, diastolic: 76 },
  BloodGlucoseRecord: 95,
  BodyFatRecord: 18.5,
  WeightRecord: 72.3,
  HeightRecord: 175.2,
  CurrentTime: new Date(),
  DailyGoals: { steps: 300, calories_burned: 20, sessions: 1 },
  WeeklyGoals: { steps: 2100, calories_burned: 140, sessions: 7 },
  WeeklyProgress: { steps: 600, calories_burned: 40, sessions: 2 },
  Streaks: {
    daily_steps_goal_met: 1,
    daily_calorie_goal_met: 1,
    exercise_sessions_streak: 1,
  },
};

const chunkArray = (arr: any[], size: number) => {
  const result = [];
  for (let i = 0; i < arr.length; i += size) {
    result.push(arr.slice(i, i + size));
  }
  return result;
};

const LiveDataForm = () => {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState(initialLiveData);
  const [datePickerVisible, setDatePickerVisible] = useState(false);
  const [activeDateField, setActiveDateField] = useState<
    'StartTime' | 'EndTime' | 'CurrentTime' | null
  >(null);

  const handleChange = (
    key: string,
    value: string | number,
    parentKey: string | null = null,
  ) => {
    if (parentKey) {
      setFormData((prev) => ({
        ...prev,
        [parentKey]: { ...prev[parentKey], [key]: value },
      }));
    } else {
      setFormData((prev) => ({ ...prev, [key]: value }));
    }
  };

  const handleSubmit = async () => {
    setLoading(true); // Start loading
    try {
      console.log('Submitted payload:', formData);
      const { data } = await supabase.auth.getUser();
      // await sendLiveDataNotification(data?.user?.id as string, formData);
      setFormData(initialLiveData);
      // Alert.alert('Success', 'Data submitted successfully');
    } catch (error) {
      Alert.alert('Error', 'Failed to submit data');
    } finally {
      setLoading(false); // Stop loading regardless of result
    }
  };

  const objectKeys = [
    'HeartRateRecord',
    'BloodPressureRecord',
    'DailyGoals',
    'WeeklyGoals',
    'WeeklyProgress',
    'Streaks',
  ];

  const openDatePicker = (field: 'StartTime' | 'EndTime' | 'CurrentTime') => {
    setActiveDateField(field);
    setDatePickerVisible(true);
  };

  const handleDateConfirm = (date: Date) => {
    if (activeDateField) {
      handleChange(activeDateField, date.toISOString());
    }
    setDatePickerVisible(false);
    setActiveDateField(null);
  };

  const handleCancel = () => {
    setDatePickerVisible(false);
    setActiveDateField(null);
  };

  return (
    <ScrollView contentContainerStyle={styles.container}>
      <View style={styles.formWrapper}>
        {/* <Text style={styles.header}>Fitness Health Data</Text> */}

        {/* Top-level primitive fields in 2-column rows */}
        {chunkArray(
          Object.entries(formData).filter(([key]) => !objectKeys.includes(key)),
          2,
        ).map((pair, idx) => (
          <View key={`primitive-${idx}`} style={styles.row}>
            {pair.map(([key, value]) => (
              <View key={key} style={styles.inputContainer}>
                <Text style={styles.subLabel}>{key}</Text>
                {key === 'StartTime' ||
                key === 'EndTime' ||
                key === 'CurrentTime' ? (
                  <TouchableOpacity
                    onPress={() => openDatePicker(key)}
                    style={[styles.input, { justifyContent: 'center' }]}
                  >
                    <Text style={{ color: 'white' }}>
                      {new Date(value).toLocaleString()}
                    </Text>
                  </TouchableOpacity>
                ) : (
                  <TextInput
                    style={styles.input}
                    value={String(value)}
                    onChangeText={(text) =>
                      handleChange(key, isNaN(+text) ? text : +text)
                    }
                  />
                )}
              </View>
            ))}
          </View>
        ))}

        {/* Object fields */}
        {objectKeys.map((section) => (
          <View key={section} style={{ marginBottom: 24 }}>
            <Text style={styles.sectionTitle}>{section}</Text>
            {chunkArray(Object.entries(formData[section]), 2).map(
              (pair, idx) => (
                <View key={`${section}-${idx}`} style={styles.row}>
                  {pair.map(([key, value]) => (
                    <View key={key} style={styles.inputContainer}>
                      <Text style={styles.subLabel}>{key}</Text>
                      <TextInput
                        style={styles.input}
                        value={String(value)}
                        onChangeText={(text) =>
                          handleChange(
                            key,
                            isNaN(+text) ? text : +text,
                            section,
                          )
                        }
                      />
                    </View>
                  ))}
                </View>
              ),
            )}
          </View>
        ))}

        <TouchableOpacity
          onPress={handleSubmit}
          disabled={loading}
          style={[styles.submitButton, loading && styles.disabledButton]}
        >
          <Text style={styles.submitButtonText}>
            {loading ? 'Submitting...' : 'Test'}
          </Text>
        </TouchableOpacity>
      </View>

      <DateTimePickerModal
        isVisible={datePickerVisible}
        mode="datetime"
        onConfirm={handleDateConfirm}
        onCancel={handleCancel}
      />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    backgroundColor: 'black',
    flexGrow: 1,
    paddingBottom: 40,
    paddingHorizontal: 24,
    paddingVertical: 20,
  },
  disabledButton: { backgroundColor: '#555' },
  formWrapper: { maxWidth: 800, width: '100%' },
  header: {
    color: 'white',
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  input: {
    backgroundColor: '#1e1e1e',
    borderColor: '#555',
    borderRadius: 6,
    borderWidth: 1,
    color: 'white',
    fontSize: 14,
    padding: 10,
  },
  inputContainer: { flex: 1 },
  row: {
    flexDirection: 'row',
    gap: 16,
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  sectionTitle: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    marginTop: 16,
  },
  subLabel: { color: '#aaa', fontSize: 12, marginBottom: 4 },
  submitButton: {
    alignItems: 'center',
    backgroundColor: '#007AFF',
    borderRadius: 8,
    marginTop: 10,
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  submitButtonText: { color: 'white', fontSize: 16, fontWeight: '600' },
});

export default LiveDataForm;
