// Login.js
import messaging from '@react-native-firebase/messaging';
import {
  GoogleSignin,
  GoogleSigninButton,
  statusCodes,
} from '@react-native-google-signin/google-signin';
import { useNavigation } from '@react-navigation/native';
import React, { useEffect, useState } from 'react';
import { Alert, StyleSheet, Text, View } from 'react-native';
import { ensureUserModuleSyncEntries, supabase } from './supabaseClient';

const Login = () => {
  const navigation = useNavigation();

  const [userInfo, setUserInfo] = useState<any>(null);
  const [error, setError] = useState<any>(null);
  const [checkingSession, setCheckingSession] = useState(true);
  // const [user, setUser] = useState(null);

  useEffect(() => {
    const checkSession = async () => {
      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (session?.user) {
        navigation.replace('Tabs');
      } else {
        console.log('error');
      }
      setCheckingSession(false);
    };

    checkSession();
  }, [navigation]);

  const signIn = async () => {
    try {
      await GoogleSignin.hasPlayServices();
      const usrInfo = await GoogleSignin.signIn();
      setUserInfo(usrInfo);
      setError(null);
      const fcmToken = await messaging().getToken();
      console.log('FCM Token:', fcmToken);
      console.log('info', `FCM Token: ${fcmToken}`, 'Login');

      // 1. Sign in to Supabase using Google idToken
      const { error: authError } = await supabase.auth.signInWithIdToken({
        provider: 'google',
        token: usrInfo.data?.idToken as string,
      });
      if (authError) {
        console.error('Supabase Auth Error:', authError);
        console.log(
          'error',
          `Supabase Auth Error: ${authError.message}`,
          'Login',
        );
        return;
      }
      // 2. Get authenticated user
      const {
        data: { user },
      } = await supabase.auth.getUser();
      // 3. Check if user exists in your users table
      const { data: existingUser, error: existingUserError } = await supabase
        .from('users')
        .select('*')
        .eq('id', user?.id)
        .single();
      if (existingUserError && existingUserError.code !== 'PGRST116') {
        // PGRST116 = no rows found, not an error
        console.error('Error checking existing user:', existingUserError);
        console.log(
          'error',
          `Error checking existing user: ${existingUserError.message}`,
          'Login',
        );
        return;
      }
      if (!existingUser) {
        // Insert user with last_synced_at = null initially
        const { error: insertError } = await supabase.from('users').insert({
          id: user?.id,
          email: user?.email,
          full_name:
            user?.user_metadata?.full_name || usrInfo?.data?.user?.name,
          created_at: new Date().toISOString(),
          // last_synced_at: null,
          fcm_token: fcmToken, // ✅ Include FCM token here
        });
        if (insertError) {
          console.error('Error inserting profile:', insertError);
          console.log(
            'error',
            `Error inserting profile: ${insertError.message}`,
            'Login',
          );

          return;
        } else {
          console.log('New user and fitness profile created');
          console.log('info', 'New user and fitness profile created', 'Login');

          console.log(':white_tick: New profile created', existingUser);
        }
      } else {
        console.log(':repeat: User already exists');
        console.log('info', 'User already exists', 'Login');
        // Update FCM token for existing user
        const { data: updatedData, error: updateError } = await supabase
          .from('users')
          .update({ fcm_token: fcmToken })
          .eq('id', user?.id)
          .select();
        if (updateError) {
          console.error('Error updating FCM token:', updateError);
          console.log(
            'error',
            `Error updating FCM token: ${updateError.message}`,
            'Login',
          );
        } else {
          console.log('FCM token updated for existing user:', updatedData);
          console.log(
            'info',
            `FCM token updated for existing user: ${JSON.stringify(updatedData)}`,
            'Login',
          );
        }
      }

      const { data: existingUserFitnessData } = await supabase
        .from('user_fitness_profiles')
        .select('id')
        .eq('user_id', user?.id)
        .single();

      if (!existingUserFitnessData) {
        // Insert default fitness profile data into 'user_fitness_profiles'
        const defaultFitnessData = {
          daily_goals_calories_burned: 500,
          daily_goals_steps: 7000,
          height: 178,
          gender: 'Male',
          date_of_birth: new Date('2000-01-01'), // or default value
          created_at: new Date().toISOString(),
          occupation: 'Software Engineer / Tech Entrepreneur',
          fitness_level: 'Beginner',
          experience_years: 0,
          past_experience: 'Mostly sedentary with occasional walks',
          limitations: 'Mild lower back pain',
          goals_set: 'Yes',
          primary_goal: 'Fat loss',
          secondary_goals: 'Improve flexibility, posture, and sleep quality',
          preferred_exercises:
            'Walking, light mobility drills, bodyweight exercises, swimming',
          availability: '2–3 days/week, mornings preferred',
          duration: '45–60 minutes',
          pain_points: 'Consistency and diet adherence due to work schedule',
          activity_level: 'Slightly Active',
          target_weight: 65,
          target_calorie: 2250,
          weight: 85,
          target_intake_calorie: 2250,
        };

        const { error: fitnessInsertError } = await supabase
          .from('user_fitness_profiles')
          .insert({ user_id: user?.id, ...defaultFitnessData });

        if (fitnessInsertError) {
          console.error('Error inserting fitness profile:', fitnessInsertError);
          console.log(
            'error',
            `Error inserting fitness profile: ${fitnessInsertError.message}`,
            'Login',
          );

          return;
        }

        const response = await fetch(
          'https://ai-coach.delicut.click/persona-generation',
          {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              user_id: user?.id,
              user_data: { ...defaultFitnessData },
            }),
          },
        );

        console.log(response, 'check121');
        console.log(
          'info',
          `Persona generation response: ${response.status}`,
          'Login',
        );
      }

      console.log('info', 'Checking for user module sync entry...', 'Login');
      const { data: existing } = await supabase
        .from('user_module_sync')
        .select('id')
        .eq('user_id', user?.id)
        .eq('module_name', 'steps')
        .single();
      if (!existing) {
        console.log(
          'info',
          'Creating initial user module sync entry...',
          'Login',
        );
        const { error: insertError } = await supabase
          .from('user_module_sync')
          .insert({
            user_id: user?.id,
            module_name: 'steps',
            last_synced_at: null,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          });
        if (insertError) {
          console.error(
            'Error inserting initial user_module_sync entry:',
            insertError,
          );
        }
      }
      await ensureUserModuleSyncEntries(user?.id as string);
      console.log(
        'info',
        'All user module sync entries ensured successfully',
        'Login',
      );
      // Navigate to main app tab
      navigation?.replace('Tabs');
    } catch (err: any) {
      console.log(err, 'test123');
      setError(err);
      if (err.code === statusCodes.SIGN_IN_CANCELLED) {
        console.log('User cancelled the login flow');
      } else if (err.code === statusCodes.IN_PROGRESS) {
        console.log('Operation is in progress already');
      } else if (err.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
        Alert.alert(
          'Google Play Services Error',
          'Please update or enable Google Play Services to use Google Sign-In.',
        );
      } else {
        Alert.alert(
          'Sign In Error',
          'An unexpected error occurred. Please try again.',
        );
      }
    }
  };

  const signOut = async () => {
    try {
      console.log('info', 'Starting sign-out process...', 'Login');
      await GoogleSignin.revokeAccess();
      console.log('info', 'Google access revoked', 'Login');
      await GoogleSignin.signOut();
      setUserInfo(null);
      setError(null);
      Alert.alert('Signed Out', 'You have been signed out.');
      console.log('info', 'Google signOut Completed', 'Login');
    } catch (err) {
      console.error('Failed to sign out:', err);
      setError(err);
    }
  };

  if (checkingSession) {
    return (
      <View>
        <Text>Checking session...</Text>
      </View>
    );
  }

  return (
    <View style={styles.containerCentered}>
      <GoogleSigninButton
        style={styles.googleButton}
        size={GoogleSigninButton.Size.Wide}
        color={GoogleSigninButton.Color.Dark}
        onPress={signIn}
      />
      {/* )} */}
      {error && (
        <Text style={styles.errorText}>
          Error: {error.message} (Code: {error.code})
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  containerCentered: {
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
    padding: 20,
  },
  errorText: { color: 'red', marginTop: 10 },
  googleButton: { height: 48, width: 192 },
  userImage: { borderRadius: 50, height: 100, width: 100 },
});

export default Login;
