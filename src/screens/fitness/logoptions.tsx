import { NavigationProp, useNavigation } from '@react-navigation/native';
import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import AntDesign from 'react-native-vector-icons/AntDesign';
// To this (use the correct type from your navigation.d.ts)
import { RootFitnessStackParamList } from '../../../@types/navigation';
import Cameraplus from '../../assets/images/svgs/Cameraplus.svg';
import Star from '../../assets/images/svgs/StarIos.svg';
import Weight from '../../assets/images/svgs/Weight.svg';
import { GorhomBottomSheet } from '../../components/BottomSheet/BottomSheet';
import { fontStyles } from '../../theme/fonts';

interface Props {
  open: boolean;
  onClose: () => void;
}

const LogOptionsSheet: React.FC<Props> = ({ open, onClose }) => {
  const navigation = useNavigation<NavigationProp<RootFitnessStackParamList>>();  return (
    <GorhomBottomSheet
      sheetOpen={open}
      sheetClose={onClose}
      title="What would you like to log?"
      closeOnBackdrop
      closeOnPressBack
    >
      <View style={styles.container}>
        <View style={styles.topRow}>
          <LinearGradient colors={['#F9F4ED', '#F9F4ED']} style={styles.card}>
            <TouchableOpacity
              onPress={() => {
                navigation.navigate('Capture');
                onClose();
              }}
              activeOpacity={0.85}
              style={styles.touchableArea}
            >
              <Cameraplus width={22} height={22} />
              <Text style={[fontStyles?.Maison_500_16PX_22LH, styles.textSize]}>
                Scan your meal
              </Text>
            </TouchableOpacity>
          </LinearGradient>
          <LinearGradient colors={['#F9F4ED', '#F9F4ED']} style={styles.card}>
            <TouchableOpacity
              onPress={() => {
                navigation.navigate('TextMealDetection');
                onClose();
              }}
              activeOpacity={0.85}
              style={styles.touchableArea}
            >
              <Star width={22} height={22} />
              <Text style={[fontStyles?.Maison_500_16PX_22LH, styles.textSize]}>
                Type your meal
              </Text>
            </TouchableOpacity>
          </LinearGradient>
          <LinearGradient colors={['#F9F4ED', '#F9F4ED']} style={styles.card}>
            <TouchableOpacity
              onPress={() => {
                navigation.navigate('TextManualMealDetection');
                onClose();
              }}
              activeOpacity={0.85}
              style={styles.touchableArea}
            >
              <AntDesign name="plus" size={20} color="#f44336" />
              <Text style={[fontStyles?.Maison_500_16PX_22LH, styles.textSize]}>
                Enter manually
              </Text>
            </TouchableOpacity>
          </LinearGradient>
        </View>
        <View style={styles.bottomRow}>
          <LinearGradient
            colors={['#F9F4ED', '#F9F4ED']}
            style={styles.singleCard}
          >
            <TouchableOpacity
              onPress={() => console.log('Add clicked')}
              activeOpacity={0.85}
              style={styles.touchableArea}
            >
              <Weight width={22} height={22} />
              <Text style={[fontStyles?.Maison_500_16PX_22LH, styles.textSize]}>
                {' '}
                Add{'\n'}Weight
              </Text>
            </TouchableOpacity>
          </LinearGradient>
        </View>
      </View>
    </GorhomBottomSheet>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 10,
    paddingTop: 4,
  },
  touchableArea: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    borderRadius: 12,
  },

  textSize: {
    textAlign: 'center',
    marginTop: 10,
  },
  topRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  bottomRow: {
    alignItems: 'flex-start',
  },
  card: {
    width: '30%',
    borderRadius: 12,
    paddingVertical: 20,
    // paddingHorizontal: 5,
    alignItems: 'center',
    justifyContent: 'center',
  },
  singleCard: {
    width: '30%',
    borderRadius: 12,
    paddingVertical: 20,
    paddingHorizontal: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default LogOptionsSheet;
