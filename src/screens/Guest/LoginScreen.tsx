import { NavigationProp, RouteProp } from '@react-navigation/native';
import React, { useEffect, useRef, useState } from 'react';
import {
  Animated,
  Dimensions,
  ImageBackground,
  KeyboardAvoidingView,
  Platform,
  View,
} from 'react-native';
import { enableScreens } from 'react-native-screens';
import { useSelector } from 'react-redux';
import {
  GuestScreenProps,
  RootGuestStackParamList,
} from '../../../@types/navigation';
import { LoginScreenBackground, Logo } from '../../assets/images';
import { WelcomeCard } from '../../components/login_screen/cards/WelcomeCard';
import { RootState } from '../../redux/store';
import { globalStyles } from '../../theme/styles/globalStyles';

enableScreens();
const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

const LoginScreen: React.FC<GuestScreenProps<'Login'>> = ({
  navigation,
  route,
}: {
  navigation: NavigationProp<RootGuestStackParamList>;
  route: RouteProp<RootGuestStackParamList, 'Login'>;
}) => {
  const { isRTL } = useSelector((state: RootState) => state.app);
  const [animationDone, setAnimationDone] = useState(false);
  const translateY = useRef(new Animated.Value(SCREEN_HEIGHT / 2 - 50)).current;
  const translateX = useRef(new Animated.Value(SCREEN_WIDTH / 2 - 50)).current; // adjust based on logo size
  const scale = useRef(new Animated.Value(3)).current;
  useEffect(() => {
    const timeout = setTimeout(() => {
      Animated.parallel([
        Animated.timing(translateY, {
          toValue: 0,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(translateX, {
          toValue: isRTL ? SCREEN_WIDTH - 100 : 0,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(scale, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
      ]).start(() => setAnimationDone(true));
    }, 2000);
    return () => clearTimeout(timeout);
  }, [isRTL]);
  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={globalStyles.flex1}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
    >
      <ImageBackground
        source={LoginScreenBackground}
        style={[globalStyles.flex1, globalStyles.justifyContentSpaceBetween]}
        resizeMode="cover"
      >
        <View
          style={[globalStyles.flex1, { backgroundColor: 'rgba(0,0,0,0.5)' }]}
        >
          <Animated.View
            style={{
              position: 'absolute',
              transform: [{ translateX }, { translateY }, { scale }],
              margin: 10,
            }}
          >
            <Logo />
          </Animated.View>

          {animationDone && <WelcomeCard />}
        </View>
      </ImageBackground>
    </KeyboardAvoidingView>
  );
};

export default LoginScreen;
