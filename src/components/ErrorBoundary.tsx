import React, { FC } from 'react';
import { ErrorBoundary, FallbackProps } from 'react-error-boundary';
import { Button, StyleSheet, Text, View } from 'react-native';

const ErrorFallback: FC<FallbackProps> = ({ error, resetErrorBoundary }) => (
  <View style={styles.fallbackContainer}>
    <Text style={styles.errorText}>Something went wrong:</Text>
    <Text style={styles.errorDetails}>{error?.message}</Text>
    <Button title="Try Again" onPress={resetErrorBoundary} />
  </View>
);
const logError = (error: Error, info: React.ErrorInfo) => {
  console.error('Logged error:', error, info);
};

const ErrorWrapper: FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <ErrorBoundary FallbackComponent={ErrorFallback} onError={logError}>
      {children}
    </ErrorBoundary>
  );
};
export default ErrorWrapper;
const styles = StyleSheet.create({
  errorDetails: { color: 'red', fontSize: 14, marginBottom: 20 },
  errorText: { fontSize: 18, fontWeight: 'bold', marginBottom: 10 },
  fallbackContainer: {
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
  },
});
