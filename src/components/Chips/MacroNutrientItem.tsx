// import React, { FC } from 'react';
// import { StyleProp, Text, View, ViewStyle } from 'react-native';
// import { useSelector } from 'react-redux';
// import { RootState } from '../../redux/store';
// import { useTheme } from '../../theme';
// import { fontStyles } from '../../theme/fonts';
// import { macrosCardStyles } from '../../theme/styles/cardStyles';
// import { globalStyles } from '../../theme/styles/globalStyles';
// import MacroProgressBar from '../ProgressBars/MacroProgressBar';

// interface MacroNutrientItemProps {
//   title: string;
//   value: string;
//   macroProgressBarStyle?: StyleProp<ViewStyle>;
// }

// const MacroNutrientItem: FC<MacroNutrientItemProps> = ({
//   title,
//   value,
//   macroProgressBarStyle,
// }) => {
//   const { isRTL } = useSelector((state: RootState) => state?.app);
//   const colors = useTheme();
//   return (
//     <View
//       style={[
//         isRTL ? globalStyles.rowReverse : globalStyles.row,
//         macrosCardStyles.macrosView,
//       ]}
//     >
//       <View style={macrosCardStyles.macrosViewDetails}>
//         <Text
//           style={[
//             isRTL ? globalStyles.textRight : globalStyles.textLeft,
//             fontStyles.Maison_500_12PX_16LH,
//             { color: colors?.green },
//           ]}
//         >
//           {title}
//         </Text>

//         <View style={macrosCardStyles.macrosViewIndicatorWrapper}>
//           <MacroProgressBar style={macroProgressBarStyle} />
//           <Text
//             style={[
//               isRTL ? globalStyles.textRight : globalStyles.textLeft,
//               fontStyles.Maison_500_12PX_16LH,
//               { color: colors?.green },
//             ]}
//           >
//             {value}
//           </Text>
//         </View>
//       </View>
//     </View>
//   );
// };

// export default MacroNutrientItem;

import React, { FC } from 'react';
import { StyleProp, Text, TextStyle, View, ViewStyle } from 'react-native';
import { useSelector } from 'react-redux';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontStyles } from '../../theme/fonts';
import { macrosCardStyles } from '../../theme/styles/cardStyles';
import { globalStyles } from '../../theme/styles/globalStyles';
import MacroProgressBar from '../ProgressBars/MacroProgressBar';

interface MacroNutrientItemProps {
  title: string;
  value: string;
  titleStyle?: StyleProp<TextStyle>;
  valueStyle?: StyleProp<TextStyle>;
  macroProgressBarStyle?: StyleProp<ViewStyle>;
}

const MacroNutrientItem: FC<MacroNutrientItemProps> = ({
  title,
  value,
  macroProgressBarStyle,
  valueStyle,
  titleStyle,
}) => {
  const { isRTL } = useSelector((state: RootState) => state?.app);
  const colors = useTheme();
  return (
    <View
      style={[
        isRTL ? globalStyles.rowReverse : globalStyles.row,
        macrosCardStyles.macrosView,
      ]}
    >
      <View style={macrosCardStyles.macrosViewDetails}>
        <Text
          style={[
            isRTL ? globalStyles.textRight : globalStyles.textLeft,
            fontStyles.Maison_500_14PX_18LH,
            { color: colors?.primary_spinach },
            titleStyle,
          ]}
        >
          {title}
        </Text>

        <View style={macrosCardStyles.macrosViewIndicatorWrapper}>
          <MacroProgressBar style={macroProgressBarStyle} />
          <Text
            style={[
              isRTL ? globalStyles.textRight : globalStyles.textLeft,
              fontStyles.Maison_600_14PX_18LH,
              { color: colors?.primary_spinach },
              valueStyle,
            ]}
          >
            {value}
          </Text>
        </View>
      </View>
    </View>
  );
};

export default MacroNutrientItem;
