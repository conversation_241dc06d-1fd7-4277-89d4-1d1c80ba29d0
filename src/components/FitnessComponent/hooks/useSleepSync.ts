import { useCallback } from 'react';
import { readRecords } from 'react-native-health-connect';
import {
  syncSleepSessionsToSupabase,
  updateLastSyncedAt,
} from '../../../screens/fitness/supabaseClient';
import { getSleepDataFromDb } from '../../../utils/fitnessFunctions';
import {
  getTodayDateRange,
  handleHealthDataError,
} from '../../../utils/healthDataUtils';

export function useSleepSync(
  setSleepFromDb: (sleep: number | null) => void,
  userId: string | null,
) {
  return useCallback(async () => {
    if (!userId) {
      console.warn('[useSleepSync] No userId provided');
      return;
    }

    try {
      const { startISOString, endISOString } = getTodayDateRange();

      const sleepRes = await readRecords('SleepSession', {
        timeRangeFilter: {
          operator: 'between',
          startTime: startISOString,
          endTime: endISOString,
        },
      });

      if (sleepRes.records?.length > 0) {
        await syncSleepSessionsToSupabase(sleepRes.records, userId);
        await updateLastSyncedAt(userId, 'sleep', endISOString);
      }

      const { data, error } = await getSleepDataFromDb(userId as string);
      setSleepFromDb(error || !data?.length ? null : (data[0]?.minutes ?? 0));
    } catch (e) {
      handleHealthDataError('useSleepSync', e);
    }
  }, [setSleepFromDb, userId]);
}
