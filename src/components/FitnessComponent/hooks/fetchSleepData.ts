import { useCallback } from 'react';
import { readRecords } from 'react-native-health-connect';
import {
  getUserId,
  syncSleepSessionsToSupabase,
  updateLastSyncedAt,
} from '../../../screens/fitness/supabaseClient';
import { getSleepDataFromDb } from '../../../utils/fitnessFunctions';
import { handleHealthDataError } from '../../../utils/healthDataUtils';

export function useFetchSleepData(setFitnessData: any, setSleepFromDb: any) {
  return useCallback(
    async (sleepStartDate: Date | null, sleepEndDate: Date) => {
      if (
        !(sleepStartDate instanceof Date) ||
        !(sleepEndDate instanceof Date) ||
        sleepStartDate >= sleepEndDate
      ) {
        console.warn('[useFetchSleepData] Invalid time range');
        return;
      }

      try {
        const sleepRes = await readRecords('SleepSession', {
          timeRangeFilter: {
            operator: 'between',
            startTime: sleepStartDate.toISOString(),
            endTime: sleepEndDate.toISOString(),
          },
        });

        setFitnessData((fd: any) => ({ ...fd, sleep: sleepRes.records }));

        const userId = await getUserId();
        await syncSleepSessionsToSupabase(sleepRes.records, userId as string);

        await updateLastSyncedAt(
          userId as string,
          'sleep',
          sleepEndDate.toISOString(),
        );
        const { data, error } = await getSleepDataFromDb(userId as string);
        setSleepFromDb(error || !data?.length ? null : (data[0]?.minutes ?? 0));
      } catch (e) {
        handleHealthDataError('useFetchSleepData', e);
      }
    },
    [setFitnessData, setSleepFromDb],
  );
}
