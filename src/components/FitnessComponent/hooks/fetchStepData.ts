import { useCallback } from 'react';
import { readRecords } from 'react-native-health-connect';
import {
  getUserId,
  syncStepsToSupabase,
  updateLastSyncedAt,
} from '../../../screens/fitness/supabaseClient';
import { getStepsDataFromDb } from '../../../utils/fitnessFunctions';
import {
  getTodayDateRange,
  handleHealthDataError,
} from '../../../utils/healthDataUtils';

export function useFetchStepData(setFitnessData: any, setStepsFromDb: any) {
  return useCallback(
    async (startDate: Date | null, endDate: Date) => {
      if (!startDate || !endDate || startDate >= endDate) {
        console.warn('[useFetchStepData] Invalid time range');
        return;
      }

      try {
        const stepsRes = await readRecords('Steps', {
          timeRangeFilter: {
            operator: 'between',
            startTime: startDate.toISOString(),
            endTime: endDate.toISOString(),
          },
        });

        const steps = Array.isArray(stepsRes?.records)
          ? stepsRes.records.reduce((t, r) => t + (r.count ?? 0), 0)
          : 0;

        setFitnessData((fd: any) => ({ ...fd, steps }));

        const userId = await getUserId();
        const { start, end, startISOString, endISOString } =
          getTodayDateRange();

        await syncStepsToSupabase(
          stepsRes.records,
          userId as string,
          startISOString,
          endISOString,
        );

        await updateLastSyncedAt(
          userId as string,
          'steps',
          endDate.toISOString(),
        );

        const { data, error } = await getStepsDataFromDb(
          userId as string,
          startISOString,
          endISOString,
        );

        setStepsFromDb(error ? 0 : (data?.[0]?.total_steps ?? 0));
      } catch (e) {
        handleHealthDataError('useFetchStepData', e);
      }
    },
    [setFitnessData, setStepsFromDb],
  );
}
