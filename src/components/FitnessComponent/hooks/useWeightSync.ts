import { useCallback } from 'react';
import { readRecords } from 'react-native-health-connect';
import {
  syncWeightToSupabase,
  updateLastSyncedAt,
} from '../../../screens/fitness/supabaseClient';
import { getWeightDataFromDb } from '../../../utils/fitnessFunctions';
import {
  getTodayDateRange,
  handleHealthDataError,
} from '../../../utils/healthDataUtils';

export function useWeightSync(
  setWeightFromDb: (weight: number | null) => void,
  userId: string | null,
) {
  return useCallback(async () => {
    if (!userId) {
      console.warn('[useWeightSync] No userId provided');
      return;
    }

    try {
      const { startISOString, endISOString } = getTodayDateRange();

      const weightRes = await readRecords('Weight', {
        timeRangeFilter: {
          operator: 'between',
          startTime: startISOString,
          endTime: endISOString,
        },
      });

      if (weightRes.records?.length > 0) {
        await syncWeightToSupabase(weightRes.records, userId);
        await updateLastSyncedAt(userId, 'weight', endISOString);
      }

      const { data, error } = await getWeightDataFromDb(userId as string);
      setWeightFromDb(
        error || !data?.length ? null : (data[0]?.weight_kg ?? 0),
      );
    } catch (e) {
      handleHealthDataError('useWeightSync', e);
    }
  }, [setWeightFromDb, userId]);
}
