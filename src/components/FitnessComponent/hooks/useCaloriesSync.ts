import { useCallback } from 'react';
import { NativeModules } from 'react-native';
import BrokenHealthKit from 'react-native-health';
import { readRecords } from 'react-native-health-connect'; // Android only
import {
  syncIosCaloriesToSupabase,
  updateLastSyncedAt,
} from '../../../screens/fitness/supabaseClient';
import { getCaloriesDataFromDb } from '../../../utils/fitnessFunctions';
import {
  getTodayDateRange,
  handleHealthDataError,
  isAndroid,
  isIOS,
} from '../../../utils/healthDataUtils';

// const NativeModules = require('react-native').NativeModules;
const AppleHealthKit = NativeModules.AppleHealthKit as typeof BrokenHealthKit;

export function useCaloriesSync(
  setCaloriesFromDb: (calories: number) => void,
  userId: string | null,
) {
  return useCallback(async () => {
    if (!userId) {
      console.warn('[useCaloriesSync] No userId provided');
      return;
    }

    try {
      const { startISOString, endISOString } = getTodayDateRange();
      let calorieRecords: any[] = [];

      if (isAndroid) {
        const caloriesRes = await readRecords('TotalCaloriesBurned', {
          timeRangeFilter: {
            operator: 'between',
            startTime: startISOString,
            endTime: endISOString,
          },
        });

        calorieRecords = caloriesRes?.records || [];
      } else if (isIOS) {
        const options = {
          startDate: startISOString,
          endDate: endISOString,
        };

        calorieRecords = await new Promise<AppleHealthKit.StatisticsResponse[]>(
          (resolve, reject) => {
            AppleHealthKit.getActiveEnergyBurned(options, (err, results) => {
              if (err) {
                reject(err);
              } else {
                resolve(results);
              }
            });
          },
        );
      }

      if (calorieRecords.length > 0) {
        await syncIosCaloriesToSupabase(
          calorieRecords,
          userId,
          startISOString,
          endISOString,
        );
        await updateLastSyncedAt(userId, 'Calories', endISOString);
      }

      const { data, error } = await getCaloriesDataFromDb(
        userId,
        startISOString,
        endISOString,
      );

      const totalCalories = error ? 0 : (data ?? 0);
      setCaloriesFromDb(totalCalories);
    } catch (e) {
      handleHealthDataError('useCaloriesSync', e);
    }
  }, [setCaloriesFromDb, userId]);
}
