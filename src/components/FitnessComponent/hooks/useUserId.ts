import { useEffect, useState } from 'react';
import { supabase } from '../../../screens/fitness/supabaseClient';

let cachedUserId: string | null = null;

export function useUserId() {
  const [userId, setUserId] = useState<string | null>(cachedUserId);
  const [loading, setLoading] = useState(!cachedUserId);

  useEffect(() => {
    if (cachedUserId) {
      // Already cached, no need to fetch
      return;
    }

    let isMounted = true;

    async function fetchUserId() {
      try {
        const { data } = await supabase.auth.getUser();
        if (isMounted) {
          cachedUserId = data?.user?.id ?? null;
          setUserId(cachedUserId);
          setLoading(false);
        }
      } catch (e) {
        if (isMounted) {
          setUserId(null);
          setLoading(false);
        }
        console.error('Failed to fetch user ID', e);
      }
    }

    fetchUserId();

    return () => {
      isMounted = false;
    };
  }, []);

  return { userId, loading };
}
