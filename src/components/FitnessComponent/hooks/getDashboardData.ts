import { useEffect, useState } from 'react';
import { supabase } from '../../../utils/supabaseClient';

type DashboardData = {
  steps: number;
  calories_burned: number;
  calories_consumed: number;
  target_steps: number;
  target_calories: number;
  calorie_balance: number;
  last_synced_at: string | null;
  activities: Array<{
    type: 'meal' | 'step';
    time: string;
    meal_name?: string;
    total_calories?: number;
    protein_grams?: number;
    carbs_grams?: number;
    fat_grams?: number;
  }>;
};


export const useDashboardData = (userId : string | null) => {
  console.log(userId, 'userId30');

  const [data, setData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!userId) {
      setLoading(false);
      setError('Missing user ID');
      return;
    }

    const fetchDashboardData = async () => {
      setLoading(true);
      setError(null);

    const start = new Date();
    start.setHours(0, 0, 0, 0);

    const now = new Date();
    now.setHours(23, 59, 59, 999);

   console.log(start,'check both time',now);

      try {
        const { data, error } = await supabase.rpc('get_user_dashboard_data', {
          p_user_id: userId,
          p_start_time: start,
          p_end_time: now,
        });
        console.log(data || error,'check data 54');

        if (error) {
          console.error('[useDashboardData] RPC error:', error);
          setError(error.message);
          setData(null);
        } else {
          setData(data);
        }
      } catch (err: any) {
        console.error('[useDashboardData] Unexpected error:', err);
        setError(err.message);
        setData(null);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [userId]);

  return { data, loading, error };
};
