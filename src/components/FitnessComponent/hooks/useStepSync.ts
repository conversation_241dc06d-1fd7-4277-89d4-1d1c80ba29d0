import { useCallback } from 'react';
import { NativeModules } from 'react-native';
import BrokenHealthKit from 'react-native-health'; // iOS
import { readRecords } from 'react-native-health-connect'; // Android only
import {
  getDashBoardData,
  syncIosStepsToSupabase,
  syncStepsToSupabase,
  updateLastSyncedAt,
  updateUserTimeZone,
} from '../../../screens/fitness/supabaseClient';
import { getStepsDataFromDb } from '../../../utils/fitnessFunctions';
import {
  getTodayDateRange,
  handleHealthDataError,
  isAndroid,
  isIOS,
} from '../../../utils/healthDataUtils';

// const NativeModules = require('react-native').NativeModules;
const AppleHealthKit = NativeModules.AppleHealthKit as typeof BrokenHealthKit;

export function useStepSync(
  setStepsFromDb: (steps: number) => void,
  setDashboardData: (steps: number) => void,
  userId: string | null,
) {
  return useCallback(async () => {
    if (!userId) {
      console.warn('[useStepSync] No userId provided');
      return;
    }

    const { start, end, startISOString, endISOString } = getTodayDateRange();

    try {
      if (isAndroid) {
        const stepsRes = await readRecords('Steps', {
          timeRangeFilter: {
            operator: 'between',
            startTime: startISOString,
            endTime: endISOString,
          },
        });

        if (stepsRes.records?.length > 0) {
          await syncStepsToSupabase(
            stepsRes.records,
            userId,
            startISOString,
            endISOString,
          );
        }
      }

      if (isIOS) {
        const options = {
          startDate: startISOString,
          endDate: endISOString,
        };

        const stepSamples = await new Promise<
          AppleHealthKit.StatisticsResponse[]
        >((resolve, reject) => {
          AppleHealthKit.getDailyStepCountSamples(options, (err, results) => {
            if (err) {
              reject(err);
            } else {
              resolve(results);
            }
          });
        });

        if (stepSamples.length > 0) {
          await syncIosStepsToSupabase(
            stepSamples,
            userId,
            startISOString,
            endISOString,
          );
        }
      }

      await updateLastSyncedAt(userId, 'steps', new Date().toISOString());
      await updateUserTimeZone(userId);

      const dashboardData = await getDashBoardData(
        userId,
        startISOString,
        endISOString,
      );

      setDashboardData(dashboardData);

      const { data, error } = await getStepsDataFromDb(
        userId,
        startISOString,
        endISOString,
      );

      // fetchNotifications(userId);
      setStepsFromDb(error ? 0 : (data?.[0]?.total_steps ?? 0));
    } catch (e) {
      handleHealthDataError('useStepSync', e);
    }
  }, [setStepsFromDb, setDashboardData, userId]);
}
