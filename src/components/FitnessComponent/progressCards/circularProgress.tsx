import { useTheme } from '@react-navigation/native';
import React, { FC, ReactNode } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import Svg, { Circle } from 'react-native-svg';
import { fontStyles, RFont } from '../../../theme/fonts';
import { globalStyles } from '../../../theme/styles/globalStyles';

interface CircularProgressProps {
  percentage: number;
  progressSize?: number;
  label: string;
  left: string;
  strokeColor?: string;
  total: string;
  consume: string;
  children?: ReactNode;
}

const CircularProgress: FC<CircularProgressProps> = ({
  percentage,
  label,
  left,
  total,
  consume,
  strokeColor = '#4CAF50',
  progressSize = 74,
  children,
}) => {
  const size = RFont(progressSize);
  const strokeWidth = RFont(4);
  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;
  const strokeDashoffset =
    circumference - (circumference * Math.min(percentage, 100)) / 100;

  const Theme = useTheme();
  const colors = Theme.colors;

  const adjustedSize = size + strokeWidth; // Increased size to accommodate stroke
  const center = adjustedSize / 2;

  return (
    <View style={styles.container}>
      <Text style={[fontStyles.Maison_400_12PX_16LH, { color: '#4A4C4F' }]}>
        {label}
      </Text>
      <View style={{ alignItems: 'center' }}>
        <Svg width={adjustedSize} height={adjustedSize}>
          <Circle
            stroke="#E0E0E0"
            fill="none"
            cx={center}
            cy={center}
            r={radius}
            strokeWidth={strokeWidth}
          />
          <Circle
            stroke={strokeColor}
            fill="none"
            cx={center}
            cy={center}
            r={radius}
            strokeWidth={strokeWidth}
            strokeDasharray={`${circumference} ${circumference}`}
            strokeDashoffset={strokeDashoffset}
            strokeLinecap="round"
            rotation="-90"
            origin={`${center}, ${center}`}
          />
        </Svg>

        <View
          style={[
            globalStyles.absolute,
            globalStyles.justifyContentCenter,
            globalStyles.alignItemsCenter,
            {
              width: adjustedSize,
              height: adjustedSize,
            },
          ]}
        >
          {children}
        </View>
      </View>
      <View>
        <Text
          style={[
            fontStyles.Maison_500_14PX_18LH,
            globalStyles.textAlignCenter,
            { color: colors?.grey_800 },
          ]}
        >
          {left}
          <Text
            style={[
              fontStyles.Maison_400_12PX_16LH,
              globalStyles.textAlignCenter,
              { color: colors?.grey_700 },
            ]}
          >
            {' '}
            left
          </Text>
        </Text>
        <Text
          style={[fontStyles.Maison_400_12PX_16LH, { color: colors?.grey_800 }]}
        >
          {consume}/{total}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    gap: RFont(16),
    margin: RFont(3),
  },
});

export default CircularProgress;
