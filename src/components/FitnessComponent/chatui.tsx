import React from 'react';
import {
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
const ChatUI = ({
  chatMessages,
  options,
  onOptionSelect,
  scrollViewRef,
  inputValue,
  setInputValue,
}: any) => {
  return (
    <View style={styles.container}>
      <ScrollView ref={scrollViewRef} style={styles.chatBox}>
        <View>
          {chatMessages.map((msg, id) => (
            <View
              key={id}
              style={[
                styles.bubble,
                msg.sender === 'user' ? styles.userBubble : styles.aiBubble,
              ]}
            >
              <Text style={styles.messageText}>{msg.text}</Text>
            </View>
          ))}
          {options.map((option, id) => (
            <TouchableOpacity
              key={id}
              style={styles.optionButton}
              onPress={() => onOptionSelect(option)}
            >
              <Text style={styles.optionText}>{option}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
      <View style={styles.inputRow}>
        <TextInput
          style={styles.textInput}
          placeholder="Type your message..."
          placeholderTextColor="#888"
          value={inputValue}
          onChangeText={setInputValue}
        />
        <TouchableOpacity
          style={styles.sendButton}
          onPress={() => {
            if (inputValue.trim()) {
              onOptionSelect(inputValue);
              setInputValue('');
            }
          }}
        >
          <Text style={styles.sendText}>Send</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};
export default ChatUI;
const styles = StyleSheet.create({
  aiBubble: { alignSelf: 'flex-start', backgroundColor: '#e0f0ff' },
  bubble: { borderRadius: 12, marginVertical: 4, maxWidth: '80%', padding: 12 },
  chatBox: { flex: 1, marginBottom: 10 },
  container: { backgroundColor: 'black', flex: 1, padding: 10 },
  inputRow: {
    alignItems: 'center',
    borderTopColor: '#ccc',
    borderTopWidth: 1,
    flexDirection: 'row',
    paddingTop: 8,
  },
  messageText: { color: 'black', fontSize: 16 },
  optionButton: {
    backgroundColor: '#e0f0ff',
    borderRadius: 8,
    marginVertical: 4,
    padding: 12,
  },
  optionText: { color: 'black', fontSize: 16 },
  sendButton: {
    backgroundColor: '#007bff',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 10,
  },
  sendText: { color: 'white', fontWeight: 'bold' },
  textInput: {
    backgroundColor: '#fff',
    borderRadius: 8,
    color: 'black',
    flex: 1,
    marginRight: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  userBubble: { alignSelf: 'flex-end', backgroundColor: '#d1ffd6' },
});
