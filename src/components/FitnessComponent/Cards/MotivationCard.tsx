import React from 'react';
import {
  GestureResponderEvent,
  StyleSheet,
  Text,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

type MotivationCardProps = {
  title: string;
  message: string;
  gradientColors?: string[];
  onClose?: (event: GestureResponderEvent) => void;
  containerStyle?: ViewStyle;
  titleStyle?: TextStyle;
  messageStyle?: TextStyle;
};

const MotivationCard: React.FC<MotivationCardProps> = ({
  title,
  message,
  gradientColors = ['#f56249', '#f7745e', '#fca128'],
  onClose,
  containerStyle,
  titleStyle,
  messageStyle,
}) => {
  return (
    <LinearGradient
      colors={gradientColors}
      start={{ x: 0, y: 0.5 }}
      end={{ x: 1, y: 1 }}
      style={[styles.gradientContainer, containerStyle]}
    >
      <View style={styles.header}>
        <MaterialCommunityIcons
          name="message-text-outline"
          size={28}
          color="#ffffff"
        />
        {onClose && (
          <TouchableOpacity onPress={onClose}>
            <MaterialCommunityIcons
              name="close"
              size={20}
              style={styles.closeIcon}
              color="#000000"
            />
          </TouchableOpacity>
        )}
      </View>

      <Text style={[styles.title, titleStyle]}>{title}</Text>
      <Text style={[styles.message, messageStyle]}>{message}</Text>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  gradientContainer: {
    borderRadius: 16,
    padding: 16,
    marginVertical: 10,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
    alignItems: 'center',
  },
  closeIcon: {
    borderRadius: 20,
    padding: 2,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    lineHeight: 24,
    color: '#fff',
  },
  message: {
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 18,
    color: '#fff',
    marginTop: 10,
  },
});

export default MotivationCard;
