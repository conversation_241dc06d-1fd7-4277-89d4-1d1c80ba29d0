// CheesyOmeletteInfo.tsx

import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { fontStyles } from '../../../theme/fonts';

interface CheesyOmeletteInfoProps {
  colors: {
    grey_600: string;
    secondary_curry: string;
    secondary_morning_sea: string;
    grey_800: string;
  };
  decs: any;
  p: any;
  c: any;
  f: any;
}

const CheesyOmeletteInfo: React.FC<CheesyOmeletteInfoProps> = ({
  colors,
  decs,
  p,
  c,
  f,
}) => {
  return (
    <View>
      <Text
        style={[fontStyles.Maison_400_12PX_16LH, { color: colors.grey_800 }]}
      >
        {decs}
      </Text>
      <Text style={{ color: colors.grey_600 }}>
        <Text style={[styles.dot, { color: colors.secondary_curry }]}>● </Text>p
        {p}
        <Text style={[styles.dot, { color: colors.secondary_morning_sea }]}>
          ●{' '}
        </Text>
        c {c}
        <Text style={[styles.dot, { color: colors.secondary_curry }]}>● </Text>f
        {f}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  boxLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  dot: {
    letterSpacing: 5,
    marginHorizontal: 10,
  },
});

export default CheesyOmeletteInfo;
