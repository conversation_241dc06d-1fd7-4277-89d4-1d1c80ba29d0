import React from 'react';
import { Text, View } from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { fontStyles, RFont } from '../../../theme/fonts';
interface Props {
  colors: any;
  lastSyncedAt: string;
  isSyncing: boolean;
  onClose: () => void;
}
export default function SyncCard({
  colors = {},
  lastSyncedAt = '',
  isSyncing = false,
  onClose = () => {},
}: Props) {
  return (
    <View
      style={{
        alignSelf: 'center',
        flexDirection: 'row',
        padding: RFont(10),
        borderRadius: RFont(10),
        alignItems: 'center',
        margin: RFont(20),
        backgroundColor: '#ECE1D7',
      }}
    >
      <Text
        style={{
          textAlign: 'center',
          color: colors?.primary_spinach,
          ...fontStyles.Maison_500_12PX_16LH,
        }}
      >
        {isSyncing
          ? 'Data sync in progress...'
          : `Last synced at ${lastSyncedAt}`}
      </Text>
      {!isSyncing && (
        <MaterialCommunityIcons
          name={'close'}
          onPress={onClose}
          size={25}
          style={{
            marginStart: RFont(15),
            borderRadius: 20,
          }}
          color="#000000"
        />
      )}
    </View>
  );
}
