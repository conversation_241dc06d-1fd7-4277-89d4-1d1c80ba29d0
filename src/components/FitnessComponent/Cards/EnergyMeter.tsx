import React, { memo, useEffect, useState } from 'react';
import {
  LayoutChangeEvent,
  StyleSheet,
  Text,
  View,
  ViewStyle,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Animated, {
  Easing,
  interpolate,
  runOnJS,
  useAnimatedStyle,
  useDerivedValue,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { Flag, InfoIcon, Mark } from '../../../assets/images';
import { fontStyles } from '../../../theme/fonts';
import { CommonCard } from '../../Card/Card';

type EnergyMeterProps = {
  target: number;
  current: number;
  min?: number;
  max?: number;
  style?: ViewStyle;
  targetLabel?: string;
  text?: string;
  gradientColors?: string[];
};

const EnergyMeter = ({
  target,
  current,
  min = -500,
  max = 500,
  style,
  text = '',
  targetLabel = '🎯 Target: 500 kcal deficit',
  gradientColors = ['#4CAF50', '#FFC107', '#FF5722'],
}: EnergyMeterProps) => {
  const progressBarWidth = useSharedValue(0);
  const animatedNumber = useSharedValue(0);
  const [displayedNumber, setDisplayedNumber] = useState(0);

  // Start the count-up animation when component mounts
  useEffect(() => {
    // Small delay to ensure component is fully mounted
    const timer = setTimeout(() => {
      animatedNumber.value = withTiming(current, {
        duration: 1500, // 1.5 seconds for the count-up animation
        easing: Easing.out(Easing.cubic), // Smooth easing for natural feel
      });
    }, 100);

    return () => clearTimeout(timer);
  }, [current, animatedNumber]);

  // Update displayed number using derived value
  useDerivedValue(() => {
    const roundedValue = Math.round(animatedNumber.value);
    runOnJS(setDisplayedNumber)(roundedValue);
  });

  // Animated style for the text with subtle scale effect
  const animatedTextStyle = useAnimatedStyle(() => {
    // Create a subtle scale effect during animation
    const targetValue = Math.abs(current) || 1;
    const progress = Math.abs(animatedNumber.value) / targetValue;
    const scale = interpolate(progress, [0, 0.5, 1], [0.8, 1.1, 1], 'clamp');

    return {
      transform: [{ scale }],
    };
  });

  const onMeterLayout = (event: LayoutChangeEvent) => {
    progressBarWidth.value = withTiming(event.nativeEvent.layout.width, {
      duration: 500,
    });
  };

  const animatedTargetStyle = useAnimatedStyle(() => ({
    left: progressBarWidth.value * ((target - min) / (max - min)) - 6,
  }));

  const animatedCurrentStyle = useAnimatedStyle(() => ({
    left: progressBarWidth.value * ((current - min) / (max - min)) - 6,
  }));

  const isSurplus = current > 0;

  console.log('Energy Meter Render', { current, displayedNumber });

  return (
    <CommonCard style={[{ marginVertical: 10 }, style]}>
      <View style={styles.header}>
        <Text style={fontStyles.Maison_600_18PX_24LH}>
          Daily calorie balance{' '}
          <Text style={[fontStyles.Maison_600_18PX_24LH, { color: 'gray' }]}>
            ●
          </Text>
        </Text>
        <InfoIcon />
      </View>

      <Text style={[fontStyles.Maison_400_12PX_16LH, { color: 'gray' }]}>
        {targetLabel}
      </Text>

      <View style={styles.meterContainer} onLayout={onMeterLayout}>
        <LinearGradient
          colors={gradientColors}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.gradientLine}
        />

        <Animated.View style={[styles.markerContainer, animatedTargetStyle]}>
          <Flag />
        </Animated.View>

        <Animated.View style={[styles.markerContainer, animatedCurrentStyle]}>
          <View style={{ flexDirection: 'row', alignItems: 'baseline' }}>
            <Animated.Text
              style={[
                fontStyles.Maison_600_32PX_40LH,
                { color: '#CC0000' },
                animatedTextStyle,
              ]}
            >
              {displayedNumber > 0
                ? `+${displayedNumber}`
                : `${displayedNumber}`}
            </Animated.Text>
            <Text
              style={[
                fontStyles.Maison_400_14PX_18LH,
                { lineHeight: 40, color: '#CC0000', marginLeft: 4 },
              ]}
            >
              {' kcal'}
            </Text>
          </View>
          <Mark />
        </Animated.View>
      </View>

      <View style={styles.legend}>
        <Text style={[fontStyles.Maison_500_12PX_16LH, { color: '#006600' }]}>
          Deficit
        </Text>
        <Text style={[fontStyles.Maison_500_12PX_16LH, { color: '#CC0000' }]}>
          Surplus
        </Text>
      </View>

      {isSurplus && (
        <View style={styles.warningBox}>
          <Text style={[fontStyles.Maison_400_12PX_14LH, styles.warningText]}>
            {text}
          </Text>
        </View>
      )}
    </CommonCard>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingBottom: 8,
  },
  meterContainer: {
    flex: 1,
    position: 'relative',
    height: 90,
    justifyContent: 'flex-end',
    marginTop: 5,
  },
  gradientLine: {
    height: 10,
    borderRadius: 10,
  },
  markerContainer: {
    position: 'absolute',
    alignItems: 'center',
  },
  legend: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    flex: 1,
    marginTop: 5,
  },
  warningBox: {
    flexDirection: 'row',
    backgroundColor: '#FFE9E5',
    marginTop: 16,
    padding: 10,
    borderRadius: 10,
  },
  warningIcon: {
    paddingHorizontal: 5,
  },
  warningText: {
    flex: 1,
    fontSize: 14,
    color: '#19191A',
  },
});

export default memo(EnergyMeter);
