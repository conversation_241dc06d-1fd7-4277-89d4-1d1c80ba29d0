import React, { FC, ReactNode } from 'react';
import { StyleProp, Text, TextStyle, View, ViewStyle } from 'react-native';
import { useTheme } from '../../theme';
import { fontStyles } from '../../theme/fonts';
import { mealSelectionCardStyles } from '../../theme/styles/cardStyles';

interface CommonBadgesProps {
  badge: string;
  style?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
  leftIcon?: ReactNode;
}
const CommonBadges: FC<CommonBadgesProps> = ({
  badge,
  style,
  textStyle,
  leftIcon,
}) => {
  const colors = useTheme();
  return (
    <View
      style={[
        mealSelectionCardStyles.discountBadge,
        { backgroundColor: colors?.primary_grenade },
        style,
      ]}
    >
      {leftIcon && <View>{leftIcon}</View>}
      <Text
        style={[
          fontStyles.Maison_500_12PX_16LH,
          { color: colors?.neutral_white },
          textStyle,
        ]}
      >
        {badge}
      </Text>
    </View>
  );
};

export default CommonBadges;
