import React, { FC } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import Svg, { Circle } from 'react-native-svg';
import { useTheme } from '../../theme';
import { fontStyles, RFont } from '../../theme/fonts';
import { globalStyles } from '../../theme/styles/globalStyles';

interface CircularProgressProps {
  percentage: number;
  progressSize?: number;
  label: string;
  left: string;
  total: string;
  consume: string;
}

const CircularProgress: FC<CircularProgressProps> = ({
  percentage,
  label,
  left,
  total,
  consume,
  progressSize = 74,
}) => {
  const size = RFont(progressSize); // circle size
  const strokeWidth = RFont(4);
  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;
  const strokeDashoffset =
    circumference -
    (circumference * (percentage > 100 ? 100 : percentage)) / 100;
  const colors = useTheme();
  return (
    <View style={[styles.container, { width: size }]}>
      <Svg width={size} height={size}>
        <Circle
          stroke="#E0E0E0"
          fill="none"
          cx={size / 2}
          cy={size / 2}
          r={radius}
          strokeWidth={strokeWidth}
        />
        {/* Progress Circle */}
        <Circle
          stroke={colors?.primary_grenade}
          fill="none"
          cx={size / 2}
          cy={size / 2}
          r={radius}
          strokeWidth={strokeWidth}
          strokeDasharray={`${circumference} ${circumference}`}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          rotation="-90"
          origin={`${size / 2}, ${size / 2}`}
        />
      </Svg>
      <View
        style={[
          globalStyles.absolute,
          globalStyles.justifyContentCenter,
          globalStyles.alignItemsCenter,
          {
            width: size,
            height: size,
          },
        ]}
      >
        <Text
          style={[
            fontStyles.Maison_500_18PX_22LH,
            {
              color: colors?.primary_spinach,
            },
          ]}
        >
          {percentage?.toFixed(0)}%
        </Text>
        <Text
          style={[
            fontStyles.Maison_400_12PX_16LH,
            {
              color: colors?.grey_700,
            },
          ]}
        >
          {label}
        </Text>
      </View>
      <View>
        <Text
          style={[
            fontStyles.Maison_400_12PX_16LH,
            globalStyles.textAlignCenter,
            { color: colors?.grey_700 },
          ]}
        >
          {left}
        </Text>
        <Text
          style={[
            fontStyles.Maison_400_12PX_16LH,
            globalStyles.textAlignCenter,
            { color: colors?.grey_700 },
          ]}
        >
          <Text
            style={[
              fontStyles.Maison_500_14PX_18LH,
              { color: colors?.grey_800 },
            ]}
          >
            {consume}
          </Text>{' '}
          / {total}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    gap: RFont(16),
  },
});

export default CircularProgress;
