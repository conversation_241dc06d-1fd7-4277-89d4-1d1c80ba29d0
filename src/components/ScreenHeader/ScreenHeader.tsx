import { ScreenHeaderProps } from 'card-props';
import React from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import { useSelector } from 'react-redux';
import { LeftArrow } from '../../assets/images';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontStyles } from '../../theme/fonts';
import {
  gapStyles,
  marginStyles,
} from '../../theme/styles/commonMarginPadding';
import { globalStyles } from '../../theme/styles/globalStyles';

export const ScreenHeader: React.FC<ScreenHeaderProps> = ({
  title,
  titleStyle,
  desc,
  isBack = true,
  onPressBack,
  wrapperStyles,
  subTitle,
}) => {
  const { isRTL } = useSelector((state: RootState) => state.app);
  const colors = useTheme();
  return (
    <View
      style={[
        isRTL ? globalStyles.rowReverse : globalStyles.row,
        globalStyles.alignItemsCenter,
        wrapperStyles,
      ]}
    >
      {isBack && (
        <TouchableOpacity onPress={onPressBack}>
          <LeftArrow
            style={[
              isRTL ? marginStyles.ml_8 : marginStyles.mr_8,
              {
                transform: [{ rotate: isRTL ? '-180deg' : '0deg' }],
              },
            ]}
          />
        </TouchableOpacity>
      )}
      <View style={[globalStyles.flex1, gapStyles.gap_12]}>
        <Text
          style={[
            isRTL ? globalStyles.textRight : globalStyles.textLeft,
            fontStyles.Maison_600_24PX_32LH,
            { color: colors?.black },
            titleStyle,
          ]}
        >
          {title}
        </Text>
        {desc ? (
          <Text
            style={[
              isRTL ? globalStyles.textRight : globalStyles.textLeft,
              fontStyles.Maison_500_16PX_18LH,
              { color: colors?.grey_900 },
            ]}
          >
            {desc}
          </Text>
        ) : null}
        {subTitle}
      </View>
    </View>
  );
};
