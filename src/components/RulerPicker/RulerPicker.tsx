/* eslint-disable react-native/no-inline-styles */
import React, { useEffect, useRef, useState } from 'react';
import {
  NativeScrollEvent,
  NativeSyntheticEvent,
  ScrollView,
  Text,
  TextInput,
  View,
} from 'react-native';
import { useTheme } from '../../theme';
import { fontStyles, RFont } from '../../theme/fonts';
import {
  gapStyles,
  marginStyles,
} from '../../theme/styles/commonMarginPadding';
import { globalStyles } from '../../theme/styles/globalStyles';
import { createRulerScreenStyles } from '../../theme/styles/rulerPickerStyles';
import { PrimaryBtn } from '../Buttons/Btns';
import Divider from '../Divider';
import RulerPickerIndicator from './RulerPickerIndicator';

const ITEM_HEIGHT = 11;

interface VerticalRulerProps {
  unit: 'cm' | 'kg' | 'lbs' | 'ft-in';
  min: number;
  max: number;
  activeUnit?: string;
  unitData: Array<{
    label: string;
    value: string;
  }>;
  // eslint-disable-next-line no-unused-vars
  onChange?: (value: number | string) => void;
  title?: string;
  // eslint-disable-next-line no-unused-vars
  onPress: (value: string) => void;
}

export const VerticalRuler = ({
  unit,
  unitData,
  activeUnit,
  onPress,
  min,
  max,
  onChange,
  title = 'Select Value',
}: VerticalRulerProps) => {
  const scrollRef = useRef<ScrollView>(null);
  const [value, setValue] = useState<number>(unit === 'ft-in' ? min / 12 : min);

  const data = Array.from(
    { length: Math.floor(max - min + 1) },
    (_, i) => min + i,
  );

  const visibleItems = 8;
  const centerOffset = (ITEM_HEIGHT * visibleItems) / 2 - ITEM_HEIGHT / 2;

  const formatValue = (val: number): number | string => {
    if (unit === 'ft-in') {
      const ft = Math.floor(val / 12);
      return `${ft}ft`;
    }
    return val;
  };

  const handleScroll = (e: NativeSyntheticEvent<NativeScrollEvent>) => {
    const offsetY = e.nativeEvent.contentOffset.y;
    const index = Math.round(offsetY / ITEM_HEIGHT);
    const newValueInInches = data[index];

    if (newValueInInches !== undefined) {
      if (unit === 'ft-in') {
        const newValueInFeet = newValueInInches / 12;
        setValue(parseFloat(newValueInFeet.toFixed(2)));
        onChange?.(formatValue(newValueInInches)); // display as 5ft 2in
      } else {
        setValue(newValueInInches);
        onChange?.(newValueInInches);
      }
    }
  };

  const scrollToInitial = () => {
    const inches =
      unit === 'ft-in' ? Math.round((value ?? min) * 12) : (value ?? min);
    const index = data.indexOf(inches);

    if (scrollRef.current && index >= 0) {
      scrollRef.current.scrollTo({
        y: index * ITEM_HEIGHT,
        animated: false,
      });
    }
  };
  const colors = useTheme();
  const rulerStyles = createRulerScreenStyles(colors);
  useEffect(() => {
    scrollToInitial();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const scrollToValue = (val: number) => {
    const target = unit === 'ft-in' ? Math.round(val * 12) : val;
    const index = data.indexOf(target);
    if (scrollRef.current && index >= 0) {
      scrollRef.current.scrollTo({
        y: index * ITEM_HEIGHT,
        animated: false,
      });
    }
  };

  return (
    <View style={rulerStyles.container}>
      <Text
        style={[fontStyles.Maison_600_32PX_40LH, { color: colors?.grey_900 }]}
      >
        {title}
      </Text>
      <View style={[globalStyles.row, globalStyles.justifyContentSpaceBetween]}>
        <View
          style={[globalStyles.alignItemsCenter, globalStyles.alignSelfEnd]}
        >
          <TextInput
            value={String(value)}
            style={[
              fontStyles.Maison_600_24PX_32LH,
              { color: colors?.neutral_black },
            ]}
            onChangeText={(e) => {
              const val = parseFloat(e);
              if (!isNaN(val)) {
                setValue(val);
                scrollToValue(val);
              } else {
                setValue(0);
              }
            }}
          />
          <Divider color={colors?.grey_100} thickness={1} />
          <View style={[globalStyles.row, gapStyles.gap_8, marginStyles.mt_20]}>
            {unitData?.map((item) => (
              <PrimaryBtn
                key={item?.value}
                text={item?.label}
                textStyle={[
                  fontStyles.Maison_500_18PX_21_6LH,
                  {
                    color:
                      item?.value === activeUnit
                        ? colors?.primary_cream
                        : colors?.grey_900,
                  },
                ]}
                style={[
                  rulerStyles.rulerPrimaryBtn,
                  {
                    backgroundColor:
                      item?.value === activeUnit
                        ? colors?.primary_spinach
                        : colors?.neutral_white,
                  },
                ]}
                onPress={() => onPress(item?.value)}
              />
            ))}
          </View>
        </View>
        <View>
          <RulerPickerIndicator
            onPress={onPress}
            activeUnit={activeUnit}
            unitData={unitData}
            centerOffset={centerOffset + 160}
            value={value}
          />

          <ScrollView
            ref={scrollRef}
            onScroll={handleScroll}
            scrollEventThrottle={10}
            contentContainerStyle={{
              paddingVertical: centerOffset + 160,
              paddingBottom: RFont(250),
            }}
            style={marginStyles.mt_2}
          >
            {data.map((val, index) => {
              return (
                <View key={val} style={rulerStyles.tickRow}>
                  <Text
                    style={[
                      fontStyles.Maison_500_12PX_15_6LH,
                      {
                        color: colors?.grey_400,
                        height: RFont(12),
                        marginRight: 30,
                      },
                    ]}
                  >
                    {(unit === 'ft-in' && val % 12 === 0) ||
                    (unit !== 'ft-in' && index % 10 === 0)
                      ? formatValue(val)
                      : ''}
                  </Text>
                  <View
                    style={[
                      rulerStyles.tick,
                      unit === 'ft-in'
                        ? val % 12 === 0
                          ? rulerStyles.tickMajor
                          : val % 6 === 0
                            ? rulerStyles.tickMedium
                            : rulerStyles.tickMinor
                        : index % 7 === 0
                          ? rulerStyles.tickMajor
                          : index % 5 === 0
                            ? rulerStyles.tickMedium
                            : rulerStyles.tickMinor,
                    ]}
                  />
                </View>
              );
            })}
          </ScrollView>
        </View>
      </View>
    </View>
  );
};
