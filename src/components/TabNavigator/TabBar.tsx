import { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import { FC } from 'react';
import { Pressable, StyleSheet, View } from 'react-native';
import { useSelector } from 'react-redux';
import { DeliveryIcon, HomeIcon, SettingIcon } from '../../assets/images';
import { useAppNavigation } from '../../hooks/useAppNavigation';
import { getCurrentRouteName } from '../../navigation/AppNavigator';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { RFont } from '../../theme/fonts';
import { globalStyles } from '../../theme/styles/globalStyles';
const MyTabBar: FC<BottomTabBarProps> = () => {
  const { isRTL } = useSelector((state: RootState) => state.app);
  const colors = useTheme();
  const currentPage = getCurrentRouteName();
  const navigation = useAppNavigation();
  return currentPage === 'MealPlan' ? null : (
    <View
      style={[
        isRTL ? globalStyles.rowReverse : globalStyles.row,
        {
          backgroundColor: colors?.neutral_white,
        },
        tabsStyles.bottomtabWrapper,
      ]}
    >
      <Pressable
        onPress={() => {
          navigation.navigate('Account');
        }}
        style={tabsStyles.iconWrapper}
      >
        <View
          style={[
            tabsStyles.iconstyle,
            {
              backgroundColor:
                currentPage === 'Account' ? colors?.FFDDD199 : undefined,
            },
          ]}
        >
          <HomeIcon
            width={RFont(27)}
            height={RFont(27)}
            color={
              currentPage === 'Account'
                ? colors?.primary_grenade
                : colors?.primary_spinach
            }
          />
        </View>
      </Pressable>
      <Pressable
        onPress={() => {
          navigation.navigate('MealPlan');
        }}
        style={tabsStyles.iconWrapper}
      >
        <View
          style={[
            tabsStyles.iconstyle,
            {
              backgroundColor:
                currentPage === 'MealPlan' ? colors?.FFDDD199 : undefined,
            },
          ]}
        >
          <DeliveryIcon
            width={RFont(27)}
            height={RFont(27)}
            color={
              currentPage === 'MealPlan'
                ? colors?.primary_grenade
                : colors?.primary_spinach
            }
          />
        </View>
      </Pressable>
      <Pressable
        onPress={() => {
          navigation.navigate('Settings');
        }}
        style={tabsStyles.iconWrapper}
      >
        <View
          style={[
            tabsStyles.iconstyle,
            {
              backgroundColor:
                currentPage === 'Settings' ? colors?.FFDDD199 : undefined,
            },
          ]}
        >
          <SettingIcon
            width={RFont(27)}
            height={RFont(27)}
            color={
              currentPage === 'Settings'
                ? colors?.primary_grenade
                : colors?.primary_spinach
            }
          />
        </View>
      </Pressable>
    </View>
  );
};
const tabsStyles = StyleSheet.create({
  bottomtabWrapper: {
    borderTopLeftRadius: RFont(20),
    borderTopRightRadius: RFont(20),
    elevation: RFont(4),
    paddingHorizontal: RFont(16),
    paddingVertical: RFont(12),
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: RFont(4) },
    shadowOpacity: 0.05,
    shadowRadius: RFont(4),
  },
  iconWrapper: { alignItems: 'center', flex: 1 },
  iconstyle: {
    borderRadius: RFont(12),
    overflow: 'hidden',
    padding: RFont(8),
  },
});
export default MyTabBar;
