import { AccordionProps } from 'card-props';
import React from 'react';
import { Pressable, Text, TouchableOpacity, View } from 'react-native';
import Collapsible from 'react-native-collapsible';
import { useSelector } from 'react-redux';
import {
  MinusRoundedIcon,
  MoreHorizontalIcon,
  PlusBlackIcon,
} from '../../assets/images';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontStyles } from '../../theme/fonts';
import { accordionStyles } from '../../theme/styles/accordionStyles';
import { marginStyles } from '../../theme/styles/commonMarginPadding';
import { globalStyles } from '../../theme/styles/globalStyles';

export const Accordion: React.FC<AccordionProps> = ({
  title = '',
  onPress = () => null,
  isOpen = false,
  showMoreHorizontalIcon = false,
  showSkipBadge = false,
  children,
  onPressMore,
}) => {
  const { isRTL } = useSelector((state: RootState) => state.app);

  const { translation } = useSelector((state: RootState) => state?.auth);
  const colors = useTheme();
  return (
    <View>
      <TouchableOpacity
        style={[
          isRTL ? globalStyles.rowReverse : globalStyles.row,
          accordionStyles.headerWrapper,
        ]}
        onPress={onPress}
      >
        <View
          style={[
            isRTL ? globalStyles.rowReverse : globalStyles.row,
            accordionStyles.header,
          ]}
        >
          <Text
            style={[
              fontStyles.Maison_500_16PX_18LH,
              { color: colors?.grey_900 },
            ]}
          >
            {title}
          </Text>
          {showMoreHorizontalIcon ? (
            <Pressable onPress={onPressMore}>
              <MoreHorizontalIcon
                style={isRTL ? marginStyles.mr_12 : marginStyles.ml_12}
              />
            </Pressable>
          ) : null}
          {showSkipBadge ? (
            <View
              style={[
                isRTL ? marginStyles.mr_8 : marginStyles.ml_8,
                accordionStyles.skipBadge,
                { backgroundColor: colors?.black_30_p },
              ]}
            >
              <Text
                style={[
                  globalStyles.textAlignCenter,
                  fontStyles.Maison_500_12PX_16LH,
                  { color: colors?.grey_900 },
                ]}
              >
                {translation?.SKIPPED}
              </Text>
            </View>
          ) : null}
          {/* {showReferAndEarnBadge ? (
            <View
              style={[
                accordionStyles.referAndEarnBadge,
                { backgroundColor: colors?.primary },
              ]}
            >
              <Text
                style={[
                  fontStyles.Maison_600_10PX_16LH,
                  {
                    color: colors?.white,
                  },
                ]}
              >
                {translation?.REFER_N_EARN}
              </Text>
            </View>
          ) : null} */}
        </View>
        {isOpen ? <MinusRoundedIcon /> : <PlusBlackIcon />}
      </TouchableOpacity>
      <Collapsible
        easing="easeInOutCubic"
        collapsed={!isOpen}
        renderChildrenCollapsed={true}
      >
        <View>{children}</View>
      </Collapsible>
    </View>
  );
};
