import { CheckboxProps } from 'card-props';
import React, { FC } from 'react';
import { TouchableOpacity, View } from 'react-native';
import { RFValue } from 'react-native-responsive-fontsize';
import { CheckIcon, RadioIcon } from '../../assets/images';
import { useTheme } from '../../theme';
import { buttonStyles } from '../../theme/styles/buttonStyles';

export const Checkbox: FC<CheckboxProps> = ({ disabled, onChange, value }) => {
  const colors = useTheme();
  return (
    <View>
      {value ? (
        <TouchableOpacity
          disabled={disabled}
          onPress={() => onChange && onChange(!value)}
        >
          <CheckIcon width={RFValue(16)} height={RFValue(16)} />
        </TouchableOpacity>
      ) : (
        <TouchableOpacity
          disabled={disabled}
          onPress={() => onChange && onChange(!value)}
          style={[
            buttonStyles.Checkbox,
            { borderColor: colors?.secondary_warm_grey },
          ]}
        />
      )}
    </View>
  );
};
export const RadioBox: FC<CheckboxProps> = ({ disabled, onChange, value }) => {
  const colors = useTheme();
  return (
    <View>
      {value ? (
        <TouchableOpacity
          disabled={disabled}
          onPress={() => onChange && onChange(!value)}
        >
          <RadioIcon width={RFValue(20)} height={RFValue(20)} />
        </TouchableOpacity>
      ) : (
        <TouchableOpacity
          disabled={disabled}
          onPress={() => onChange && onChange(!value)}
          style={[
            buttonStyles.Checkbox,
            { borderColor: colors?.secondary_warm_grey },
          ]}
        />
      )}
    </View>
  );
};
