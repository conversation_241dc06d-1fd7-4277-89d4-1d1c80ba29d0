import React, { FC } from 'react';
import { Pressable, Text } from 'react-native';
import { useSelector } from 'react-redux';
import { AddIcon } from '../../assets/images';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontStyles, RFont } from '../../theme/fonts';
import {
  gapStyles,
  paddingStyles,
} from '../../theme/styles/commonMarginPadding';
import { globalStyles } from '../../theme/styles/globalStyles';
interface AddNewCardBtnProps {
  onPress?: () => void;
}
export const AddNewCardBtn: FC<AddNewCardBtnProps> = ({ onPress }) => {
  const { isRTL } = useSelector((state: RootState) => state.app);
  const colors = useTheme();
  return (
    <Pressable
      onPress={onPress}
      style={[
        isRTL ? globalStyles.flexDirectionRow : globalStyles.flexDirectionRow,
        gapStyles.gap_8,
        paddingStyles.py8,
        paddingStyles.px12,
        globalStyles.alignItemsCenter,
      ]}
    >
      <AddIcon width={RFont(20)} height={RFont(20)} />
      <Text
        style={[
          fontStyles.Maison_600_16PX_20LH,
          { color: colors?.primary_grenade },
        ]}
      >
        Add new card
      </Text>
    </Pressable>
  );
};
