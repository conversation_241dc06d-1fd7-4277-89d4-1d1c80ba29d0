import { PrimaryBtnProps } from 'componentsProps';
import React, { useState } from 'react';
import {
  ActivityIndicator,
  Pressable,
  ScrollView,
  Text,
  View,
} from 'react-native';
import { useSelector } from 'react-redux';
import { MultiSelectPillGroupProps } from '../../../@types/button';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontStyles } from '../../theme/fonts';
import { buttonStyles } from '../../theme/styles/buttonStyles';
import {
  gapStyles,
  marginStyles,
} from '../../theme/styles/commonMarginPadding';
import { globalStyles } from '../../theme/styles/globalStyles';
import { SelectableRoundChip } from './Chips';

export const PrimaryBtn: React.FC<PrimaryBtnProps> = ({
  leftIcon,
  rightIcon,
  iconStyle,
  text = '',
  onPress = () => null,
  style = undefined,
  disabled,
  isLoading,
  textStyle,
  disabledColor,
  backgroundColor,
  focusColor,
}) => {
  const { isRTL } = useSelector((state: RootState) => state.app);
  const colors = useTheme();
  const [isFocus, setFocus] = useState(false);
  return (
    <Pressable
      disabled={disabled || isLoading}
      onPress={(event) => {
        if (onPress) onPress(event);
      }}
      onPressIn={() => {
        setFocus(true);
      }}
      onPressOut={() => {
        setFocus(false);
      }}
      style={[
        isRTL ? globalStyles.rowReverse : globalStyles.row,
        buttonStyles.PrimaryBtn,
        {
          backgroundColor: disabled
            ? disabledColor
            : isFocus && focusColor
              ? focusColor
              : (backgroundColor ?? colors?.primary_grenade),
        },
        style,
      ]}
    >
      {leftIcon && <View style={iconStyle}>{leftIcon}</View>}
      {text && (
        <Text
          style={[
            fontStyles.Maison_600_16PX_20LH,
            {
              color: isFocus ? focusColor : colors?.neutral_white,
            },
            textStyle,
          ]}
        >
          {text}
        </Text>
      )}
      {rightIcon ||
        (isLoading && (
          <View style={globalStyles.row}>
            {rightIcon && <View style={iconStyle}>{rightIcon}</View>}
            {isLoading ? <ActivityIndicator size={'small'} /> : null}
          </View>
        ))}
    </Pressable>
  );
};
export const MultiSelectPillGroup: React.FC<MultiSelectPillGroupProps> = ({
  options,
  selectedValues,
  onSelect = () => null,
  containerStyle,
  buttonStyle,
  textStyle,
  titleStyle,
  title,
  isRTL = false,
  isScrollable = false,
}) => {
  const colors = useTheme();
  return (
    <View style={containerStyle}>
      {title && (
        <Text
          style={[
            fontStyles?.Maison_500_18PX_22LH,
            marginStyles?.mb_16,
            { color: colors?.grey_700 },
            titleStyle,
          ]}
        >
          {title}
        </Text>
      )}
      {isScrollable ? (
        <ScrollView
          horizontal={true}
          showsHorizontalScrollIndicator={false}
          style={[globalStyles.flex0, marginStyles.mrn_20]}
          contentContainerStyle={[globalStyles.row, gapStyles.gap_8]}
        >
          {options?.map((opt) => {
            const isSelected = selectedValues?.includes(opt?.label);
            return (
              <SelectableRoundChip
                key={`multi-pill-${opt.label}`}
                text={opt?.label}
                onPress={() => (onSelect ? onSelect(opt.label) : undefined)}
                style={buttonStyle}
                textStyle={textStyle}
                isSelected={isSelected}
              />
            );
          })}
        </ScrollView>
      ) : (
        <View
          style={[
            isRTL ? globalStyles?.rowReverse : globalStyles?.row,
            globalStyles?.flexWrap,
            gapStyles.gap_10,
          ]}
        >
          {options?.map((opt) => {
            const isSelected = selectedValues?.includes(opt?.label);
            return (
              <SelectableRoundChip
                key={`multi-pill-${opt.label}`}
                text={opt?.label}
                onPress={() => (onSelect ? onSelect(opt.label) : undefined)}
                style={buttonStyle}
                textStyle={textStyle}
                isSelected={isSelected}
              />
            );
          })}
        </View>
      )}
    </View>
  );
};
