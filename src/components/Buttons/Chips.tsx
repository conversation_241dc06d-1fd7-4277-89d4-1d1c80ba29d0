import { SelectableChipProps, warning_chip_props } from 'componentsProps';
import React from 'react';
import { Pressable, Text } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { useSelector } from 'react-redux';
import { WarningIcon } from '../../assets/images';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontStyles, RFont } from '../../theme/fonts';
import { buttonStyles } from '../../theme/styles/buttonStyles';
import {
  gapStyles,
  paddingStyles,
} from '../../theme/styles/commonMarginPadding';
import {
  borderRadiusStyles,
  globalStyles,
} from '../../theme/styles/globalStyles';

export const SelectableRoundChip: React.FC<SelectableChipProps> = ({
  text = '',
  onPress = () => null,
  style = undefined,
  disabled,
  isLoading,
  isSelected,
  textStyle,
  rightIcon,
  leftIcon,
}) => {
  const { isRTL } = useSelector((state: RootState) => state.app);
  const colors = useTheme();
  return (
    <Pressable
      disabled={disabled || isLoading}
      onPress={(event) => {
        if (onPress) onPress(event);
      }}
      style={[
        isRTL ? globalStyles.rowReverse : globalStyles.row,
        buttonStyles?.PrimarySelectablePill,
        {
          backgroundColor: isSelected
            ? colors?.primary_spinach
            : colors?.neutral_white,
          borderColor: isSelected ? colors?.primary_spinach : colors?.grey_100,
        },
        style,
      ]}
    >
      {leftIcon}
      <Text
        style={[
          fontStyles?.Maison_500_18PX_22LH,
          { color: isSelected ? colors?.neutral_white : colors?.grey_900 },
          textStyle,
        ]}
      >
        {text}
      </Text>
      {rightIcon}
    </Pressable>
  );
};
export const SelectableSlotChip: React.FC<SelectableChipProps> = ({
  text = '',
  onPress = () => null,
  style = undefined,
  disabled,
  isLoading,
  isSelected,
  textStyle,
  rightIcon,
  leftIcon,
}) => {
  const { isRTL } = useSelector((state: RootState) => state.app);
  const colors = useTheme();
  return (
    <Pressable
      disabled={disabled || isLoading}
      onPress={(event) => {
        if (onPress) onPress(event);
      }}
      style={[
        isRTL ? globalStyles.rowReverse : globalStyles.row,
        buttonStyles?.PrimarySelectablePill,
        paddingStyles?.py8,
        paddingStyles?.px12,
        borderRadiusStyles?.br12,
        {
          backgroundColor: isSelected
            ? colors?.primary_spinach
            : colors?.neutral_white,
          borderColor: isSelected ? colors?.primary_spinach : colors?.grey_100,
        },
        style,
      ]}
    >
      {leftIcon}
      <Text
        style={[
          fontStyles?.Maison_500_14PX_18LH,
          { color: isSelected ? colors?.neutral_white : colors?.grey_900 },
          textStyle,
        ]}
      >
        {text}
      </Text>
      {rightIcon}
    </Pressable>
  );
};
export const WarningChip: React.FC<warning_chip_props> = ({
  text = '',
  textStyle,
  style,
}) => {
  const colors = useTheme();
  return (
    <LinearGradient
      start={{ x: 0.1, y: 1 }}
      end={{ x: 0.3, y: 1 }}
      colors={colors?.yellow_gradient}
      style={[
        gapStyles.gap_8,
        paddingStyles.p12,
        borderRadiusStyles.br12,
        globalStyles.flexDirectionRow,
        globalStyles.alignItemsCenter,
        style,
      ]}
    >
      <WarningIcon width={RFont(16)} height={RFont(16)} />
      <Text
        style={[
          fontStyles?.Maison_500_14PX_18LH,
          { color: colors?.neutral_black },
          textStyle,
        ]}
      >
        {text}
      </Text>
    </LinearGradient>
  );
};
