import React from 'react';
import { Text, View } from 'react-native';
import { PriceWithDiscountProps } from '../../../@types/text';
import { useTheme } from '../../theme';
import { fontStyles } from '../../theme/fonts';
import { marginStyles } from '../../theme/styles/commonMarginPadding';
import { globalStyles } from '../../theme/styles/globalStyles';

const PriceWithDiscount: React.FC<PriceWithDiscountProps> = ({
  currency,
  originalPrice,
  finalPrice,
  durationText,
  containerStyle,
  currencyStyle,
  originalPriceStyle,
  finalPriceStyle,
  durationStyle,
}) => {
  const colors = useTheme();
  return (
    <View
      style={[globalStyles.row, globalStyles?.alignItemsCenter, containerStyle]}
    >
      <Text
        style={[
          fontStyles.Maison_400_14PX_18LH,
          { color: colors?.grey_600 },
          currencyStyle,
        ]}
      >
        {currency}{' '}
      </Text>

      {originalPrice && (
        <Text
          style={[
            fontStyles.Maison_400_14PX_18LH,
            globalStyles?.textDecorationLineThrough,
            {
              color: colors?.grey_600,
            },
            originalPriceStyle,
          ]}
        >
          {originalPrice}{' '}
        </Text>
      )}

      <Text
        style={[
          fontStyles.Maison_600_16PX_20LH,
          marginStyles?.mr_6,
          { color: colors?.grey_800 },
          finalPriceStyle,
        ]}
      >
        {finalPrice}
      </Text>

      <Text
        style={[
          fontStyles.Maison_400_14PX_18LH,
          { color: colors?.grey_600 },
          durationStyle,
        ]}
      >
        {durationText}
      </Text>
    </View>
  );
};

export default PriceWithDiscount;
