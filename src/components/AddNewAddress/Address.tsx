import { AddEditAddressPayload } from 'actions';
import React, { useEffect } from 'react';
import { useFormContext } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { getDeliveryArea } from '../../redux/action/appActions';
import { RootState } from '../../redux/store';
import { fontStyles } from '../../theme/fonts';
import {
  marginStyles,
  paddingStyles,
} from '../../theme/styles/commonMarginPadding';
import { CommonCard } from '../Card/Card';
import SelectBottomSheet from '../SelectBottomSheet/SelectBottomSheet';
import { TextInputField } from '../TextInputField/TextInputField';

const Address = () => {
  const { translation } = useSelector((state: RootState) => state?.auth);
  const { deliveryArea, cityList } = useSelector(
    (state: RootState) => state?.app,
  );

  const dispatch = useDispatch();
  const {
    setValue,
    watch,
    formState: { errors },
  } = useFormContext<AddEditAddressPayload>();
  useEffect(() => {
    if (watch('city')) {
      dispatch(getDeliveryArea(watch('city')));
    }
  }, [dispatch, watch, watch('city')]);

  return (
    <CommonCard
      title={translation?.ADDRESS}
      titleStyle={fontStyles.Maison_600_18PX_23_4LH}
      style={[paddingStyles.py16, paddingStyles.px16]}
    >
      <SelectBottomSheet
        option={cityList?.map((e) => ({
          label: e.city_name,
          value: e.city_name,
        }))}
        value={watch('city')}
        bottomSheetTitle="Select city"
        containerStyle={marginStyles?.mt_16}
        header="City"
        onChange={(value) => {
          setValue('city', value, { shouldValidate: true });
          setValue('province', '');
        }}
        error={errors?.city?.message}
      />

      <SelectBottomSheet
        option={deliveryArea?.map((e) => ({
          label: e,
          value: e,
        }))}
        value={watch('province')}
        bottomSheetTitle="Select Area"
        containerStyle={marginStyles?.mt_16}
        header="Area"
        onChange={(value) => {
          setValue('province', value, { shouldValidate: true });
        }}
        error={errors?.province?.message}
      />

      <TextInputField
        header="Address"
        containerStyle={marginStyles?.mt_16}
        onChangeText={(value) => {
          setValue('full_address', value, { shouldValidate: true });
        }}
        value={watch('full_address')}
        error={errors?.full_address?.message}
      />
    </CommonCard>
  );
};

export default Address;
