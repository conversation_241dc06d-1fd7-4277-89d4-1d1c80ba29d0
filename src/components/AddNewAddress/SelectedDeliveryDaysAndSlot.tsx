import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../../redux/store';
import { fontStyles } from '../../theme/fonts';
import {
  marginStyles,
  paddingStyles,
} from '../../theme/styles/commonMarginPadding';
import { avilableSlotsData } from '../../utils/global';
import { MultiSelectPillGroup } from '../Buttons/Btns';
import { CommonCard } from '../Card/Card';
import InfoCard from '../Card/InfoCard';

const SelectedDeliveryDaysAndSlot = () => {
  const { translation } = useSelector((state: RootState) => state?.auth);
  const [selectedValues, setSelectedValues] = useState('');
  return (
    <CommonCard
      title={translation?.SELECTED_DELIVERY_DAYS_AND_SLOT}
      titleStyle={fontStyles.Maison_600_18PX_23_4LH}
      style={[paddingStyles.py16, paddingStyles.px16]}
    >
      <MultiSelectPillGroup
        options={avilableSlotsData.map((itm) => ({
          label: itm.value,
        }))}
        selectedValues={selectedValues as string}
        onSelect={(val) => {
          setSelectedValues(val);
        }}
        containerStyle={marginStyles.mt_16}
      />
      <InfoCard message="Your delivery days & slot applies to all deliveries for this address and can be changed anytime." />
    </CommonCard>
  );
};

export default SelectedDeliveryDaysAndSlot;
