import { AddEditAddressPayload } from 'actions';
import React, { useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { fontStyles } from '../../theme/fonts';
import {
  marginStyles,
  paddingStyles,
} from '../../theme/styles/commonMarginPadding';
import { AddressTypes } from '../../utils/global';
import { MultiSelectPillGroup } from '../Buttons/Btns';
import { CommonCard } from '../Card/Card';
import { TextInputField } from '../TextInputField/TextInputField';

const DeliveryLabel = () => {
  const [isOther, setOther] = useState(false);
  const [selectedValues, setSelectedValues] = useState('');
  const {
    setValue,
    watch,
    formState: { errors },
  } = useFormContext<AddEditAddressPayload>();
  return (
    <CommonCard
      title="Label"
      titleStyle={fontStyles.Maison_600_18PX_23_4LH}
      style={[paddingStyles.py16, paddingStyles.px16]}
    >
      <MultiSelectPillGroup
        options={AddressTypes.map((itm) => ({
          label: itm,
        }))}
        selectedValues={(selectedValues as string) || watch('address_type')}
        onSelect={(val) => {
          if (val === 'Other') {
            setOther(true);
            setValue('address_type', '', { shouldValidate: true });
          } else {
            setOther(false);
            setValue('address_type', val, { shouldValidate: true });
          }
          setSelectedValues(val);
        }}
        containerStyle={marginStyles.mt_12}
      />
      {isOther ? (
        <TextInputField
          placeholder="Enter Address type"
          containerStyle={marginStyles?.mt_20}
          onChangeText={(value) => {
            setValue('address_type', value, { shouldValidate: true });
          }}
          value={watch('address_type')}
          error={errors?.address_type?.message}
        />
      ) : null}
    </CommonCard>
  );
};

export default DeliveryLabel;
