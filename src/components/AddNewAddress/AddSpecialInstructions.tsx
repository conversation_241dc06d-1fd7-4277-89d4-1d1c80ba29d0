import { editDeliveryAddressPayload } from 'actions';
import React, { useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { View } from 'react-native';
import { useSelector } from 'react-redux';
import { PlusOrangeIcon, Xclose } from '../../assets/images';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { lightTheme } from '../../theme/colors';
import { fontStyles, RFont } from '../../theme/fonts';
import { buttonStyles } from '../../theme/styles/buttonStyles';
import {
  gapStyles,
  paddingStyles,
} from '../../theme/styles/commonMarginPadding';
import { globalStyles } from '../../theme/styles/globalStyles';
import SpecialInstructionsSheet from '../BottomSheet/SpecialInstructionsSheet';
import { PrimaryBtn } from '../Buttons/Btns';
import { SelectableSlotChip } from '../Buttons/Chips';
import { CommonCard } from '../Card/Card';
import InfoCard from '../Card/InfoCard';

const AddSpecialInstructions = () => {
  const { translation } = useSelector((state: RootState) => state?.auth);
  const [openBottomSheet, setOpenBottomSheet] = useState(false);
  const { setValue, watch } = useFormContext<editDeliveryAddressPayload>();
  const colors = useTheme();

  return (
    <CommonCard
      title={translation?.ADD_SPECIAL_INSTRUCTIONS}
      titleStyle={fontStyles.Maison_600_18PX_23_4LH}
      style={[paddingStyles.py16, paddingStyles.px16, gapStyles.gap_16]}
    >
      {watch('instruction')?.length === 0 ? null : (
        <View
          style={[
            globalStyles.flexDirectionRow,
            globalStyles.flexWrap,
            gapStyles.gap_12,
          ]}
        >
          {watch('instruction')?.map((item, i) => {
            return (
              <SelectableSlotChip
                key={i}
                text={item}
                style={{
                  backgroundColor: colors?.secondary_warm_grey,
                  borderColor: colors?.secondary_warm_grey,
                }}
                rightIcon={
                  <Xclose
                    onPress={() => {
                      const updatedItems = watch('instruction')?.filter(
                        (e) => e !== item,
                      );
                      setValue('instruction', updatedItems);
                    }}
                    width={RFont(16)}
                    hitSlop={RFont(16)}
                  />
                }
              />
            );
          })}
        </View>
      )}
      <PrimaryBtn
        onPress={() => setOpenBottomSheet(true)}
        leftIcon={<PlusOrangeIcon />}
        textStyle={[
          fontStyles?.Maison_600_18PX_21_6LH,
          { color: lightTheme?.primary_grenade },
        ]}
        text={translation?.ADD_SPECIAL_INSTRUCTIONS}
        style={[
          buttonStyles?.BorderLessBtn,
          globalStyles.justifyContentFlexStart,
          paddingStyles.px0,
          paddingStyles.py0,
        ]}
      />
      <InfoCard message="Your delivery days & slot applies to all deliveries for this address and can be changed anytime." />
      <SpecialInstructionsSheet
        sheetOpen={openBottomSheet}
        sheetClose={() => {
          setOpenBottomSheet(false);
        }}
      />
    </CommonCard>
  );
};

export default AddSpecialInstructions;
