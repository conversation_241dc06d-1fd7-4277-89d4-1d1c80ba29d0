import React from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../../redux/store';
import { fontStyles } from '../../theme/fonts';
import {
  marginStyles,
  paddingStyles,
} from '../../theme/styles/commonMarginPadding';
import { CommonCard } from '../Card/Card';
import { PhoneInputField } from '../TextInputField/PhoneInputField';
import { TextInputField } from '../TextInputField/TextInputField';

const ContactDetails = () => {
  const { translation } = useSelector((state: RootState) => state?.auth);
  return (
    <CommonCard
      title={translation?.CONTACT_DETAILS}
      titleStyle={fontStyles.Maison_600_18PX_23_4LH}
      style={[paddingStyles.py16, paddingStyles.px16]}
    >
      <TextInputField placeholder="Name" containerStyle={marginStyles?.mt_16} />
      <PhoneInputField
        placeholder="12345 67890"
        containerStyle={marginStyles?.mt_16}
      />
    </CommonCard>
  );
};

export default ContactDetails;
