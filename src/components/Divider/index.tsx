import { CustomDividerProps, DividerProps } from 'componentsProps';
import React from 'react';
import { Text, View } from 'react-native';
import { useTheme } from '../../theme';
import { marginStyles } from '../../theme/styles/commonMarginPadding';
import { globalStyles, widthStyles } from '../../theme/styles/globalStyles';

const Divider: React.FC<DividerProps> = ({ color, thickness = 2, style }) => {
  return (
    <View
      style={[
        style,
        {
          height: thickness,
          backgroundColor: color,
        },
        widthStyles.w100,
      ]}
    />
  );
};

/*
DividerWithText
here we can add the text on the top of the divider .
*/

export const DividerWithText: React.FC<CustomDividerProps> = ({
  color = 'grey_200',
  height = 2,
  padding = 0,
  margin = 0,
  style,
  text,
  textStyle,
}) => {
  const theme = useTheme();
  const dividerColor = theme[color as keyof typeof theme];

  return (
    <View
      style={[
        widthStyles.w100,
        globalStyles.flexDirectionRow,
        globalStyles.alignItemsCenter,
        {
          paddingTop: padding,
          paddingBottom: padding,
          marginTop: margin,
          marginBottom: margin,
        },
        ...(style || []),
      ]}
    >
      <View
        style={[globalStyles.flex1, { height, backgroundColor: dividerColor }]}
      />
      {text && (
        <Text
          style={[
            marginStyles.mx_8,
            {
              color: dividerColor,
              backgroundColor: theme.transparent,
            },
            ...(textStyle || []),
          ]}
        >
          {text}
        </Text>
      )}
      <View
        style={[{ height, backgroundColor: dividerColor }, globalStyles.flex1]}
      />
    </View>
  );
};

export default Divider;
