import { CalendarModalProps } from 'componentsProps';
import moment from 'moment';
import React, { FC, useEffect, useState } from 'react';
import { Text, View } from 'react-native';
import { Calendar } from 'react-native-calendars';
import Modal from 'react-native-modal';
import { RFValue } from 'react-native-responsive-fontsize';
import { useSelector } from 'react-redux';
import { LeftArrow, RightArrow } from '../../assets/images/index';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontStyles } from '../../theme/fonts';
import { marginStyles } from '../../theme/styles/commonMarginPadding';
import { globalStyles } from '../../theme/styles/globalStyles';

export const CalendarModal: FC<CalendarModalProps> = ({
  onclose,
  open,
  value,
  maximumDate = undefined,
  minimumDate,
  isWeekday,
  title,
  titleStyle,
  onChangeText,
}) => {
  const { isRTL } = useSelector((state: RootState) => state.app);
  const colors = useTheme();
  const [date, setDate] = useState(value);

  useEffect(() => {
    setDate(value);
    return () => {
      setDate(value);
    };
  }, [open, value]);
  return (
    <Modal
      onBackdropPress={() => {
        onclose();
      }}
      onBackButtonPress={() => {
        onclose();
      }}
      isVisible={open}
    >
      <View
        style={{
          backgroundColor: colors?.neutral_white,
          padding: RFValue(16),
          borderRadius: RFValue(12),
        }}
      >
        <Text
          style={[
            fontStyles?.Maison_600_24PX_30LH,
            globalStyles?.letterSpacingN1,
            titleStyle,
          ]}
        >
          {title}
        </Text>

        <Calendar
          style={marginStyles?.mt_16}
          renderArrow={(direction: string) =>
            direction === 'left' ? (
              <LeftArrow color={colors?.primary_cream} />
            ) : (
              <RightArrow color={colors?.primary_cream} />
            )
          }
          current={date}
          initialDate={date}
          onDayPress={(day: { dateString: string }) => {
            if (day?.dateString) {
              setDate(day?.dateString);
              onChangeText?.(day?.dateString);
              onclose?.();
            }
          }}
          maxDate={maximumDate}
          minDate={minimumDate}
          markedDates={
            date && {
              [moment(date).format('YYYY-MM-DD')]: {
                selected: true,
                selectedColor: colors?.primary_grenade,
              },
            }
          }
          theme={{
            todayTextColor: colors?.primary_cream,
            textDisabledColor: '#d9e1e8',
            arrowColor: colors?.primary_cream,
            arrowStyle: { transform: [{ scaleX: isRTL ? -1 : 1 }] },
            'stylesheet.calendar.main': {
              week: {
                marginTop: 7,
                marginBottom: 7,
                flexDirection: isRTL ? 'row-reverse' : 'row',
                justifyContent: 'space-around',
              },
            },
            'stylesheet.calendar.header': {
              header: {
                flexDirection: isRTL ? 'row-reverse' : 'row',
                justifyContent: 'space-between',
                paddingLeft: 10,
                paddingRight: 10,
                alignItems: 'center',
                height: 45,
              },
              week: {
                marginTop: 7,
                flexDirection: isRTL ? 'row-reverse' : 'row',
                justifyContent: 'space-around',
              },
            },
          }}
          disabledByWeekDays={isWeekday ? isWeekday() : []}
        />
      </View>
    </Modal>
  );
};
