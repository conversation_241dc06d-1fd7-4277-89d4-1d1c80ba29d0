import React from 'react';
import {
  Image,
  ImageSourcePropType,
  ImageStyle,
  Text,
  View,
  ViewStyle,
} from 'react-native';
import { useSelector } from 'react-redux';
import { DubaiFlag } from '../../assets/images';
import { RootState } from '../../redux/store';
import { lightTheme } from '../../theme/colors';
import { fontStyles } from '../../theme/fonts';
import { formInputStyles } from '../../theme/styles/inputsStyles';
import {
  globalStyles,
  heightStyles,
  widthStyles,
} from '../../theme/styles/globalStyles';
import { gapStyles } from '../../theme/styles/commonMarginPadding';

interface InputFieldBorderProps {
  children: React.ReactNode;
  style?: ViewStyle[];
}

export const InputFieldBorder = ({
  children,
  style,
}: InputFieldBorderProps) => {
  return (
    <View style={[formInputStyles.customInput, ...(style || [])]}>
      {children}
    </View>
  );
};

export const PhoneInputField = ({
  borderStyle,
  ImageStyle,
  TextStyle,
  imageSource,
  text,
}: {
  borderStyle?: ViewStyle[];
  ImageStyle?: ImageStyle[];
  TextStyle?: ViewStyle[];
  imageSource?: ImageSourcePropType;
  text?: string;
}) => {
  const { isRTL } = useSelector((state: RootState) => state.app);
  return (
    <InputFieldBorder
      style={[
        globalStyles.row,
        globalStyles.alignItemsCenter,
        gapStyles.gap_6,
        ...(borderStyle || []),
      ]}
    >
      <Image
        source={imageSource || DubaiFlag}
        style={[widthStyles.w20, heightStyles.h20, ...(ImageStyle || [])]}
      />
      <Text
        style={[
          fontStyles.Maison_500_18PX_22LH,
          isRTL ? globalStyles.textRight : globalStyles.textLeft,
          {
            color: lightTheme.grey_400,
          },
          ...(TextStyle || []),
        ]}
      >
        {text || '+971'}
      </Text>
    </InputFieldBorder>
  );
};
