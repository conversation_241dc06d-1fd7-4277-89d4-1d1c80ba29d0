import { OtpInputProps } from 'componentsProps';
import React, { useEffect, useRef, useState } from 'react';
import {
  NativeSyntheticEvent,
  TextInput,
  TextInputKeyPressEventData,
  View,
} from 'react-native';
import { useTheme } from '../../theme';
import { fontStyles } from '../../theme/fonts';
import {
  gapStyles,
  paddingStyles,
} from '../../theme/styles/commonMarginPadding';
import {
  borderRadiusStyles,
  borderWidthStyles,
  globalStyles,
} from '../../theme/styles/globalStyles';

const OtpInput: React.FC<OtpInputProps> = ({ length = 4, onComplete }) => {
  const [otp, setOtp] = useState<string[]>(new Array(length).fill(''));
  const inputRefs = useRef<TextInput[]>([]);
  const colors = useTheme();

  useEffect(() => {
    // Focus first input on mount
    inputRefs.current[0]?.focus();
  }, []);

  const handleChange = (text: string, index: number) => {
    const newOtp = [...otp];
    newOtp[index] = text;
    setOtp(newOtp);

    // Move to next input if value entered
    if (text && index < length - 1) {
      inputRefs.current[index + 1]?.focus();
    }

    // Call onComplete when all digits entered
    if (newOtp.every((digit) => digit !== '')) {
      onComplete?.(newOtp.join(''));
    }
  };

  const handleKeyPress = (
    event: NativeSyntheticEvent<TextInputKeyPressEventData>,
    index: number,
  ) => {
    // Move to previous input on backspace and clear value
    if (event.nativeEvent.key === 'Backspace') {
      const newOtp = [...otp];

      if (!otp[index] && index > 0) {
        // If current input is empty, move to previous and clear it
        newOtp[index - 1] = '';
        setOtp(newOtp);
        inputRefs.current[index - 1]?.focus();
      } else {
        // Clear current input
        newOtp[index] = '';
        setOtp(newOtp);
        if (index > 0) {
          inputRefs.current[index - 1]?.focus();
        }
      }
    }
  };

  return (
    <View style={[globalStyles.row, gapStyles.gap_8]}>
      {otp.map((digit, index) => (
        <TextInput
          key={index}
          ref={(ref) => {
            if (ref) {
              inputRefs.current[index] = ref;
            }
          }}
          style={[
            globalStyles.flex1,
            globalStyles.textAlignCenter,
            borderRadiusStyles.br32,
            borderWidthStyles.bw1,
            fontStyles.Maison_600_24PX_30LH,
            paddingStyles.p16,
            {
              borderColor: colors?.grey_100,
            },
          ]}
          value={digit}
          onChangeText={(text) => {
            // Always allow new input even if field is filled
            handleChange(text.slice(-1), index);
            // Move to next input if there is a value and not last field
            if (text && index < length - 1) {
              inputRefs.current[index + 1]?.focus();
            }
          }}
          onKeyPress={(e) => handleKeyPress(e, index)}
          keyboardType="numeric"
          maxLength={1}
          selectTextOnFocus
        />
      ))}
    </View>
  );
};

export default OtpInput;
