import { VerifyCardProps } from 'card-props';
import { useEffect, useState } from 'react';
import { Text, View } from 'react-native';
import { useSelector } from 'react-redux';
import { LeftArrow } from '../../../assets/images';
import { RootState } from '../../../redux/store';
import { useTheme } from '../../../theme';
import { fontStyles } from '../../../theme/fonts';
import {
  gapStyles,
  marginStyles,
  paddingStyles,
} from '../../../theme/styles/commonMarginPadding';
import {
  borderRadiusStyles,
  globalStyles,
  widthStyles,
} from '../../../theme/styles/globalStyles';
import { PrimaryBtn } from '../../Buttons/Btns';
import { ModalCard } from '../../Card/Card';
import OtpInput from '../OtpInput';

export const VerifyCard = ({ setSteps, phoneNumber }: VerifyCardProps) => {
  const { translation } = useSelector((state: RootState) => state.auth);
  const { isRTL } = useSelector((state: RootState) => state.app);
  const colors = useTheme();
  const [counter, setCounter] = useState(120); // 2 minutes in seconds

  useEffect(() => {
    const timer =
      counter > 0 &&
      setInterval(() => {
        setCounter((prev) => prev - 1);
      }, 1000);

    return () => {
      if (timer) clearInterval(timer);
    };
  }, [counter]);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
  };

  return (
    <View style={globalStyles.flex1}>
      <View style={[paddingStyles.p16, globalStyles.flex1]}>
        <ModalCard
          ShowTitleLeftIcon={LeftArrow}
          leftIconClick={() => {
            setSteps(1);
          }}
          title={translation.VERIFY_CODE}
          titleStyle={[
            fontStyles.Marison_600_40PX_44LH,
            {
              color: colors?.grey_900,
            },
            globalStyles.alignContentCenter,
          ]}
          showCloseIcon={true}
          onClose={() => {
            setSteps(0);
          }}
        >
          <View
            style={[gapStyles.gap_12, widthStyles.w100, marginStyles.mt_auto]}
          >
            <Text
              style={[
                fontStyles.Marison_600_22PX_28LH,
                isRTL ? globalStyles.textRight : globalStyles.textLeft,
                { color: colors?.grey_900 },
              ]}
            >
              {translation.ENTER_6_DIGIT_CODE_SENT_TO}
              +971 {phoneNumber ? phoneNumber : ''}
            </Text>
            <View
              style={[
                widthStyles.w100,
                globalStyles.flexDirectionColumn,
                gapStyles.gap_24,
                globalStyles.justifyContentCenter,
              ]}
            >
              <OtpInput />
              <Text
                style={[
                  fontStyles.Maison_500_16PX_19LH,
                  globalStyles.textAlignCenter,
                  {
                    color:
                      counter > 0 ? colors?.grey_600 : colors?.primary_grenade,
                  },
                ]}
              >
                {counter > 0
                  ? `Resend code in ${formatTime(counter)}`
                  : 'Resend Code'}
              </Text>
            </View>
          </View>
        </ModalCard>
      </View>
      <View style={widthStyles.w100}>
        <View
          style={[
            paddingStyles.pt20,
            paddingStyles.pb12,
            paddingStyles.px16,
            gapStyles.gap_10,
            globalStyles.alignSelfStretch,
            borderRadiusStyles.bt20,
            {
              backgroundColor: colors?.neutral_white,
            },
          ]}
        >
          <PrimaryBtn
            text={translation.VERIFY_CODE}
            onPress={() => {
              setSteps(3);
            }}
          />
        </View>
      </View>
    </View>
  );
};
