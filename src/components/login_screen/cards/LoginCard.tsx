import { GoogleSigninButton } from '@react-native-google-signin/google-signin';
import { useNavigation } from '@react-navigation/native';
import { LoginCardProps } from 'card-props';
import { useState } from 'react';
import { Text, View } from 'react-native';
import { TextInput } from 'react-native-gesture-handler';
import { useSelector } from 'react-redux';
import { RootState } from '../../../redux/store';
import { useTheme } from '../../../theme';
import { fontStyles } from '../../../theme/fonts';
import {
  gapStyles,
  paddingStyles,
} from '../../../theme/styles/commonMarginPadding';
import {
  borderRadiusStyles,
  globalStyles,
  widthStyles,
} from '../../../theme/styles/globalStyles';
import { PrimaryBtn } from '../../Buttons/Btns';
import { ModalCard } from '../../Card/Card';
import { DividerWithText } from '../../Divider';
import { InputFieldBorder, PhoneInputField } from '../Custominputfield';

export const LoginCard = ({
  setSteps,
  phoneNumber,
  setPhoneNumber,
}: LoginCardProps) => {
  const { translation } = useSelector((state: RootState) => state.auth);
  const { isRTL } = useSelector((state: RootState) => state.app);
  const colors = useTheme();
  const navigation = useNavigation();
  const [error, setError] = useState<any>(null);
  const [checkingSession, setCheckingSession] = useState(true);

  return (
    <View style={globalStyles.flex1}>
      <View style={[paddingStyles.p16, globalStyles.flex1]}>
        <ModalCard
          title={translation.LOGIN_TO_DELICUT}
          showCloseIcon={true}
          onClose={() => {
            setSteps(0);
          }}
        >
          <View
            style={[
              globalStyles.flex1,
              globalStyles.justifyContentEnd,
              globalStyles.alignItemsFlexStart,
            ]}
          >
            <GoogleSigninButton
              style={{}}
              size={GoogleSigninButton.Size.Wide}
              color={GoogleSigninButton.Color.Dark}
              onPress={signIn}
            />
            {/* )} */}
            {error && (
              <Text style={{}}>
                Error: {error.message} (Code: {error.code})
              </Text>
            )}

            <DividerWithText
              margin={12}
              text={translation.OR}
              padding={12}
              textStyle={[fontStyles.Maison_500_16PX_18LH]}
              color={colors?.grey_200}
            />
            <View style={[gapStyles.gap_12, widthStyles.w100]}>
              <Text
                style={[
                  fontStyles.Maison_500_16PX_18LH,
                  isRTL ? globalStyles.textRight : globalStyles.textLeft,
                  { color: colors?.grey_900 },
                ]}
              >
                {translation.LOGIN_USING_PHONE_NUMBER}
              </Text>
              <View
                style={[
                  widthStyles.w100,
                  globalStyles.flexDirectionRow,
                  gapStyles.gap_12,
                ]}
              >
                <PhoneInputField />
                <InputFieldBorder
                  style={[
                    paddingStyles.px20,
                    paddingStyles.py16,
                    globalStyles.flex1,
                  ]}
                >
                  <TextInput
                    keyboardType="numeric"
                    maxLength={10}
                    style={[
                      fontStyles.Maison_500_18PX_22LH,
                      widthStyles.w100,
                      { color: colors?.grey_900 },
                    ]}
                    value={phoneNumber}
                    onChangeText={setPhoneNumber}
                  />
                </InputFieldBorder>
              </View>
            </View>
          </View>
        </ModalCard>
      </View>
      <View style={widthStyles.w100}>
        <View
          style={[
            paddingStyles.pt20,
            paddingStyles.pb12,
            paddingStyles.px16,
            gapStyles.gap_10,
            globalStyles.alignSelfStretch,
            borderRadiusStyles.bt20,
            { backgroundColor: colors?.neutral_white },
          ]}
        >
          <PrimaryBtn
            text={translation.CONTINUE}
            onPress={() => {
              if (phoneNumber.length === 10) {
                setSteps(2);
              }
            }}
          />
          <View
            style={[
              globalStyles.flexDirectionColumn,
              gapStyles.gap_10,
              globalStyles.alignItemsCenter,
            ]}
          >
            <Text
              style={[
                fontStyles.Maison_400_12PX_16LH,
                { color: colors?.grey_600 },
              ]}
            >
              {translation.BY_CONTINUING_YOU_AGREE_TO_DELICUT_S}
            </Text>

            <View
              style={[
                globalStyles.row,
                globalStyles.alignItemsCenter,
                gapStyles.gap_2,
              ]}
            >
              <Text
                style={[
                  fontStyles.Marison_600_12PX_14LH,
                  { color: colors?.grey_600 },
                ]}
              >
                {translation.TERMS_AND_CONDITIONS}
              </Text>

              <Text style={fontStyles.Maison_400_12PX_14LH}>
                {translation.AND}
              </Text>

              <Text
                style={[
                  fontStyles.Marison_600_12PX_14LH,
                  { color: colors?.grey_600 },
                ]}
              >
                {translation.PRIVACY_POLICY}
              </Text>
            </View>
          </View>
        </View>
      </View>
    </View>
  );
};
