import React, { useEffect, useState } from 'react';
import { Text, View } from 'react-native';
import { useSelector } from 'react-redux';
import { Loader, Success } from '../../../assets/images';
import { RootState } from '../../../redux/store';
import { lightTheme } from '../../../theme/colors';
import { fontStyles } from '../../../theme/fonts';
import {
  gapStyles,
  paddingStyles,
} from '../../../theme/styles/commonMarginPadding';
import {
  globalStyles,
  heightStyles,
  widthStyles,
} from '../../../theme/styles/globalStyles';
import { ModalCard } from '../../Card/Card';

export const SpinnerCard = () => {
  const { translation } = useSelector((state: RootState) => state.auth);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <View style={globalStyles.flex1}>
      <View style={[paddingStyles.p16, globalStyles.flex1]}>
        <ModalCard
          titleStyle={[
            fontStyles.Marison_600_40PX_44LH,
            globalStyles.alignContentCenter,
            {
              color: lightTheme.grey_900,
            },
          ]}
          showCloseIcon={false}
          onClose={() => {}}
        >
          <View
            style={[
              globalStyles.alignItemsCenter,
              globalStyles.justifyContentCenter,
              widthStyles.w100,
              heightStyles.h100,
              gapStyles.gap_20,
            ]}
          >
            {isLoading ? (
              <>
                <Loader width={48} height={48} />
                <Text
                  style={[
                    fontStyles.Maison_600_24PX_30LH,
                    {
                      color: lightTheme.grey_900,
                    },
                  ]}
                >
                  {translation.VERIFYING_YOUR_OTP}
                </Text>
              </>
            ) : (
              <>
                <Success width={48} height={48} />
                <Text
                  style={[
                    fontStyles.Maison_600_24PX_30LH,
                    {
                      color: lightTheme.grey_900,
                    },
                  ]}
                >
                  {translation.CODE_VERIFIED_SUCCESSFULLY}
                </Text>
              </>
            )}
          </View>
        </ModalCard>
      </View>
    </View>
  );
};
