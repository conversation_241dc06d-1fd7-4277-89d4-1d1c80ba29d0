import React from 'react';
import { Text, View } from 'react-native';
import { RFValue } from 'react-native-responsive-fontsize';
import { User } from '../../assets/images';
import { useTheme } from '../../theme';
import { fontStyles } from '../../theme/fonts';
import { cardStyles } from '../../theme/styles/cardStyles';
import {
  gapStyles,
  marginStyles,
  paddingStyles,
} from '../../theme/styles/commonMarginPadding';
import { globalStyles } from '../../theme/styles/globalStyles';
import { CommonCard } from '../Card/Card';

const YourDetails = () => {
  const colors = useTheme();
  return (
    <CommonCard style={cardStyles.cardR16_P16_BoxShadow1}>
      <View
        style={[
          globalStyles.flexDirectionRow,
          globalStyles.justifyContentSpaceBetween,
          globalStyles.flex1,
          marginStyles.mb_12,
        ]}
      >
        <View
          style={[
            globalStyles.flex1,
            globalStyles.flexDirectionRow,
            gapStyles.gap_8,
            globalStyles.alignItemsCenter,
          ]}
        >
          <User width={RFValue(20)} height={RFValue(20)} />
          <Text
            style={[
              fontStyles.Maison_600_16PX_22LH,
              { color: colors?.black_900 },
            ]}
          >
            Your details
          </Text>
        </View>
        <Text
          style={[
            fontStyles.Maison_600_14PX_18LH,
            { color: colors?.primary_grenade },
          ]}
        >
          Change
        </Text>
      </View>
      <View style={[paddingStyles.py12, gapStyles.gap_16]}>
        <View style={gapStyles.gap_8}>
          <Text
            style={[
              fontStyles.Maison_500_14PX_18LH,
              { color: colors?.grey_900 },
            ]}
          >
            Jonathan Green
          </Text>
          <Text
            style={[
              fontStyles.Maison_400_14PX_18LH,
              { color: colors?.grey_600 },
            ]}
          >
            +971549424373
          </Text>
          <Text
            style={[
              fontStyles.Maison_400_14PX_18LH,
              { color: colors?.grey_600 },
            ]}
          >
            Dusit Thani Dubai, United Arab Emirates, Pet at home
          </Text>
        </View>
      </View>
    </CommonCard>
  );
};

export default YourDetails;
