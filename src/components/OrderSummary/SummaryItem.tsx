import { Text, View } from 'react-native';
import { useSelector } from 'react-redux';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontStyles } from '../../theme/fonts';
import { gapStyles } from '../../theme/styles/commonMarginPadding';
import { globalStyles } from '../../theme/styles/globalStyles';

const SummaryItem = () => {
  const colors = useTheme();
  const { isRTL } = useSelector((state: RootState) => state.app);
  return (
    <View
      style={[
        isRTL ? globalStyles.rowReverse : globalStyles.flexDirectionRow,
        globalStyles.flex1,
        globalStyles.alignItemsCenter,
        globalStyles.justifyContentSpaceBetween,
        gapStyles.gap_6,
      ]}
    >
      <Text style={[globalStyles.flex1, fontStyles.Maison_400_16PX_22LH]}>
        Order total
      </Text>

      <View
        style={[
          globalStyles.justifyContentEnd,
          globalStyles.flex1,
          globalStyles.flexDirectionRow,
          gapStyles.gap_8,
          globalStyles.alignItemsFlexEnd,
        ]}
      >
        <Text
          style={[fontStyles.Maison_400_14PX_18LH, { color: colors?.grey_700 }]}
        >
          AED
        </Text>
        <Text
          style={[fontStyles.Maison_500_16PX_22LH, { color: colors?.grey_900 }]}
        >
          2,394.20
        </Text>
      </View>
    </View>
  );
};
export default SummaryItem;
