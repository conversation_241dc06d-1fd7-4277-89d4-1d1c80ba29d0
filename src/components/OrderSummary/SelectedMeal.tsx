import React from 'react';
import { Text, View } from 'react-native';
import { RFValue } from 'react-native-responsive-fontsize';
import { MealBowlIcon } from '../../assets/images';
import { useTheme } from '../../theme';
import { fontStyles } from '../../theme/fonts';
import { cardStyles } from '../../theme/styles/cardStyles';
import {
  gapStyles,
  marginStyles,
  paddingStyles,
} from '../../theme/styles/commonMarginPadding';
import { globalStyles } from '../../theme/styles/globalStyles';
import { CommonCard } from '../Card/Card';

const SelectedMeal = () => {
  const colors = useTheme();
  return (
    <CommonCard style={cardStyles.cardR16_P16_BoxShadow1}>
      <View
        style={[
          globalStyles.flexDirectionRow,
          globalStyles.justifyContentSpaceBetween,
          globalStyles.flex1,
          marginStyles.mb_12,
        ]}
      >
        <View
          style={[
            globalStyles.flex1,
            globalStyles.flexDirectionRow,
            gapStyles.gap_8,
            globalStyles.alignItemsCenter,
          ]}
        >
          <MealBowlIcon width={RFValue(20)} height={RFValue(20)} />
          <Text
            style={[
              fontStyles.Maison_600_16PX_22LH,
              { color: colors?.black_900 },
            ]}
          >
            Your meal subscription
          </Text>
        </View>
        <Text
          style={[
            fontStyles.Maison_600_14PX_18LH,
            { color: colors?.primary_grenade },
          ]}
        >
          Edit
        </Text>
      </View>
      <View style={[paddingStyles.py12, gapStyles.gap_16]}>
        <View style={gapStyles.gap_8}>
          <Text
            style={[
              fontStyles.Maison_500_14PX_18LH,
              { color: colors?.grey_900 },
            ]}
          >
            Low carb meal
          </Text>
          <Text
            style={[
              fontStyles.Maison_400_14PX_18LH,
              { color: colors?.grey_600 },
            ]}
          >
            1 Breakfast + 1 Lunch + 1 Dinner + 2 Snack for 4 weeks
          </Text>
        </View>
        <View style={gapStyles.gap_8}>
          <Text
            style={[
              fontStyles.Maison_500_14PX_18LH,
              { color: colors?.grey_900 },
            ]}
          >
            Starts 7th April 2025
          </Text>
          <Text
            style={[
              fontStyles.Maison_400_14PX_18LH,
              { color: colors?.grey_600 },
            ]}
          >
            2PM - 6PM
          </Text>
        </View>
      </View>
    </CommonCard>
  );
};

export default SelectedMeal;
