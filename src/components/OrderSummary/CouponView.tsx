import React from 'react';
import { Text, View } from 'react-native';
import { CouponIcon } from '../../assets/images';
import { useTheme } from '../../theme';
import { fontStyles, RFont } from '../../theme/fonts';
import { cardStyles } from '../../theme/styles/cardStyles';
import { gapStyles } from '../../theme/styles/commonMarginPadding';
import { globalStyles } from '../../theme/styles/globalStyles';
import { CommonCard } from '../Card/Card';

const CouponView = () => {
  const colors = useTheme();
  return (
    <CommonCard style={cardStyles.cardR16_P16_BoxShadow1}>
      <View
        style={[
          globalStyles.flexDirectionRow,
          globalStyles.alignItemsCenter,
          gapStyles.gap_8,
          globalStyles.flex1,
        ]}
      >
        <View
          style={[
            globalStyles.flex1,
            globalStyles.flexDirectionRow,
            gapStyles.gap_8,
            globalStyles.alignItemsCenter,
          ]}
        >
          <CouponIcon width={RFont(20)} height={RFont(20)} />
          <Text
            style={[
              fontStyles.Maison_500_14PX_18LH,
              { color: colors?.grey_900 },
            ]}
          >
            <Text
              style={[
                fontStyles.Maison_600_14PX_18LH,
                { color: colors?.grey_900 },
              ]}
            >
              {' '}
              AED 359.13
            </Text>{' '}
            saved with PAYDAY15
          </Text>
        </View>
        <Text
          style={[
            fontStyles.Maison_600_14PX_16_8LH,
            { color: colors?.primary_grenade },
          ]}
        >
          Remove
        </Text>
      </View>
    </CommonCard>
  );
};

export default CouponView;
