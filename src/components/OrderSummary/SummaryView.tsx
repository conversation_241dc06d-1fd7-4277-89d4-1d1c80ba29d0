import React from 'react';
import { Text, View } from 'react-native';
import { useSelector } from 'react-redux';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontStyles } from '../../theme/fonts';
import { cardStyles } from '../../theme/styles/cardStyles';
import {
  gapStyles,
  marginStyles,
  paddingStyles,
} from '../../theme/styles/commonMarginPadding';
import { globalStyles } from '../../theme/styles/globalStyles';
import { CommonCard } from '../Card/Card';
import Divider from '../Divider';
import SummaryItem from './SummaryItem';

const SummaryView = () => {
  const colors = useTheme();
  const { isRTL } = useSelector((state: RootState) => state.app);
  return (
    <CommonCard style={cardStyles.cardR16_P16_BoxShadow1}>
      <Text
        style={[
          fontStyles.Maison_600_16PX_22LH,
          marginStyles.mb_12,
          { color: colors?.black_900 },
        ]}
      >
        Payment summary
      </Text>
      <View style={[paddingStyles.pt12, gapStyles.gap_12]}>
        <SummaryItem />
        <SummaryItem />
        <SummaryItem />
        <Divider color={colors?.grey_100} />
        <View
          style={[
            isRTL ? globalStyles.rowReverse : globalStyles.flexDirectionRow,
            globalStyles.flex1,
            globalStyles.alignItemsCenter,
            globalStyles.justifyContentSpaceBetween,
            gapStyles.gap_6,
          ]}
        >
          <Text
            style={[
              fontStyles.Maison_600_18PX_24LH,
              { color: colors?.grey_900 },
            ]}
          >
            Total to pay
          </Text>
          <View
            style={[
              isRTL ? globalStyles.rowReverse : globalStyles.flexDirectionRow,
              globalStyles.alignItemsCenter,
              gapStyles.gap_8,
            ]}
          >
            <Text
              style={[
                fontStyles.Maison_600_18PX_24LH,
                { color: colors?.grey_900 },
              ]}
            >
              AED
            </Text>
            <View
              style={[
                isRTL ? globalStyles.rowReverse : globalStyles.flexDirectionRow,
                globalStyles.alignItemsCenter,
              ]}
            >
              <Text style={fontStyles.Maison_600_18PX_24LH}>2,154</Text>
              <Text style={fontStyles.Maison_600_14PX_24LH}>.78</Text>
            </View>
          </View>
        </View>
      </View>
    </CommonCard>
  );
};

export default SummaryView;
