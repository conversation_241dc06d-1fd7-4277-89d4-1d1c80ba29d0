/* eslint-disable react-native/no-unused-styles */
/* eslint-disable react-native/no-inline-styles */
import React from 'react';
import { Dimensions, StyleSheet, Text, View } from 'react-native';
import { useTheme } from '../../theme';
import { fontStyles, RFont } from '../../theme/fonts';
import { globalStyles } from '../../theme/styles/globalStyles';
import { PrimaryBtn } from '../Buttons/Btns';

const BottomPayView = () => {
  const colors = useTheme();
  return (
    <View
      style={[
        style?.card,
        {
          width: Dimensions.get('window').width,
          backgroundColor: colors?.neutral_white,
        },
      ]}
    >
      <PrimaryBtn text="Pay AED 2,154.78" />
      <Text
        style={[fontStyles.Maison_400_16PX_20LH, globalStyles.textAlignCenter]}
      >
        🎉 Earn AED 323 cashback on this order
      </Text>
    </View>
  );
};
const style = StyleSheet.create({
  card: {
    borderTopEndRadius: RFont(20),
    borderTopStartRadius: RFont(20),
    bottom: 0,
    gap: RFont(16),
    paddingHorizontal: RFont(16),
    paddingVertical: RFont(12),
    position: 'absolute',
  },
});
export default BottomPayView;
