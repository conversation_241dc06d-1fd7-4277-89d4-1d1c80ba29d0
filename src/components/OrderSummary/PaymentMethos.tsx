import React, { FC, useState } from 'react';
import { Pressable, Text, View } from 'react-native';
import { PaymentIcon } from 'react-native-payment-icons';
import { useSelector } from 'react-redux';
import { ApplePayIcon, CardIcon, GPayIcon } from '../../assets/images';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontFamily, fontStyles, RFont } from '../../theme/fonts';
import { cardStyles } from '../../theme/styles/cardStyles';
import {
  gapStyles,
  marginStyles,
  paddingStyles,
} from '../../theme/styles/commonMarginPadding';
import {
  borderRadiusStyles,
  globalStyles,
} from '../../theme/styles/globalStyles';
import { GorhomBottomSheet } from '../BottomSheet/BottomSheet';
import { AddNewCardBtn } from '../Buttons/AddNewCardBtn';
import { PrimaryBtn } from '../Buttons/Btns';
import { RadioBox } from '../Buttons/Checkbox';
import { CommonCard } from '../Card/Card';
interface MethodCardsProps {
  isSelected?: boolean;
  type?:
    | 'g-pay'
    | 'apple-pay'
    | 'code'
    | 'unionpay'
    | 'alipay'
    | 'amex'
    | 'american-express'
    | 'cvv'
    | 'diners-club'
    | 'diners'
    | 'discover'
    | 'elo'
    | 'generic'
    | 'hiper'
    | 'hipercard'
    | 'jcb'
    | 'maestro'
    | 'mastercard'
    | 'master'
    | 'mir'
    | 'paypal'
    | 'visa';
  title: string;
  onPressChange?: () => void;
  showChange?: boolean;
  onPressCard?: () => void;
  isBorder?: boolean;
}

const MethodCards: FC<MethodCardsProps> = ({
  isSelected,
  type = 'generic',
  title,
  onPressChange,
  showChange,
  onPressCard,
  isBorder,
}) => {
  const { isRTL } = useSelector((state: RootState) => state.app);
  const colors = useTheme();
  return (
    <Pressable
      onPress={onPressCard}
      style={[
        isRTL ? globalStyles.rowReverse : globalStyles.flexDirectionRow,
        gapStyles.gap_8,
        paddingStyles.p8,
        globalStyles.alignItemsCenter,
        borderRadiusStyles.br16,
        globalStyles.flex1,
        (isSelected || isBorder) && globalStyles.border2,
        {
          borderColor: isSelected ? colors?.primary_grenade : colors?.grey_100,
        },
      ]}
    >
      <RadioBox value={isSelected} />
      <View
        style={[
          globalStyles.flex1,
          globalStyles.alignItemsCenter,
          gapStyles.gap_8,
          isRTL ? globalStyles.rowReverse : globalStyles.flexDirectionRow,
        ]}
      >
        <Text
          style={[fontStyles.Maison_500_14PX_18LH, { color: colors?.grey_900 }]}
        >
          {title}
        </Text>
        {showChange && (
          <Text
            onPress={onPressChange}
            style={[
              fontStyles.Maison_600_14PX_18LH,
              { color: colors?.primary_grenade },
            ]}
          >
            Change
          </Text>
        )}
      </View>
      {type == 'g-pay' ? (
        <GPayIcon width={RFont(32)} />
      ) : type == 'apple-pay' ? (
        <ApplePayIcon width={RFont(32)} />
      ) : (
        <PaymentIcon type={type} width={RFont(32)} />
      )}
    </Pressable>
  );
};

const PaymentMethos = () => {
  const [selectCardSheet, setSelectCardSheet] = useState(false);
  const [newCardSheet, setNewCardSheet] = useState(false);
  const { isRTL } = useSelector((state: RootState) => state.app);
  const colors = useTheme();
  return (
    <CommonCard style={cardStyles.cardR16_P16_BoxShadow1}>
      <View
        style={[
          globalStyles.flex1,
          isRTL ? globalStyles.rowReverse : globalStyles.flexDirectionRow,
          gapStyles.gap_8,
          globalStyles.alignItemsCenter,
          marginStyles.mb_12,
        ]}
      >
        <CardIcon width={RFont(20)} height={RFont(20)} />
        <Text
          style={[
            fontStyles.Maison_600_16PX_22LH,
            { color: colors?.black_900 },
          ]}
        >
          Payment Method
        </Text>
      </View>
      <View style={marginStyles.mb_12}>
        <MethodCards title="Apple pay" type="apple-pay" isSelected />
        <MethodCards title="Google Pay" type="g-pay" />
        <MethodCards
          title="***** ***** 1056"
          showChange
          onPressChange={() => {
            setSelectCardSheet(true);
          }}
          onPressCard={() => {}}
        />
        <AddNewCardBtn onPress={() => setNewCardSheet(true)} />
      </View>
      <Text
        style={[
          fontStyles.Maison_400_12PX_16LH,
          globalStyles.textAlignCenter,
          { color: colors?.primary_spinach },
        ]}
      >
        🔒 Payments are{' '}
        <Text style={{ fontFamily: fontFamily.MaisonDemi }}>
          100% secure & encrypted
        </Text>
      </Text>
      <GorhomBottomSheet
        sheetOpen={selectCardSheet}
        sheetClose={() => setSelectCardSheet(false)}
        title="Select your card"
        desc="Choose your card or add a new one."
        footer={
          <PrimaryBtn
            text="Save & continue"
            style={[marginStyles.my_12, marginStyles.mx_16]}
          />
        }
      >
        <View style={gapStyles.gap_16}>
          <MethodCards title="**** **** 1056" isBorder />
          <MethodCards title="**** **** 1059" isBorder isSelected />
          <AddNewCardBtn
            onPress={() => {
              setSelectCardSheet(false);
              setTimeout(() => {
                setNewCardSheet(true);
              }, 400);
            }}
          />
        </View>
      </GorhomBottomSheet>
      <GorhomBottomSheet
        sheetOpen={newCardSheet}
        sheetClose={() => setNewCardSheet(false)}
        title="Add new card"
        footer={
          <PrimaryBtn
            text="Add New Card"
            style={[marginStyles.my_12, marginStyles.mx_16]}
          />
        }
      >
        <View style={gapStyles.gap_16}></View>
      </GorhomBottomSheet>
    </CommonCard>
  );
};

export default PaymentMethos;
