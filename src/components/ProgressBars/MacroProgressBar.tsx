import React, { <PERSON> } from 'react';
import { View } from 'react-native';
import { ProgressBarProps } from '../../../@types/progressBar';
import { macrosCardStyles } from '../../theme/styles/cardStyles';

const MacroProgressBar: FC<ProgressBarProps> = ({ style }) => {
  return (
    <View style={[macrosCardStyles.macrosViewIndicator, style]}>
      <View style={[macrosCardStyles.indicatorCircle, style]} />
    </View>
  );
};

export default MacroProgressBar;
