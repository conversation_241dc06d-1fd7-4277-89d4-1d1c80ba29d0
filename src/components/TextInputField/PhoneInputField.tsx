import { FormInputProps } from 'componentsProps';
import React, { useState } from 'react';
import { Text, TextInput, View } from 'react-native';
import { RFValue } from 'react-native-responsive-fontsize';
import { useSelector } from 'react-redux';
import { UaeFlagIcon } from '../../assets/images';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontStyles } from '../../theme/fonts';
import {
  gapStyles,
  marginStyles,
  paddingStyles,
} from '../../theme/styles/commonMarginPadding';
import { globalStyles } from '../../theme/styles/globalStyles';
import { formInputStyles } from '../../theme/styles/inputsStyles';

export const PhoneInputField: React.FC<FormInputProps> = ({
  error,
  wrapperStyle,
  inputStyles,
  placeholder = '',
  value = undefined,
  onChangeText = undefined,
  header,
  keyboardType = 'ascii-capable',
  disabled,
  autoFocus,
  headerStyle,
  containerStyle,
}) => {
  const { isRTL } = useSelector((state: RootState) => state.app);
  const [isFocused, setIsFocused] = useState(false);
  const colors = useTheme();
  return (
    <View style={[gapStyles?.gap_8, containerStyle]}>
      {header && (
        <Text
          style={[
            isRTL ? globalStyles.textRight : globalStyles.textLeft,
            fontStyles.Maison_500_16PX_18LH,
            { color: colors?.grey_700 },
            headerStyle,
          ]}
        >
          {header}
        </Text>
      )}
      <View
        style={[
          isRTL ? globalStyles.rowReverse : globalStyles.row,
          gapStyles.gap_8,
          formInputStyles.primaryInput,
          paddingStyles.py8,
          {
            borderColor: error
              ? colors?.red_500
              : isFocused
                ? colors?.grey_900
                : colors?.grey_100,
          },
          wrapperStyle,
        ]}
      >
        <View
          style={[
            isRTL ? globalStyles.rowReverse : globalStyles.row,
            gapStyles.gap_6,
          ]}
        >
          <UaeFlagIcon style={marginStyles.mt_2} />
          <Text
            style={[
              isRTL ? globalStyles.textRight : globalStyles.textLeft,
              fontStyles.Maison_500_16PX_19_2LH,
              { color: colors?.grey_700 },
            ]}
          >
            +971
          </Text>
        </View>
        <TextInput
          style={[
            isRTL
              ? globalStyles.writingDirectionRight
              : globalStyles.writingDirectionLeft,
            isRTL ? globalStyles.textRight : globalStyles.textLeft,
            fontStyles.Maison_500_16PX_19_2LH,
            marginStyles.mt_2,
            {
              color: disabled ? colors?.grey_600 : colors?.grey_700,
              backgroundColor: colors?.neutral_white,
            },
            inputStyles,
          ]}
          placeholder={placeholder}
          keyboardType={keyboardType}
          placeholderTextColor={colors?.grey_600}
          onChangeText={onChangeText}
          value={value}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          editable={!disabled}
          autoFocus={autoFocus}
        />
      </View>
      {error && (
        <View style={{ marginTop: RFValue(8) }}>
          {/* <ErrorCard messgae={error?.message} /> */}
        </View>
      )}
    </View>
  );
};
