import { RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { RootFitnessStackParamList } from '../../../@types/navigation';
import { LeftArrow } from '../../assets/images';
import { fontStyles, RFont } from '../../theme/fonts';
import { headerCondition } from '../../utils/constans';

type FitnessHeaderProps = {
  navigation: StackNavigationProp<RootFitnessStackParamList>;
  route: RouteProp<RootFitnessStackParamList>;
  title?: string;
  isExternal?: boolean;
};

export default function FitnessHeader({
  navigation,
  route,
  title,
  isExternal,
}: FitnessHeaderProps) {
  const headerName = headerCondition?.[route?.name]?.backTitle || 'BACK';
  const hideHeader = headerCondition?.[route?.name]?.hideHeader || true;

  return (
    <View
      style={[
        styles.container,
        {
          display: isExternal ? 'flex' : hideHeader ? 'none' : 'flex',
        },
      ]}
    >
      <View style={styles.leftIcon}>
        <LeftArrow onPress={() => navigation.goBack()} />
      </View>
      <View>
        <Text style={fontStyles.Maison_600_18PX_24LH}>
          {title ?? headerName ?? ''}
        </Text>
      </View>
      <View style={styles.rightSpacer} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    backgroundColor: '#F9F4ED',
    borderBottomColor: '#E0E0E0',
    borderBottomWidth: 0.4,
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: RFont(15),
  },
  leftIcon: {
    marginLeft: RFont(10),
  },
  rightSpacer: {
    width: RFont(24), // add width to balance right side visually
  },
});
