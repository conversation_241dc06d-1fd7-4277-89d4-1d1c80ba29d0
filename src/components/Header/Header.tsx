import { useNavigation } from '@react-navigation/native';
import { HeaderProps } from 'componentsProps';
import React, { FC } from 'react';
import { Dimensions, Text, View } from 'react-native';
import { RFValue } from 'react-native-responsive-fontsize';
import { ChevronLeftIcon } from '../../assets/images';
import { useTheme } from '../../theme';
import { fontStyles } from '../../theme/fonts';
import {
  gapStyles,
  paddingStyles,
} from '../../theme/styles/commonMarginPadding';
import { globalStyles } from '../../theme/styles/globalStyles';
import Divider from '../Divider';

const Header: FC<HeaderProps> = ({
  headerTitle,
  showDivider = true,
  textStyle,
}) => {
  const color = useTheme();
  const navigation = useNavigation();
  return (
    <View style={{ backgroundColor: color.primary_cream }}>
      <View
        style={[
          globalStyles.row,
          globalStyles?.alignItemsCenter,
          paddingStyles.p16,
          gapStyles.gap_16,
        ]}
      >
        {navigation.canGoBack() && (
          <ChevronLeftIcon
            onPress={() => {
              navigation.goBack();
            }}
            width={RFValue(20)}
            height={RFValue(20)}
          />
        )}
        <Text style={[fontStyles?.Maison_600_24PX_30LH, textStyle]}>
          {headerTitle}
        </Text>
      </View>
      {showDivider && (
        <View style={{ width: Dimensions.get('window').width * 0.8 }}>
          <Divider color={color.primary_grenade} thickness={RFValue(3)} />
        </View>
      )}
    </View>
  );
};

export default Header;
