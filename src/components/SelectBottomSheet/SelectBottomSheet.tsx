/* eslint-disable react-native/sort-styles */
import { SelectBottomSheetprops } from 'componentsProps';
import React, { useState } from 'react';
import { Pressable, Text, View } from 'react-native';
import { useSelector } from 'react-redux';
import { ChevronDownIcon } from '../../assets/images';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontStyles } from '../../theme/fonts';
import {
  gapStyles,
  marginStyles,
} from '../../theme/styles/commonMarginPadding';
import { globalStyles } from '../../theme/styles/globalStyles';
import { formInputStyles } from '../../theme/styles/inputsStyles';
import AddressSelectBottomSheet from '../BottomSheet/AddressSelectBottomSheet';
// import ErrorCard from '../Cards/ErrorCard';

const SelectBottomSheet: React.FC<SelectBottomSheetprops> = ({
  option = [],
  error,
  value = undefined,
  onChange = () => null,
  header,
  headerStyle,
  bottomSheetTitle = '',
  containerStyle,
}) => {
  const { isRTL } = useSelector((state: RootState) => state.app);
  const [isFocus, setIsFocus] = useState(false);
  const [openBottomSheet, setOpenBottomSheet] = useState(false);
  const color = useTheme();

  return (
    <View style={[gapStyles?.gap_8, containerStyle]}>
      {header && (
        <Text
          style={[
            isRTL ? globalStyles.textRight : globalStyles.textLeft,
            fontStyles.Maison_500_16PX_18LH,
            { color: color.neutral_black },
            headerStyle,
          ]}
        >
          {header}
        </Text>
      )}
      <Pressable
        onFocus={() => setIsFocus(true)}
        onBlur={() => setIsFocus(false)}
        onPress={() => {
          setOpenBottomSheet(true);
        }}
      >
        <View
          style={[
            globalStyles.row,
            formInputStyles.input,
            globalStyles.alignItemsFlexStart,
            globalStyles.justifyContentSpaceBetween,
            {
              borderColor: error
                ? color.red_500
                : isFocus
                  ? color.grey_900
                  : color.grey_100,
            },
          ]}
        >
          <Text
            style={[
              marginStyles.mt_2,
              isRTL ? globalStyles.textRight : globalStyles.textLeft,
              fontStyles.Maison_500_16PX_19_2LH,
              { color: value ? color.neutral_black : color.grey_600 },
            ]}
          >
            {value || 'Select'}
          </Text>
          <ChevronDownIcon style={marginStyles.mt_2} />
        </View>
      </Pressable>
      <AddressSelectBottomSheet
        value={value}
        onChange={onChange}
        option={option}
        title={bottomSheetTitle}
        sheetOpen={openBottomSheet}
        sheetClose={() => {
          setOpenBottomSheet(false);
        }}
      />
      {error && (
        <Text
          style={[fontStyles.Maison_500_14PX_18LH, { color: color.red_500 }]}
        >
          {error}
        </Text>
      )}
    </View>
  );
};

export default SelectBottomSheet;
