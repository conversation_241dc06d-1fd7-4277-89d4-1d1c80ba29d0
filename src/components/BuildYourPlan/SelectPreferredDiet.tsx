import React, { FC, useEffect, useState } from 'react';
import { RFValue } from 'react-native-responsive-fontsize';
import { useSelector } from 'react-redux';
import { SelectPreferredDietProps } from '../../../@types/meal-plan';
import { ActionLeftImg } from '../../assets/images';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { RFont } from '../../theme/fonts';
import { marginStyles } from '../../theme/styles/commonMarginPadding';
import { globalStyles } from '../../theme/styles/globalStyles';
import { getmacrosCardData } from '../../utils/functions';
import { preferedDietData } from '../../utils/global';
import { CommonCard } from '../Card/Card';
import KnowTragetGreenCard from '../Card/KnowTragetGreenCard';
import { SelectableCard } from '../Card/SelectableCard';
import MacroNutrientItem from '../Chips/MacroNutrientItem';
import { MultiStepModal } from '../MultiStepModal/MultiStepModal';

const SelectPreferredDiet: FC<SelectPreferredDietProps> = ({
  addAllNonVegIng,
  cartFormik,
  cartOnchange,
  removeAllNonVegIng,
}) => {
  const colors = useTheme();
  const { translation } = useSelector((state: RootState) => state?.auth);
  const { masterRmsData } = useSelector((state: RootState) => state.app);
  const { values } = cartFormik;
  const recipe_sizes = masterRmsData?.find(
    (e) => e.key === 'recipe_sizes',
  )?.value;
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false);
    }, 3000);
    return () => clearTimeout(timer);
  }, [loading]);
  return (
    <CommonCard title={translation?.SELECT_YOUR_PREFERRED_DIET}>
      <React.Fragment>
        {preferedDietData?.map((itm, idx) => {
          return (
            <SelectableCard
              key={`prefered-diet-${itm}-${idx}`}
              cardStyle={marginStyles.mt_16}
              ingredientsWrapperStyles={{ marginTop: RFValue(16) }}
              isSelected={
                values?.protein_category === itm.value &&
                values.is_vegetarian === itm.isVeg
              }
              title={itm?.lable}
              desc={itm?.desc}
              onPress={() => {
                let obj = {
                  protein_category: itm.value,
                  is_vegetarian: itm.isVeg,
                  avoid_category: [] as string[],
                  avoid_ingredients: [] as string[],
                  kcal_range: values?.kcal_range,
                };
                if (itm.isVeg) {
                  obj = {
                    ...obj,
                    ...addAllNonVegIng(),
                  };
                } else {
                  obj = {
                    ...obj,
                    ...removeAllNonVegIng(),
                  };
                }
                if (
                  values?.kcal_range === 'Large' ||
                  values?.kcal_range === 'Extra_large'
                ) {
                  const newData = recipe_sizes?.find((e) => e.key == 'medium');
                  if (newData) {
                    obj.kcal_range = newData?.value as string;
                  } else {
                    obj.kcal_range = values?.kcal_range;
                  }
                }
                cartOnchange(obj);
              }}
              renderFooter={itm.macrosData?.map((item) => (
                <MacroNutrientItem
                  macroProgressBarStyle={{
                    backgroundColor: getmacrosCardData(item?.title, colors),
                  }}
                  key={item.title}
                  title={
                    translation?.[item?.title as 'PROTEIN' | 'CARB' | 'FAT']
                  }
                  value={item?.value}
                />
              ))}
            />
          );
        })}

        <KnowTragetGreenCard
          onPress={() => {
            setOpen(true);
            setLoading(true);
          }}
          title={translation?.NOT_SURE_WHAT_TO_PICK}
          desc={translation?.NOT_SURE_WHAT_TO_PICK_DESC}
          imageSource={<ActionLeftImg width={RFont(32)} height={RFont(32)} />}
          titleStyle={[globalStyles?.letterSpacingN1]}
        />
      </React.Fragment>
      <MultiStepModal
        onclose={() => setOpen(false)}
        open={open}
        loading={loading}
        setLoading={setLoading}
      />
    </CommonCard>
  );
};

export default SelectPreferredDiet;
