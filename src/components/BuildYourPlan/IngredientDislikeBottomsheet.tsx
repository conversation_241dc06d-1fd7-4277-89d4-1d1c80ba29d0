import React from 'react';
import { Pressable, Text, View } from 'react-native';
import { useSelector } from 'react-redux';
import {
  CalendarCheckIcon,
  ReloadIcon,
  SearchIcon,
  SupportIcon,
  Xclose,
} from '../../assets/images';
import { GorhomBottomSheet } from '../../components/BottomSheet/BottomSheet';
import { MultiSelectPillGroup } from '../../components/Buttons/Btns';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontStyles, RFont } from '../../theme/fonts';
import {
  gapStyles,
  marginStyles,
  paddingStyles,
} from '../../theme/styles/commonMarginPadding';
import {
  borderRadiusStyles,
  globalStyles,
} from '../../theme/styles/globalStyles';
import { SelectableSlotChip } from '../Buttons/Chips';
import MultiSelectField from '../SelectInputField/MultiSelect';

const IngredientDislikeBottomsheet = () => {
  const colors = useTheme();
  const { masterIng, isRTL } = useSelector((state: RootState) => state?.app);
  return (
    <GorhomBottomSheet
      sheetOpen={true}
      title="Select ingredients you dislike"
      desc="We’ll flag meals that contain them."
      customRightIcon={
        <Pressable
          style={[
            globalStyles.border1,
            paddingStyles.p8,
            borderRadiusStyles.br12,
            globalStyles.alignSelfStart,
            { borderColor: colors?.secondary_warm_grey },
          ]}
        >
          <SupportIcon width={RFont(20)} height={RFont(20)} />
        </Pressable>
      }
    >
      <View style={gapStyles.gap_36}>
        <View style={gapStyles.gap_32}>
          <View>
            <Text
              style={[
                fontStyles.Maison_500_20PX_28LH,
                { color: colors?.grey_900 },
              ]}
            >
              Tap to exclude
            </Text>
          </View>
          <MultiSelectPillGroup
            options={
              masterIng?.category
                ?.filter((e) => e.is_live)
                ?.sort((a, b) => a.order_number - b.order_number)
                ?.map((item) => ({
                  label: item.category_name,
                  value: item.category_name,
                })) || []
            }
            selectedValues={
              masterIng?.category?.filter((e) => e.is_live)?.[0]?.category_name
            }
            onSelect={() => null}
          />
          <Text
            style={[
              fontStyles.Maison_500_20PX_28LH,
              { color: colors?.grey_900 },
            ]}
          >
            Search an ingredient to exclude
          </Text>
          <MultiSelectField
            option={masterIng?.ingredient?.map((e) => ({
              label: e.ingredient,
              value: e.ingredient,
            }))}
            onChange={() => null}
            value={[]}
            renderLeftIcon={() => (
              <SearchIcon
                width={RFont(20)}
                height={RFont(20)}
                style={isRTL ? marginStyles.ml_8 : marginStyles.mr_8}
              />
            )}
            placeholder="Search an ingredient to exclude"
          />
          <Text
            style={[
              fontStyles.Maison_500_20PX_28LH,
              { color: colors?.grey_900 },
            ]}
          >
            Ingredients you’re avoiding
          </Text>
          <View
            style={[
              globalStyles.flexDirectionRow,
              globalStyles.flexWrap,
              gapStyles.gap_12,
            ]}
          >
            <SelectableSlotChip
              text="Moringa powder"
              style={{
                backgroundColor: colors?.secondary_warm_grey,
                borderColor: colors?.secondary_warm_grey,
              }}
              rightIcon={<Xclose width={RFont(16)} hitSlop={RFont(16)} />}
            />
            <SelectableSlotChip
              text="Moringa powder"
              style={{
                backgroundColor: colors?.secondary_warm_grey,
                borderColor: colors?.secondary_warm_grey,
              }}
              rightIcon={<Xclose width={RFont(16)} hitSlop={RFont(16)} />}
            />
            <SelectableSlotChip
              text="Moringa powder"
              style={{
                backgroundColor: colors?.secondary_warm_grey,
                borderColor: colors?.secondary_warm_grey,
              }}
              rightIcon={<Xclose width={RFont(16)} hitSlop={RFont(16)} />}
            />
          </View>
        </View>
        <View
          style={[
            paddingStyles.p12,
            borderRadiusStyles.br12,
            gapStyles.gap_12,
            { backgroundColor: colors?.primary_cream },
          ]}
        >
          <View style={[globalStyles.flexDirectionRow, gapStyles.gap_8]}>
            <CalendarCheckIcon width={RFont(16)} height={RFont(16)} />
            <Text
              style={[
                fontStyles.Maison_400_14PX_18LH,
                { color: colors?.grey_700 },
              ]}
            >
              Your changes will apply starting with deliveries from April 23,
              2025.
            </Text>
          </View>
          <View style={[globalStyles.flexDirectionRow, gapStyles.gap_8]}>
            <ReloadIcon width={RFont(16)} height={RFont(16)} />
            <Text
              style={[
                fontStyles.Maison_400_14PX_18LH,
                { color: colors?.grey_700 },
              ]}
            >
              Updating ingredient preferences will reset your current meal
              selection.
            </Text>
          </View>
        </View>
      </View>
    </GorhomBottomSheet>
  );
};

export default IngredientDislikeBottomsheet;
