import React, { useEffect, useState } from 'react';
import { Text, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { AddressAllDayEditProvider } from '../../FormContext/AddressContext';
import { getDeliverySlot } from '../../redux/action/appActions';
import { getAllCity } from '../../redux/slices/appSlice';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontStyles } from '../../theme/fonts';
import { buttonStyles } from '../../theme/styles/buttonStyles';
import { marginStyles } from '../../theme/styles/commonMarginPadding';
import {
  globalStyles,
  heightStyles,
  widthStyles,
} from '../../theme/styles/globalStyles';
import ChangeDeliveryDetails from '../BottomSheet/ChangeDeliveryDetails';
import { PrimaryBtn } from '../Buttons/Btns';
import { CommonCard } from '../Card/Card';
import { SelectableCard } from '../Card/SelectableCard';
import Divider from '../Divider';

const DeliverySlot = () => {
  const dispatch = useDispatch();
  const { subscriptionDetails } = useSelector(
    (state: RootState) => state?.subscription,
  );
  const colors = useTheme();
  const { translation } = useSelector((state: RootState) => state?.auth);
  const [openBottomSheet, setOpenBottomSheet] = useState(false);
  useEffect(() => {
    if (
      subscriptionDetails?.[0]?.address_id?.province &&
      subscriptionDetails?.[0]?.address_id?.city
    )
      dispatch(
        getDeliverySlot({
          city: subscriptionDetails?.[0]?.address_id?.city,
          area: subscriptionDetails?.[0]?.address_id?.province,
        }),
      );
    dispatch(getAllCity());
  }, [dispatch, subscriptionDetails]);

  return (
    <CommonCard
      title={translation?.DELIVERY_DETAILS}
      titleStyle={fontStyles.Maison_600_24PX_32LH}
    >
      <SelectableCard
        customIcon={
          <Text
            onPress={() => {
              setOpenBottomSheet(true);
            }}
            style={[
              fontStyles?.Maison_600_14PX_18LH,
              { color: colors?.primary_grenade },
            ]}
          >
            {translation?.CHANGE}
          </Text>
        }
        cardStyle={[marginStyles?.mt_10, { borderColor: colors?.grey_100 }]}
        title="Office"
        isSelected
        descStyle={fontStyles?.Maison_400_14PX_18LH}
        desc="32, Dusit Thani Dubai, United Arab Emirates"
        renderFooter={
          <View
            style={[
              globalStyles?.flexDirectionColumn,
              globalStyles?.justifyContentCenter,
              widthStyles?.w100,
            ]}
          >
            <Divider
              color={colors?.secondary_warm_grey}
              style={marginStyles?.mtn_10}
            />
            <View
              style={[
                globalStyles?.row,
                globalStyles?.justifyContentFlexStart,
                marginStyles?.mt_10,
              ]}
            >
              <PrimaryBtn
                text={'6PM - 9PM'}
                style={[
                  buttonStyles?.SecondarySelectablePill,
                  heightStyles?.h40,
                ]}
                textStyle={[
                  fontStyles?.Maison_500_14PX_18LH,
                  { color: colors?.neutral_white },
                ]}
              />
            </View>
          </View>
        }
      />
      <AddressAllDayEditProvider>
        <ChangeDeliveryDetails
          sheetOpen={openBottomSheet}
          sheetClose={() => {
            setOpenBottomSheet(false);
          }}
        />
      </AddressAllDayEditProvider>
    </CommonCard>
  );
};

export default DeliverySlot;
