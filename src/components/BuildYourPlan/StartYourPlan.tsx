import moment from 'moment';
import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import { PauseImg } from '../../assets/images/index';
import { RootState } from '../../redux/store';
import { globalStyles } from '../../theme/styles/globalStyles';
import { CommonCard } from '../Card/Card';
import InfoBanner from '../Card/InfoBanner';
import { DateInput } from '../TextInputField/DateInput';

const StartYourPlan = () => {
  const { translation } = useSelector((state: RootState) => state?.auth);
  const [startDate, setStartDate] = useState(moment().format('YYYY-MM-DD'));

  return (
    <CommonCard
      title={translation.START_PLAN_ON}
      titleStyle={[globalStyles?.letterSpacingN1]}
    >
      <DateInput
        value={startDate}
        onChangeText={setStartDate}
        title={translation?.START_PLAN_ON}
      />
      <InfoBanner
        leftIcon={<PauseImg width={20} height={20} />}
        message={translation?.START_PLAN_ON_NOTE}
      />
    </CommonCard>
  );
};

export default StartYourPlan;
