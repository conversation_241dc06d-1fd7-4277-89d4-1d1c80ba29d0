import { MasterRmsDataValueProps } from 'app-actions';
import React, { FC } from 'react';
import { Text } from 'react-native';
import { useSelector } from 'react-redux';
import {
  PortionMacroProps,
  SelectCalorieProps,
} from '../../../@types/meal-plan';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontStyles } from '../../theme/fonts';
import { marginStyles } from '../../theme/styles/commonMarginPadding';
import { globalStyles } from '../../theme/styles/globalStyles';
import { getMacroAverage } from '../../utils/functions';
import { macrosData } from '../../utils/global';
import { CommonCard } from '../Card/Card';
import { SelectableCard } from '../Card/SelectableCard';
import MacroNutrientItem from '../Chips/MacroNutrientItem';

const SelectCalorie: FC<SelectCalorieProps> = ({
  cartFormik,
  cartOnchange,
  totalKcalInMeal,
}) => {
  const colors = useTheme();
  const { translation } = useSelector((state: RootState) => state?.auth);
  const { masterRmsData } = useSelector((state: RootState) => state?.app);
  const { values } = cartFormik;

  const recipe_sizes = masterRmsData?.find(
    (e) => e.key === 'recipe_sizes',
  )?.value;

  const calculateGrams = (number: number, factor: number, content: number) => {
    let temp = (number * (factor / 100)) / content;
    return temp;
  };

  return (
    <CommonCard
      title={translation.SELECT_CALORIE_RANGE_DAILY}
      titleStyle={[globalStyles?.letterSpacingN1]}
    >
      {[...(recipe_sizes || [])]
        .sort(
          (a: { order_number: number }, b: { order_number: number }) =>
            a?.order_number - b?.order_number,
        )
        ?.filter(
          (e: MasterRmsDataValueProps) =>
            e.is_live &&
            (values?.is_vegetarian
              ? e.is_vegetarian === values?.is_vegetarian
              : e),
        )
        ?.map((recipes: MasterRmsDataValueProps) => {
          const recipeData = totalKcalInMeal(recipes?.value as string);
          const portioning =
            recipeData?.portioning as unknown as PortionMacroProps[];

          const carbWidth = getMacroAverage(portioning, 'Carb', '#236B00');
          const fatWidth = getMacroAverage(portioning, 'Fat', '#236B00');
          const proteinWidth = getMacroAverage(
            portioning,
            'Protein',
            '#236B00',
          );
          const isSelected = values?.kcal_range === recipes?.value;
          return (
            <SelectableCard
              key={recipes?.value as string}
              cardStyle={marginStyles.mt_16}
              ingredientsWrapperStyles={marginStyles?.mt_16}
              isSelected={isSelected}
              title={`${totalKcalInMeal(recipes?.value as string)?.totalKcalStart} - ${totalKcalInMeal(recipes?.value as string)?.totalKcalEnd} `}
              titleStyle={{
                color: isSelected ? colors?.primary_spinach : colors?.grey_900,
              }}
              showBadge={
                <Text
                  style={[
                    fontStyles?.Maison_500_14PX_18LH,
                    marginStyles?.mt_4,
                    marginStyles?.ml_4,
                    {
                      color: colors?.grays_black,
                    },
                  ]}
                >
                  Kcal/day
                </Text>
              }
              onPress={() => {
                cartOnchange('kcal_range', recipes.value);
              }}
              renderFooter={macrosData?.map((item) => (
                <MacroNutrientItem
                  macroProgressBarStyle={{ backgroundColor: colors?.AEC323 }}
                  key={item.title}
                  title={
                    translation?.[item?.title as 'PROTEIN' | 'CARB' | 'FAT']
                  }
                  value={
                    item?.title === 'CARB'
                      ? `${calculateGrams(totalKcalInMeal(recipes?.value as string)?.totalKcalStart, carbWidth, 4)?.toFixed(0)} - ${calculateGrams(totalKcalInMeal(recipes?.value as string)?.totalKcalEnd, carbWidth, 4).toFixed(0)}g`
                      : item?.title === 'PROTEIN'
                        ? `${calculateGrams(totalKcalInMeal(recipes?.value as string)?.totalKcalStart, proteinWidth, 4).toFixed(0)} - ${calculateGrams(totalKcalInMeal(recipes?.value as string)?.totalKcalEnd, proteinWidth, 4).toFixed(0)}g`
                        : `${calculateGrams(totalKcalInMeal(recipes?.value as string)?.totalKcalStart, fatWidth, 9).toFixed(0)} - ${calculateGrams(totalKcalInMeal(recipes?.value as string)?.totalKcalEnd, fatWidth, 9)?.toFixed(0)}g`
                  }
                  titleStyle={fontStyles?.Maison_400_14PX_18LH}
                />
              ))}
            />
          );
        })}
    </CommonCard>
  );
};

export default SelectCalorie;
