import React, { useState } from 'react';
import { RFValue } from 'react-native-responsive-fontsize';
import { useSelector } from 'react-redux';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontStyles } from '../../theme/fonts';
import { mealSelectionCardStyles } from '../../theme/styles/cardStyles';
import { marginStyles } from '../../theme/styles/commonMarginPadding';
import { SelectDurationPlanData } from '../../utils/global';
import CommonBadges from '../Badges/Badges';
import { CommonCard } from '../Card/Card';
import { SelectableCard } from '../Card/SelectableCard';
import PriceWithDiscount from '../Text/PriceWithDiscount';

const SelectDurationPlan = () => {
  const colors = useTheme();
  const [selectCalorieRange, setSelectCalorieRange] = useState<string>();
  const { translation } = useSelector((state: RootState) => state?.auth);

  return (
    <CommonCard title={translation.SELECT_PLAN_DURATION}>
      {SelectDurationPlanData?.map((itm, idx) => (
        <SelectableCard
          key={`calorie-range-${itm}-${idx}`}
          cardStyle={marginStyles.mt_16}
          ingredientsWrapperStyles={{ marginTop: RFValue(16) }}
          isSelected={selectCalorieRange === itm?.durationPlan}
          title={itm?.durationPlan}
          badgeStyle={marginStyles?.ml_8}
          showBadge={
            itm?.discount && (
              <CommonBadges
                style={[
                  { backgroundColor: colors?.DAEBB6 },
                  mealSelectionCardStyles?.selectDurationBadge,
                ]}
                textStyle={[
                  fontStyles?.Maison_500_12PX_16LH,
                  { color: colors?.primary_spinach },
                ]}
                badge={itm?.discount}
              />
            )
          }
          customDesc={
            <PriceWithDiscount
              currency="AED"
              originalPrice={itm?.originalPrice}
              finalPrice={String(itm?.finalPrice)}
              durationText={String(itm?.durationText)}
            />
          }
          onPress={() => {
            setSelectCalorieRange(itm?.durationPlan);
          }}
        />
      ))}
    </CommonCard>
  );
};

export default SelectDurationPlan;
