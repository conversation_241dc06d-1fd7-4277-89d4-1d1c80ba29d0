import React from 'react';
import { useSelector } from 'react-redux';
import { AddIcon, EditIcon } from '../../assets/images';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontStyles } from '../../theme/fonts';
import { buttonStyles } from '../../theme/styles/buttonStyles';
import { marginStyles } from '../../theme/styles/commonMarginPadding';
import { widthStyles } from '../../theme/styles/globalStyles';
import { PrimaryBtn } from '../Buttons/Btns';
import { CommonCard } from '../Card/Card';
import InfoBanner from '../Card/InfoBanner';

const SelectIngredients = () => {
  const color = useTheme();
  const { translation } = useSelector((state: RootState) => state?.auth);
  return (
    <CommonCard title={translation.SELECT_INGREDIENTS_YOU_DISLIKE}>
      <PrimaryBtn
        text={translation?.ADD}
        leftIcon={<AddIcon width={20} height={20} />}
        textStyle={[
          fontStyles?.Maison_500_18PX_22LH,
          { color: color?.primary_spinach },
        ]}
        style={[buttonStyles?.BorderBtn, widthStyles?.w88, marginStyles?.mt_10]}
        iconStyle={marginStyles?.mrn_10}
      />

      <InfoBanner
        message={translation?.FINE_TUNE_PLAN_AFTER_ORDER}
        leftIcon={<EditIcon width={15} height={20} />}
      />
    </CommonCard>
  );
};

export default SelectIngredients;
