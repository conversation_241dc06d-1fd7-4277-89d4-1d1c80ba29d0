import React, { FC, useState } from 'react';
import { useSelector } from 'react-redux';
import { SelectDailyMealProps } from '../../../@types/meal-plan';
import { RootState } from '../../redux/store';
import { marginStyles } from '../../theme/styles/commonMarginPadding';
import { mealsList } from '../../utils/global';
import { MultiSelectPillGroup } from '../Buttons/Btns';
import { CommonCard } from '../Card/Card';

const SelectDailyMeal: FC<SelectDailyMealProps> = ({
  cartFormik,
  cartOnchange,
}) => {
  const { translation } = useSelector((state: RootState) => state?.auth);
  const { isRTL } = useSelector((state: RootState) => state.app);
  const { values } = cartFormik;
  const [isError, setError] = useState(
    values.selected_meal.length == 0 || false,
  );
  const toggleMeal = (meal: string) => {
    const value: string[] = [...values.selected_meal];
    const findIndex = value?.findIndex((e) => e === meal);
    setError(false);
    if (findIndex === -1) {
      value.push(meal);
    } else {
      if (
        (meal === 'lunch' || meal === 'dinner') &&
        value.filter((v) => v === 'lunch' || v === 'dinner').length === 1
      ) {
        setError(true);
      }
      value.splice(findIndex, 1);
    }
    cartOnchange('selected_meal', value);
  };
  return (
    <CommonCard title={translation.SELECT_MEALS_DAILY}>
      <MultiSelectPillGroup
        options={mealsList.map((itm) => ({
          label: itm.lable,
          value: itm?.value,
        }))}
        selectedValues={values?.selected_meal}
        onSelect={toggleMeal}
        containerStyle={marginStyles.mt_16}
        isRTL={isRTL}
      />
    </CommonCard>
  );
};

export default SelectDailyMeal;
