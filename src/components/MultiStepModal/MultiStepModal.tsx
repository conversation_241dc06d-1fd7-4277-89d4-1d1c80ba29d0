import { MultiStepModalProps } from 'componentsProps';
import { useFormik } from 'formik';
import moment from 'moment';
import React, { FC, Fragment, useState } from 'react';
import { Pressable, Text, View } from 'react-native';
import Modal from 'react-native-modal';
import { useDispatch, useSelector } from 'react-redux';
import { bodyMetricsProps } from 'recipe-slice';
import * as Yup from 'yup';
import { Xclose } from '../../assets/images';
import { setMacroCalculation } from '../../redux/action/recipesActions';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontStyles, RFont } from '../../theme/fonts';
import {
  gapStyles,
  marginStyles,
  paddingStyles,
} from '../../theme/styles/commonMarginPadding';
import {
  borderRadiusStyles,
  createCloseIconStyle,
  globalStyles,
  heightStyles,
  opacityStyles,
  widthStyles,
} from '../../theme/styles/globalStyles';
import { showErrorToast, showSuccessToast } from '../../utils/functions';
import { rulerData, rulerDataWeight, steps } from '../../utils/global';
import { PrimaryBtn } from '../Buttons/Btns';
import { SelectableRoundChip } from '../Buttons/Chips';
import { CommonCard } from '../Card/Card';
import { VerticalRuler } from '../RulerPicker/RulerPicker';
import { StepperDateInput } from '../TextInputField/DateInput';
import ModalLoader from './ModalLoader';

const stepValidations = [
  Yup.object({
    goal: Yup.string().required('Please select a goal'),
  }),
  Yup.object({
    activity: Yup.string().required('Please select your activity level'),
  }),
  Yup.object({
    gender: Yup.string().required('Please select your gender'),
  }),
  Yup.object({
    height: Yup.number().required('Please enter your height'),
  }),
  Yup.object({ weight: Yup.number().required('Please enter your weight') }),
  Yup.object({ birthdate: Yup.date().required('Please enter your birthdate') }),
];

export const MultiStepModal: FC<MultiStepModalProps> = ({
  onclose,
  open,
  loading,
  setLoading = () => null,
}) => {
  const dispatch = useDispatch();
  const [activeUnit, setActiveUnit] = useState('cm');
  const [loaderText, setLoaderText] = useState(
    'Setting up the questions to find your perfect diet',
  );
  const [activeUnitWeight, setActiveUnitWeight] = useState('kg');
  const [currentStep, setCurrentStep] = useState(0);
  const [answers, setAnswers] = useState<Array<Record<string, string>>>([]);
  const step = steps[currentStep];
  const validationSchema = stepValidations[currentStep];
  const { isRTL } = useSelector((state: RootState) => state.app);
  type StepField =
    | 'goal'
    | 'activity'
    | 'gender'
    | 'weight'
    | 'height'
    | 'birthdate';
  const fieldName = step?.name as StepField;
  const today = new Date();
  const hundredYearsAgo = new Date(
    today.getFullYear() - 100,
    today.getMonth(),
    today.getDate(),
  );

  const handleOnPress = (val: string) => {
    setActiveUnit(val);
  };

  const handleWeight = (val: string) => {
    setActiveUnitWeight(val);
  };
  const initialValues: bodyMetricsProps = {
    goal: '',
    activity: '',
    gender: '',
    weight: '',
    height: '',
    birthdate: '',
  };
  const bodyMetricsFormik = useFormik<bodyMetricsProps>({
    initialValues: initialValues,
    enableReinitialize: true,
    validationSchema: validationSchema,
    onSubmit(values, actions) {
      const updatedAnswers = [...answers];
      updatedAnswers[currentStep] = { [step?.name]: values[fieldName] };
      setAnswers(updatedAnswers);

      if (currentStep < steps.length - 1) {
        setCurrentStep(currentStep + 1);
      } else {
        setLoading(true);
        setLoaderText('Setting up your perfect diet');
        const obj = {
          body_metrics: {
            weight: Number(values?.weight),
            weight_unit: activeUnitWeight,
            height: Number(values?.height),
            height_unit: activeUnit === 'ft-in' ? 'ft' : activeUnit,
            activity_level: values.activity,
            goal: values.goal,
            gender: values.gender,
            birth_date: values.birthdate,
          },
        };
        console.log('obj >>>', obj);

        dispatch(
          setMacroCalculation(
            obj,
            (res: { status: boolean; message: string }) => {
              actions.setSubmitting(false);
              setLoading(false);
              setLoaderText(
                'Setting up the questions to find your perfect diet',
              );
              if (res?.status) {
                console.log('res >>>', res);

                // dispatch(storeBodyMetrics({ ...obj, ...res?.data }));
                showSuccessToast(res?.message);
                setCurrentStep(0);
                onclose();
              } else {
                showErrorToast(res?.message);
              }
            },
          ),
        );
      }
    },
  });
  const { setFieldValue, handleSubmit, errors, touched, values } =
    bodyMetricsFormik;
  const colors = useTheme();
  const IconStyles = createCloseIconStyle(colors);
  return (
    <Modal
      onBackdropPress={() => {
        onclose();
      }}
      onBackButtonPress={() => {
        onclose();
      }}
      isVisible={open}
    >
      {loading ? (
        <ModalLoader message={loaderText} />
      ) : (
        <Fragment>
          <CommonCard
            title={step?.title}
            titleStyle={[
              fontStyles.Maison_600_32PX_40LH,
              { color: colors?.grey_900 },
            ]}
            style={[paddingStyles.px20, paddingStyles.py32, heightStyles.mh580]}
          >
            <Pressable style={IconStyles?.icon} onPress={onclose}>
              <Xclose width={RFont(16)} height={RFont(16)} />
            </Pressable>
            {step?.name === 'birthdate' ? (
              <StepperDateInput
                onChangeText={(date) =>
                  setFieldValue(
                    'birthdate',
                    moment(date).format('YYYY-MM-DD'),
                    true,
                  )
                }
                value={values.birthdate}
                minimumDate={hundredYearsAgo}
                maximumDate={new Date()}
              />
            ) : step?.name === 'height' ? (
              <VerticalRuler
                title=""
                unit={activeUnit as 'cm' | 'kg' | 'lbs' | 'ft-in'}
                min={activeUnit === 'ft-in' ? 0 : 0}
                max={activeUnit === 'ft-in' ? 120 : 300}
                activeUnit={activeUnit}
                unitData={rulerData}
                onPress={handleOnPress}
                onChange={(v) => setFieldValue('height', v, true)}
              />
            ) : step?.name === 'weight' ? (
              <VerticalRuler
                title=""
                unit={activeUnitWeight as 'cm' | 'kg' | 'lbs' | 'ft-in'}
                min={0}
                max={300}
                activeUnit={activeUnitWeight}
                unitData={rulerDataWeight}
                onPress={handleWeight}
                onChange={(v) => setFieldValue('weight', v, true)}
              />
            ) : (
              <View style={marginStyles.mt_auto}>
                {step?.options && (
                  <View
                    style={[
                      isRTL ? globalStyles?.rowReverse : globalStyles?.row,
                      globalStyles?.flexWrap,
                      gapStyles.gap_8,
                    ]}
                  >
                    {step?.options?.map((opt, i) => {
                      const isSelected = values[fieldName] === opt?.value;
                      return (
                        <SelectableRoundChip
                          key={i}
                          text={opt?.label}
                          isSelected={isSelected}
                          onPress={() =>
                            setFieldValue(step?.name, opt?.value, true)
                          }
                        />
                      );
                    })}
                  </View>
                )}
              </View>
            )}
            {touched[fieldName] && errors[fieldName] && (
              <Text
                style={[
                  marginStyles.mt_16,
                  fontStyles.Maison_500_14PX_18LH,
                  { color: colors?.red_500 },
                ]}
              >
                {errors[fieldName]}
              </Text>
            )}
          </CommonCard>
          <PrimaryBtn
            onPress={handleSubmit}
            textStyle={fontStyles?.Maison_500_16PX_20}
            text="Next"
            style={marginStyles?.mt_24}
          />
          <View
            style={[
              globalStyles.row,
              globalStyles.justifyContentCenter,
              marginStyles.mt_24,
              gapStyles.gap_6,
            ]}
          >
            {steps?.map((_, i) => (
              <View
                key={i}
                style={[
                  borderRadiusStyles.br666,
                  widthStyles.w8,
                  heightStyles.h8,
                  {
                    opacity:
                      currentStep === i
                        ? opacityStyles.opacity_0_67.opacity
                        : opacityStyles.opacity_0_2.opacity,
                    backgroundColor:
                      currentStep === i
                        ? colors?.neutral_white
                        : colors?.primary_cream,
                  },
                ]}
              />
            ))}
          </View>
        </Fragment>
      )}
    </Modal>
  );
};
