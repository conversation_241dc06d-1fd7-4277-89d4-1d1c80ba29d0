import { ModalLoaderProps } from 'componentsProps';
import React, { FC, useEffect, useRef } from 'react';
import { Animated, Easing, Text, View } from 'react-native';
import { useSelector } from 'react-redux';
import { Loader, SuccessGreen } from '../../assets/images';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontStyles } from '../../theme/fonts';
import {
  gapStyles,
  marginStyles,
} from '../../theme/styles/commonMarginPadding';
import {
  borderRadiusStyles,
  globalStyles,
  heightStyles,
} from '../../theme/styles/globalStyles';
const ModalLoader: FC<ModalLoaderProps> = ({ message }) => {
  const { isRTL } = useSelector((state: RootState) => state?.app);
  const spinAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.loop(
      Animated.timing(spinAnim, {
        toValue: 1,
        duration: 2000,
        easing: Easing.linear,
        useNativeDriver: true,
      }),
    ).start();
  }, [spinAnim]);

  const spin = spinAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });
  const colors = useTheme();
  return (
    <View
      style={[
        { backgroundColor: colors?.neutral_white },
        globalStyles.alignItemsCenter,
        globalStyles.justifyContentCenter,
        heightStyles.h700,
        borderRadiusStyles.br20,
      ]}
    >
      <Animated.View style={{ transform: [{ rotate: spin }] }}>
        <Loader width={48} height={48} />
      </Animated.View>
      <Text
        style={[
          globalStyles.textAlignCenter,
          marginStyles.mt_20,
          fontStyles.Maison_600_24PX_30LH,
          {
            color: colors?.grey_900,
          },
        ]}
      >
        {message}
      </Text>
      <View
        style={[
          marginStyles.mt_32,
          gapStyles.gap_8,
          isRTL ? globalStyles.rowReverse : globalStyles.row,
        ]}
      >
        <SuccessGreen />
        <Text
          style={[
            fontStyles.Maison_600_15PX_18LH,
            {
              color: colors?.primary_spinach,
            },
          ]}
        >
          Nutrionist approved
        </Text>
      </View>
    </View>
  );
};

export default ModalLoader;
