import { editDeliveryAddressPayload } from 'actions';
import { AddressSelectBottomSheetProps } from 'componentsProps';
import React, { FC } from 'react';
import { useFormContext } from 'react-hook-form';
import { Text, View } from 'react-native';
import { RFValue } from 'react-native-responsive-fontsize';
import { useSelector } from 'react-redux';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontStyles } from '../../theme/fonts';
import {
  gapStyles,
  marginStyles,
  paddingStyles,
} from '../../theme/styles/commonMarginPadding';
import { globalStyles } from '../../theme/styles/globalStyles';
import { PrimaryBtn } from '../Buttons/Btns';
import { SelectableCard } from '../Card/SelectableCard';
import { GorhomBottomSheet } from './BottomSheet';
const SpecialInstructionsSheet: FC<AddressSelectBottomSheetProps> = ({
  sheetOpen,
  sheetClose,
}) => {
  const { masterData } = useSelector((state: RootState) => state?.app);
  const { setValue, watch } = useFormContext<editDeliveryAddressPayload>();
  const colors = useTheme();

  return (
    <GorhomBottomSheet
      modalBackgroundColor="white"
      sheetOpen={sheetOpen}
      title="Add special instructions"
      sheetClose={sheetClose}
      footer={
        <View style={[paddingStyles.px16, paddingStyles.pb12]}>
          <PrimaryBtn
            onPress={sheetClose}
            textStyle={fontStyles?.Maison_600_16PX_20LH}
            text="Save instructions"
            style={marginStyles?.mt_10}
          />
        </View>
      }
    >
      <Text
        style={[
          globalStyles.flex1,
          fontStyles.Maison_500_18PX_24LH,
          { color: colors?.black_900 },
        ]}
      >
        Choose all that apply
      </Text>
      <View style={[gapStyles.gap_12, marginStyles.mt_20]}>
        {masterData?.instruction?.map((itm) => (
          <SelectableCard
            key={itm?.name}
            customIcon
            leftIcon
            isSelected={
              watch('instruction')?.findIndex((e) => e === itm?.id) !== -1
                ? true
                : false
            }
            title={itm?.name}
            cardStyle={[
              paddingStyles.py20,
              paddingStyles.px16,
              {
                borderWidth:
                  watch('instruction')?.findIndex((e) => e === itm?.id) !== -1
                    ? RFValue(2)
                    : RFValue(1),
              },
            ]}
            titleStyle={[
              fontStyles.Maison_500_18PX_24LH,
              marginStyles?.ml_16,
              { color: colors?.black },
            ]}
            onPress={() => {
              const instructions = watch('instruction');
              const instructionsUpdate = Object.assign(
                [],
                instructions,
              ) as Array<string>;
              const findIndex = instructionsUpdate.findIndex(
                (e: string) => e == itm?.id,
              );
              if (findIndex == -1) {
                instructionsUpdate.push(itm?.id);
              } else {
                instructionsUpdate.splice(findIndex, 1);
              }
              setValue('instruction', instructionsUpdate);
            }}
          />
        ))}
      </View>
    </GorhomBottomSheet>
  );
};

export default SpecialInstructionsSheet;
