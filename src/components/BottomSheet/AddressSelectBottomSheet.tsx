import { AddressSelectBottomSheetProps } from 'componentsProps';
import React, { FC, useEffect, useState } from 'react';
import { View } from 'react-native';
import { useTheme } from '../../theme';
import { fontStyles } from '../../theme/fonts';
import {
  marginStyles,
  paddingStyles,
} from '../../theme/styles/commonMarginPadding';
import { PrimaryBtn } from '../Buttons/Btns';
import { SelectableCard } from '../Card/SelectableCard';
import Divider from '../Divider';
import { SearchBar } from '../TextInputField/TextInputField';
import { GorhomBottomSheet } from './BottomSheet';
const AddressSelectBottomSheet: FC<AddressSelectBottomSheetProps> = ({
  option = [],
  value = undefined,
  onChange = () => null,
  sheetOpen,
  title = '',
  sheetClose,
}) => {
  const [searchText, setSearchText] = useState('');
  const handleSearch = (text: string) => {
    setSearchText(text);
  };
  const [selectAddress, setSelectAddress] = useState<string>('');
  const colors = useTheme();
  useEffect(() => {
    if (value) {
      setSelectAddress(value);
    }
  }, [value]);

  return (
    <GorhomBottomSheet
      modalBackgroundColor="white"
      sheetOpen={sheetOpen}
      title={title}
      sheetClose={sheetClose}
      footer={
        <View style={[paddingStyles.px16, paddingStyles.pb12]}>
          <PrimaryBtn
            onPress={sheetClose}
            textStyle={fontStyles?.Maison_600_16PX_20LH}
            text="Continue"
            style={marginStyles?.mt_10}
          />
        </View>
      }
    >
      <SearchBar
        placeholder="Select"
        value={searchText}
        onChangeText={handleSearch}
      />
      <View style={marginStyles.mt_24}>
        {option
          ?.filter((option) =>
            option?.label?.toLowerCase().includes(searchText?.toLowerCase()),
          )
          ?.map((itm, i) => (
            <View key={`delivery-details-options${i}`}>
              <SelectableCard
                customIcon
                leftIcon
                isSelected={selectAddress === itm?.value}
                title={itm?.label}
                cardStyle={[
                  paddingStyles.py20,
                  paddingStyles.px16,
                  {
                    borderColor:
                      selectAddress !== itm?.value
                        ? colors?.transparent
                        : colors?.primary_grenade,
                  },
                ]}
                titleStyle={[
                  fontStyles.Maison_500_18PX_24LH,
                  marginStyles?.ml_16,
                  { color: colors?.black },
                ]}
                onPress={() => {
                  onChange(itm?.value as string);
                  setSelectAddress(itm?.value as string);
                }}
              />
              {selectAddress === itm?.value ? null : (
                <Divider color={colors?.secondary_warm_grey} />
              )}
            </View>
          ))}
      </View>
    </GorhomBottomSheet>
  );
};

export default AddressSelectBottomSheet;
