import { editDeliveryAddressPayload } from 'actions';
import React, { FC, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { Text, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { PlusOrangeIcon } from '../../assets/images';
import { useAppNavigation } from '../../hooks/useAppNavigation';
import { getDeliverySlot } from '../../redux/action/appActions';
import { getUserAddressById } from '../../redux/action/authAction';
import {
  editDeliveryAddress,
  getWeekWiseDelivery,
} from '../../redux/action/deliveryActions';
import { getSubscriptionDetails } from '../../redux/slices/subscriptionSlices';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { lightTheme } from '../../theme/colors';
import { fontStyles, RFont } from '../../theme/fonts';
import { buttonStyles } from '../../theme/styles/buttonStyles';
import {
  gapStyles,
  marginStyles,
  paddingStyles,
} from '../../theme/styles/commonMarginPadding';
import {
  borderRadiusStyles,
  globalStyles,
  widthStyles,
} from '../../theme/styles/globalStyles';
import { MultiSelectPillGroup, PrimaryBtn } from '../Buttons/Btns';
import { SelectableCard } from '../Card/SelectableCard';
import Divider from '../Divider';
import { GorhomBottomSheet } from './BottomSheet';

interface ChangeDeliveryDetailsProps {
  sheetOpen: boolean;
  sheetClose?: () => void;
}
const ChangeDeliveryDetails: FC<ChangeDeliveryDetailsProps> = ({
  sheetOpen,
  sheetClose = () => null,
}) => {
  const [selectedSlot, setSelectedSlot] = useState<string>();
  const dispatch = useDispatch();
  const navigation = useAppNavigation();
  const colors = useTheme();
  const { deliverySlots } = useSelector((state: RootState) => state?.app);
  const { loader } = useSelector((state: RootState) => state?.delivery);
  const { translation, authUser } = useSelector(
    (state: RootState) => state?.auth,
  );
  const { handleSubmit } = useFormContext<editDeliveryAddressPayload>();
  const onSubmit = (data: editDeliveryAddressPayload) => {
    if (data?.type === 'all')
      dispatch(
        editDeliveryAddress(
          {
            address_id: data?.address_id,
            customer_id: data?.customer_id,
            slot: data?.slot,
            subscription_id: data?.subscription_id,
            type: data?.type,
          },
          (res: { status: boolean }) => {
            if (res.status) {
              dispatch(getSubscriptionDetails());
              if (authUser?._id)
                dispatch(
                  getWeekWiseDelivery({
                    customer_id: authUser?._id,
                    week: 'current_week',
                  }),
                );
              sheetClose();
            }
          },
        ),
      );
  };
  const { setValue, watch } = useFormContext<editDeliveryAddressPayload>();
  return (
    <GorhomBottomSheet
      footer={
        <View style={[paddingStyles.px16, paddingStyles.pb12]}>
          <PrimaryBtn
            onPress={() => navigation.navigate('AddNewAddress', {})}
            leftIcon={<PlusOrangeIcon />}
            textStyle={[
              fontStyles?.Maison_600_14PX_18LH,
              { color: lightTheme?.primary_grenade },
            ]}
            text={translation?.ADD_NEW_ADDRESS}
            style={[buttonStyles?.BorderLessBtn, marginStyles?.mt_20]}
          />
          <PrimaryBtn
            isLoading={loader}
            onPress={handleSubmit(onSubmit)}
            textStyle={fontStyles?.Maison_600_16PX_20LH}
            text="Confirm delivery details"
            style={marginStyles?.mt_10}
          />
        </View>
      }
      modalBackgroundColor="white"
      sheetOpen={sheetOpen}
      desc="for Monday, 5 May"
      title={translation?.CHANGE_DELIVERY_DETAILS}
      sheetClose={sheetClose}
    >
      {authUser?.address_data?.map(
        (adItem: {
          _id: string;
          address_type: string | undefined;
          full_address: string;
          province: string;
          city: string;
        }) => {
          return (
            <SelectableCard
              isSelected={watch('address_id') === adItem._id}
              leftIcon
              key={adItem?._id}
              title={adItem?.address_type}
              customIcon={
                <Text
                  onPress={() => {
                    dispatch(getUserAddressById(adItem._id));
                    navigation.navigate('AddNewAddress', {});
                  }}
                  style={[
                    fontStyles?.Maison_600_14PX_18LH,
                    { color: colors?.primary_grenade },
                  ]}
                >
                  {translation?.EDIT}
                </Text>
              }
              desc={
                (adItem?.full_address ? adItem?.full_address + ', ' : '') +
                (adItem?.province ? adItem?.province + ', ' : '') +
                adItem?.city
              }
              descStyle={[
                fontStyles?.Maison_400_14PX_18LH,
                marginStyles?.ml_30,
              ]}
              numberOfLinesDesc={1}
              cardStyle={[marginStyles?.mt_16]}
              titleStyle={[
                marginStyles?.ml_8,
                { fontSize: RFont(18), lineHeight: RFont(24) },
              ]}
              renderFooter={
                <View
                  style={[
                    globalStyles.flex1,
                    globalStyles?.flexDirectionColumn,
                    globalStyles?.justifyContentCenter,
                    widthStyles?.w100,
                    marginStyles?.ml_30,
                  ]}
                >
                  <Divider color={colors?.secondary_warm_grey} />
                  <View style={[gapStyles.gap_12, marginStyles.mt_12]}>
                    <Text
                      style={[
                        fontStyles?.Maison_500_14PX_18LH,
                        { color: colors?.grey_800 },
                      ]}
                    >
                      Choose delivery slot:
                    </Text>
                    <MultiSelectPillGroup
                      buttonStyle={[
                        borderRadiusStyles.br12,
                        paddingStyles.px14,
                        paddingStyles.py8,
                      ]}
                      options={deliverySlots?.map((itm) => ({
                        label: itm?.timing,
                      }))}
                      selectedValues={selectedSlot}
                      onSelect={(val) => {
                        setValue('slot', val);
                        if (
                          val === 'Before 7:30AM' ||
                          val === '3AM - 7:30AM' ||
                          val === '3AM - 6AM'
                        ) {
                          setValue('instruction', ['1001', '1002', '1003']);
                        } else {
                          setValue('instruction', []);
                        }
                        setSelectedSlot(val);
                      }}
                      textStyle={fontStyles.Maison_500_14PX_16_8LH}
                    />
                  </View>
                </View>
              }
              onPress={() => {
                dispatch(
                  getDeliverySlot({
                    city: adItem?.city,
                    area: adItem?.province,
                  }),
                );
                setValue('slot', '');
                setValue('address_id', adItem?._id);
              }}
            />
          );
        },
      )}
    </GorhomBottomSheet>
  );
};

export default ChangeDeliveryDetails;
