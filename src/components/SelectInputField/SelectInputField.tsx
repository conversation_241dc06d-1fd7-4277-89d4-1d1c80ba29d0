import { DropDownprops } from 'componentsProps';
import React, { useState } from 'react';
import { Text, View } from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';
import { RFValue } from 'react-native-responsive-fontsize';
import { useSelector } from 'react-redux';
import { ChevronRightIcon } from '../../assets/images';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontStyles } from '../../theme/fonts';
import { gapStyles } from '../../theme/styles/commonMarginPadding';
import { globalStyles } from '../../theme/styles/globalStyles';
import { formInputStyles } from '../../theme/styles/inputsStyles';

const SelectInputField: React.FC<DropDownprops> = ({
  labelField = 'label',
  header,
  valueField = 'value',
  placeholder = 'Select',
  option = [],
  headerStyle,
  value = undefined,
  onChange,
  disable,
  error,
  style,
  containerStyle,
  touched,
}) => {
  const { isRTL } = useSelector((state: RootState) => state.app);
  const [isFocus, setIsFocus] = useState(false);
  const color = useTheme();
  return (
    <View style={[gapStyles?.gap_8, containerStyle]}>
      {header && (
        <Text
          style={[
            isRTL ? globalStyles.textRight : globalStyles.textLeft,
            fontStyles.Maison_500_16PX_18LH,
            { color: color.neutral_black },
            headerStyle,
          ]}
        >
          {header}
        </Text>
      )}
      <Dropdown
        mode="modal"
        disable={disable}
        style={[
          formInputStyles.dropdown,
          style,
          {
            backgroundColor: color.neutral_white,
            borderColor:
              error && touched
                ? color.red_500
                : isFocus
                  ? color.grey_900
                  : color.grey_100,
          },
        ]}
        placeholderStyle={[
          isRTL ? globalStyles.textRight : globalStyles.textLeft,
          fontStyles.Maison_500_16PX_18LH,
          { color: color.grey_600 },
        ]}
        selectedTextStyle={[
          isRTL ? globalStyles.textRight : globalStyles.textLeft,
          fontStyles.Maison_500_16PX_18LH,
          { color: color.grey_900 },
        ]}
        itemTextStyle={[
          isRTL ? globalStyles.textRight : globalStyles.textLeft,
          fontStyles.Maison_500_14PX_18LH,
          { color: color.grey_900 },
        ]}
        searchPlaceholderTextColor={color.grey_900}
        inputSearchStyle={[{ color: color.grey_900 }]}
        data={option}
        maxHeight={300}
        labelField={labelField}
        valueField={valueField}
        placeholder={placeholder}
        searchPlaceholder="Search..."
        search={true}
        value={value}
        onFocus={() => setIsFocus(true)}
        onBlur={() => setIsFocus(false)}
        onChange={(item) => {
          onChange(item?.[valueField]);
          setIsFocus(false);
        }}
        renderLeftIcon={() =>
          isRTL ? (
            <ChevronRightIcon
              height={RFValue(16)}
              width={RFValue(16)}
              style={{ transform: [{ rotate: !isFocus ? '90deg' : '-90deg' }] }}
            />
          ) : null
        }
        renderRightIcon={() =>
          !isRTL ? (
            <ChevronRightIcon
              height={RFValue(16)}
              width={RFValue(16)}
              style={{ transform: [{ rotate: !isFocus ? '90deg' : '-90deg' }] }}
            />
          ) : null
        }
        iconStyle={[
          formInputStyles.dropdownIcon,
          {
            transform: [{ rotate: !isFocus ? '0deg' : '180deg' }],
          },
        ]}
        activeColor={color.secondary_warm_grey}
      />
      {error && touched && (
        <Text
          style={[fontStyles.Maison_500_14PX_18LH, { color: color.red_500 }]}
        >
          {error}
        </Text>
      )}
    </View>
  );
};

export default SelectInputField;
