import { FC } from 'react';
import { Pressable, Text } from 'react-native';
import { useTheme } from '../../theme';
import { fontStyles } from '../../theme/fonts';
import { mealSelectionCardStyles } from '../../theme/styles/cardStyles';
import {
  gapStyles,
  paddingStyles,
} from '../../theme/styles/commonMarginPadding';
import {
  borderRadiusStyles,
  borderWidthStyles,
  globalStyles,
} from '../../theme/styles/globalStyles';
import CommonBadges from '../Badges/Badges';
import { Checkbox } from '../Buttons/Checkbox';

interface OptionCardProps {
  onPress?: (() => void) | null;
  isSelected?: boolean;
  title: string;
  badge?: string;
}

const OptionCard: FC<OptionCardProps> = ({
  onPress,
  isSelected,
  title,
  badge,
}) => {
  const colors = useTheme();
  return (
    <Pressable
      onPress={onPress}
      style={[
        borderRadiusStyles.br16,
        globalStyles.flexDirectionRow,
        globalStyles.alignItemsCenter,
        gapStyles.gap_16,
        borderWidthStyles.bw1,
        paddingStyles.px16,
        paddingStyles.py20,
        {
          borderColor: isSelected
            ? colors?.primary_grenade
            : colors?.neutral_white,
        },
      ]}
    >
      <Checkbox value={isSelected} />
      <Text style={[fontStyles.Maison_500_18PX_24LH, { color: colors?.black }]}>
        {title}
      </Text>
      {badge && (
        <CommonBadges
          style={[
            { backgroundColor: colors?.DAEBB6 },
            mealSelectionCardStyles?.selectDurationBadge,
          ]}
          textStyle={[{ color: colors?.primary_spinach }]}
          badge={badge}
        />
      )}
    </Pressable>
  );
};
export default OptionCard;
