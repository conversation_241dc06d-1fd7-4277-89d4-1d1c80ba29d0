import { CommonCardProps, CustomCardProps } from 'card-props';
import React, { FC } from 'react';
import { Image, Text, TouchableOpacity, View } from 'react-native';
import { RFValue } from 'react-native-responsive-fontsize';
import { useSelector } from 'react-redux';
import { Xclose } from '../../assets/images';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontStyles } from '../../theme/fonts';
import { cardStyles } from '../../theme/styles/cardStyles';
import {
  gapStyles,
  leftStyles,
  rightStyles,
  topStyles,
} from '../../theme/styles/commonMarginPadding';
import {
  borderRadiusStyles,
  globalStyles,
} from '../../theme/styles/globalStyles';

export const CommonCard: FC<CommonCardProps> = ({
  style,
  title,
  titleStyle,
  children,
}) => {
  const colors = useTheme();
  return (
    <View style={[cardStyles.cardR20_P24_BoxShadow1, style]}>
      {title && (
        <Text
          style={[
            fontStyles.Maison_600_24PX_30LH,
            { color: colors?.grey_900 },
            titleStyle,
          ]}
        >
          {title}
        </Text>
      )}
      {children}
    </View>
  );
};

/*
ModalCard
It has a close button (X) which is typical for modals
It has a title section with optional left icon (back button)
It has a structured layout with header and content areas
*/

/*
future update shift the left icon entire thing  to the parent
*/
export const ModalCard: FC<CustomCardProps> = ({
  style,
  children,
  showCloseIcon = false,
  leftIconClick,
  title,
  titleStyle,
  onClose,
  ShowTitleLeftIcon,
}) => {
  const { isRTL } = useSelector((state: RootState) => state?.app);
  return (
    <View
      style={[
        cardStyles.cardR20_PT32_PB20_PX16,
        globalStyles.relative,
        globalStyles.flex1,
        ...(style || []),
      ]}
    >
      {(ShowTitleLeftIcon || title) && (
        <View
          style={[
            globalStyles.row,
            globalStyles.justifyContentFlexStart,
            globalStyles.alignItemsCenter,
            gapStyles.gap_12,
          ]}
        >
          {ShowTitleLeftIcon &&
            (typeof ShowTitleLeftIcon === 'function' ? (
              <ShowTitleLeftIcon
                width={RFValue(24)}
                height={RFValue(24)}
                onPress={leftIconClick}
              />
            ) : (
              <TouchableOpacity onPress={leftIconClick}>
                <Image
                  source={
                    typeof ShowTitleLeftIcon === 'string'
                      ? { uri: ShowTitleLeftIcon }
                      : ShowTitleLeftIcon
                  }
                  style={{ width: RFValue(24), height: RFValue(24) }}
                />
              </TouchableOpacity>
            ))}
          {title && (
            <Text
              style={[
                fontStyles.Maison_600_24PX_30LH,
                ...(titleStyle || []),
                isRTL ? globalStyles.textRight : globalStyles.textLeft,
              ]}
            >
              {title}
            </Text>
          )}
        </View>
      )}

      {showCloseIcon && (
        <TouchableOpacity
          onPress={onClose}
          style={[
            globalStyles.absolute,
            isRTL ? leftStyles.l_12 : rightStyles.r_12,
            topStyles.t_12,
            borderRadiusStyles.br666,
          ]}
        >
          <Xclose width={RFValue(16)} height={RFValue(16)} />
        </TouchableOpacity>
      )}
      {children}
    </View>
  );
};
