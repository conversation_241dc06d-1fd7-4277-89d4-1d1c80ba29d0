import { MealSelectionCardProps } from 'card-props';
import React from 'react';
import { Pressable, Text, View } from 'react-native';
import { RFValue } from 'react-native-responsive-fontsize';
import { useSelector } from 'react-redux';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontStyles } from '../../theme/fonts';
import { cardStyles, macrosCardStyles } from '../../theme/styles/cardStyles';
import { marginStyles } from '../../theme/styles/commonMarginPadding';
import { globalStyles } from '../../theme/styles/globalStyles';
import { Checkbox } from '../Buttons/Checkbox';

export const SelectableCard: React.FC<MealSelectionCardProps> = ({
  title = '',
  desc = '',
  ingredientsWrapperStyles,
  cardStyle,
  showBadge,
  isSelected = false,
  onPress,
  badgeStyle,
  renderFooter,
  titleStyle,
  descStyle,
  customDesc,
  customIcon,
  leftIcon,
  numberOfLinesDesc,
}) => {
  const { isRTL } = useSelector((state: RootState) => state?.app);
  const colors = useTheme();
  return (
    <Pressable
      onPress={onPress}
      style={[
        cardStyles.cardR16_P16_BoxShadow1,
        globalStyles.border2,
        {
          backgroundColor: colors?.neutral_white,
          borderColor: isSelected ? colors?.primary_grenade : colors?.grey_100,
        },
        cardStyle,
      ]}
    >
      <View
        style={[
          isRTL ? globalStyles.rowReverse : globalStyles.row,
          globalStyles.alignItemsCenter,
          globalStyles.justifyContentSpaceBetween,
        ]}
      >
        <View style={[globalStyles.row, globalStyles.alignItemsCenter]}>
          {leftIcon && <Checkbox value={isSelected} />}
          <Text
            style={[
              isRTL ? globalStyles.textRight : globalStyles.textLeft,
              fontStyles.Maison_600_20PX_26LH,
              {
                color: isSelected ? colors?.primary_spinach : colors?.black_900,
              },
              titleStyle,
            ]}
          >
            {title}
          </Text>
          {showBadge && <View style={badgeStyle}>{showBadge}</View>}
        </View>
        {customIcon ? (
          <View>{customIcon}</View>
        ) : (
          <Checkbox value={isSelected} />
        )}
      </View>

      {customDesc ? (
        <View style={{ marginTop: RFValue(8) }}>{customDesc}</View>
      ) : desc ? (
        <Text
          numberOfLines={numberOfLinesDesc}
          style={[
            isRTL ? globalStyles.textRight : globalStyles.textLeft,
            fontStyles.Maison_400_16PX_20LH,
            marginStyles?.mt_8,
            { color: colors?.grey_600 },
            descStyle,
          ]}
        >
          {desc}
        </Text>
      ) : null}

      {isSelected && renderFooter && (
        <View
          style={[
            isRTL ? globalStyles.rowReverse : globalStyles.row,
            macrosCardStyles.macrosViewWrapper,
            isRTL ? globalStyles.rowReverse : globalStyles.row,
            ingredientsWrapperStyles,
            { marginTop: RFValue(15) },
          ]}
        >
          {renderFooter}
        </View>
      )}
    </Pressable>
  );
};
