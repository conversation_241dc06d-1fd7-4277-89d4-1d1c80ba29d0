import { InfoCardProps } from 'componentsProps';
import React, { FC } from 'react';
import { Text, View } from 'react-native';
import { useSelector } from 'react-redux';
import { InfoIcon } from '../../assets/images';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontStyles } from '../../theme/fonts';
import { infoCardStyles } from '../../theme/styles/cardStyles';
import { globalStyles } from '../../theme/styles/globalStyles';
const InfoCard: FC<InfoCardProps> = ({ cardStyle, textStyle, message }) => {
  const { isRTL } = useSelector((state: RootState) => state?.app);
  const colors = useTheme();
  return (
    <View
      style={[
        infoCardStyles.card,
        cardStyle,
        isRTL ? globalStyles.rowReverse : globalStyles.row,
      ]}
    >
      <InfoIcon />
      <Text
        style={[
          globalStyles.flex1,
          fontStyles.Maison_400_14PX_18LH,
          { color: colors?.green_477C7C },
          textStyle,
        ]}
      >
        {message}
      </Text>
    </View>
  );
};

export default InfoCard;
