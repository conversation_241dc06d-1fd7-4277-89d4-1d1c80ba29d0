import { DeliveryItemProps } from 'delivery-slice';
import React, { FC, useState } from 'react';
import {
  Image,
  Pressable,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import StarRating, { StarRatingDisplay } from 'react-native-star-rating-widget';
import { useDispatch, useSelector } from 'react-redux';
import { BuildPlanImg, ConfirmIcon } from '../../assets/images';
import {
  getDeliveryByDate,
  setMealLog,
} from '../../redux/action/deliveryActions';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontStyles, RFont } from '../../theme/fonts';
import { buttonStyles } from '../../theme/styles/buttonStyles';
import {
  gapStyles,
  paddingStyles,
} from '../../theme/styles/commonMarginPadding';
import {
  borderRadiusStyles,
  globalStyles,
} from '../../theme/styles/globalStyles';
import { showErrorToast } from '../../utils/functions';
import MealItemBottomSheet from '../Account/MealItemBottomSheet';
import MealRatingBottomSheet from '../Account/MealRatingBottomSheet';
import { PrimaryBtn } from '../Buttons/Btns';
import SkeletonLoader from '../SkeletonLoader';

interface AccountMealItemcardProps {
  mealType: string;
  mealName: string;
  mealVarient?: string;
  mealKcal: string;
  mealStaus?: 'NotDelivered' | 'Delivered' | 'Taken';
  rating?: number;
  meal_item: DeliveryItemProps;
}

const AccountMealItemcard: FC<AccountMealItemcardProps> = ({
  mealType,
  mealName,
  mealVarient,
  mealKcal,
  mealStaus = 'NotDelivered',
  rating,
  meal_item,
}) => {
  const [isOpen, setOpen] = useState(false);
  const [isRating, setRating] = useState(false);
  const colors = useTheme();
  const { deliverybyDate, selectedDate } = useSelector(
    (state: RootState) => state.delivery,
  );
  const { authUser } = useSelector((state: RootState) => state.auth);
  const dispatch = useDispatch();

  return (
    <React.Fragment>
      <Pressable
        onPress={() => {
          setOpen(true);
        }}
        style={[globalStyles.flexDirectionRow, gapStyles.gap_12]}
      >
        <View
          style={[
            globalStyles.justifyContentCenter,
            globalStyles.alignItemsCenter,
            globalStyles.relative,
            borderRadiusStyles.br6,
            globalStyles.overflowHidden,
            {
              width: RFont(72),
              height: RFont(72),
            },
          ]}
        >
          <Image source={BuildPlanImg} style={style.image} />
          {mealStaus == 'Taken' && (
            <View
              style={[
                globalStyles.justifyContentCenter,
                globalStyles.alignItemsCenter,
                globalStyles.absolute,
                {
                  width: RFont(72),
                  height: RFont(72),
                  backgroundColor: colors?.black_30_p,
                },
              ]}
            >
              <ConfirmIcon width={RFont(32)} height={RFont(32)} />
            </View>
          )}
        </View>
        <View style={[gapStyles.gap_4, globalStyles.flex1]}>
          <View
            style={[
              globalStyles.flexDirectionRow,
              globalStyles.justifyContentSpaceBetween,
            ]}
          >
            <Text
              style={[
                fontStyles.Maison_500_13PX_15_2LH,
                { color: colors?.grey_700 },
              ]}
            >
              {mealType}
            </Text>
            {rating && (
              <View
                style={[
                  paddingStyles.px6,
                  paddingStyles.py2,
                  gapStyles.gap_4,
                  borderRadiusStyles.br16,
                  globalStyles.flexDirectionRow,
                  { backgroundColor: colors?.primary_cream },
                ]}
              >
                <StarRatingDisplay
                  starSize={RFont(16)}
                  rating={1}
                  maxStars={1}
                />
                <Text
                  style={[
                    fontStyles.Maison_600_14PX_16_8LH,
                    { color: colors?.primary_spinach },
                  ]}
                >
                  {rating?.toFixed(1)}
                </Text>
              </View>
            )}
          </View>
          <Text
            style={[
              fontStyles.Maison_600_16PX_20_8LH,
              { color: colors?.black_900 },
            ]}
          >
            {mealName}
          </Text>
          <View
            style={[
              globalStyles.flexDirectionRow,
              gapStyles.gap_6,
              globalStyles.alignItemsCenter,
              globalStyles.flexWrap,
            ]}
          >
            {mealVarient && (
              <Text
                style={[
                  fontStyles.Maison_400_13PX_15_6LH,
                  { color: colors?.neutral_black },
                ]}
              >
                with {mealVarient}
              </Text>
            )}
            {mealVarient && (
              <View
                style={[
                  paddingStyles.p4,
                  borderRadiusStyles.br16,
                  { backgroundColor: colors?.neutral_black },
                ]}
              />
            )}
            <View
              style={[
                globalStyles.flexDirectionRow,
                globalStyles.alignItemsCenter,
                gapStyles.gap_6,
              ]}
            >
              <Text
                style={[
                  fontStyles.Maison_600_14PX_16_8LH,
                  { color: colors?.primary_spinach },
                ]}
              >
                {mealKcal}
              </Text>
              <Text
                style={[
                  fontStyles.Maison_400_13PX_15_6LH,
                  { color: colors?.neutral_black },
                ]}
              >
                KCal
              </Text>
            </View>
          </View>
          {mealStaus === 'Taken' && !rating && (
            <View
              style={[
                paddingStyles.py6,
                gapStyles.gap_12,
                globalStyles.flexDirectionRow,
                globalStyles.alignItemsCenter,
              ]}
            >
              <Text
                style={[
                  fontStyles.Maison_600_16PX_20_8LH,
                  { color: colors?.primary_spinach },
                ]}
              >
                Rate this meal
              </Text>
              <StarRating
                rating={5}
                color={colors?.grey_200}
                onChange={() => setRating(true)}
              />
            </View>
          )}
        </View>
        {mealStaus == 'Delivered' && (
          <TouchableOpacity
            style={[
              buttonStyles.Checkbox,
              globalStyles.alignItemsCenter,
              { borderColor: colors?.secondary_warm_grey },
            ]}
          />
        )}
      </Pressable>
      <MealItemBottomSheet
        isOpen={isOpen}
        meal_item={meal_item}
        onClose={() => setOpen(false)}
        avoid_ingredients={deliverybyDate?.avoid_ingredients}
        avoid_ingredients_tl={deliverybyDate?.avoid_ingredients_tl}
        footer={
          mealStaus === 'Delivered' || mealStaus == 'Taken' ? (
            <View style={[paddingStyles.px16, paddingStyles.py12]}>
              <PrimaryBtn
                text={
                  mealStaus === 'Delivered' ? 'Log as taken' : 'Rate this meal'
                }
                onPress={() => {
                  if (mealStaus === 'Delivered' && deliverybyDate) {
                    dispatch(
                      setMealLog(
                        {
                          delivery_id: deliverybyDate._id,
                          unique_id: meal_item?.selected_meal?.unique_id,
                        },
                        (res) => {
                          if (res?.status) {
                            setOpen(false);
                            if (authUser?._id)
                              dispatch(
                                getDeliveryByDate(selectedDate, authUser?._id),
                              );
                          } else {
                            showErrorToast(res?.message);
                          }
                        },
                      ),
                    );
                  } else {
                    setRating(true);
                  }
                }}
              />
            </View>
          ) : null
        }
      />
      {deliverybyDate?._id && (
        <MealRatingBottomSheet
          meal_item={meal_item}
          delivery_id={deliverybyDate?._id}
          isOpen={isRating}
          onClose={() => setRating(false)}
        />
      )}
    </React.Fragment>
  );
};

export const MealItemSkeletonLoader = () => {
  return (
    <View style={[globalStyles.flexDirectionRow, gapStyles.gap_12]}>
      <SkeletonLoader
        style={borderRadiusStyles.br6}
        height={RFont(72)}
        width={RFont(72)}
      />

      <View style={[gapStyles.gap_4, globalStyles.flex1]}>
        <SkeletonLoader height={RFont(15)} width={'50%'} />
        <SkeletonLoader height={RFont(20)} width={'80%'} />
        <SkeletonLoader height={RFont(20)} width={'60%'} />
      </View>
    </View>
  );
};
const style = StyleSheet.create({
  image: {
    height: RFont(72),
    resizeMode: 'cover',
    width: RFont(72),
  },
});
export default AccountMealItemcard;
