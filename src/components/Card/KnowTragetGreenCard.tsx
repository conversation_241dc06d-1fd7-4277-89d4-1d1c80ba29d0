import { KnowTragetGreenCardProps } from 'card-props';
import { FC } from 'react';
import { Pressable, Text, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { useSelector } from 'react-redux';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontStyles } from '../../theme/fonts';
import { cardStyles } from '../../theme/styles/cardStyles';
import {
  gapStyles,
  marginStyles,
} from '../../theme/styles/commonMarginPadding';
import { globalStyles } from '../../theme/styles/globalStyles';

const KnowTragetGreenCard: FC<KnowTragetGreenCardProps> = ({
  onPress,
  cardStyle,
  title,
  titleStyle,
  desc,
  imageSource,
  children,
}) => {
  const { isRTL } = useSelector((state: RootState) => state?.app);
  const colors = useTheme();
  return (
    <Pressable onPress={onPress}>
      <LinearGradient
        colors={['#bee177', '#ffeca2']}
        style={[
          cardStyles.cardR16_P16_BoxShadow1,
          marginStyles.mt_20,
          cardStyle,
          gapStyles.gap_8,
        ]}
      >
        <View
          style={[
            isRTL ? globalStyles.rowReverse : globalStyles.row,
            globalStyles.alignItemsCenter,
            globalStyles.flex1,
            gapStyles.gap_4,
            globalStyles.justifyContentSpaceBetween,
          ]}
        >
          <Text
            style={[
              fontStyles.Maison_600_20PX_26LH,
              titleStyle,
              { color: colors?.primary_spinach },
            ]}
          >
            {title}
          </Text>
          {imageSource}
        </View>

        {desc ? (
          <Text
            style={[
              isRTL ? globalStyles.textRight : globalStyles.textLeft,
              fontStyles.Maison_400_14PX_18LH,
              { color: colors?.grey_800 },
            ]}
          >
            {desc}
          </Text>
        ) : null}
        {children}
      </LinearGradient>
    </Pressable>
  );
};
export default KnowTragetGreenCard;
