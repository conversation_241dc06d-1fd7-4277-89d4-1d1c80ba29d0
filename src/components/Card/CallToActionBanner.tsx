import { CallToActionBannerProps } from 'card-props';
import React from 'react';
import { ImageBackground, Text, View } from 'react-native';
import { buildYourBgStyle } from '../../theme/styles/cardStyles';

export default function CallToActionBanner({
  title,
  imageSource,
  titleStyle,
}: CallToActionBannerProps) {
  return (
    <View style={buildYourBgStyle.container}>
      <ImageBackground
        source={imageSource}
        style={buildYourBgStyle.background}
        imageStyle={buildYourBgStyle.image}
      >
        <View style={buildYourBgStyle.overlay} />
        <Text style={[buildYourBgStyle.title, titleStyle]}>{title}</Text>
      </ImageBackground>
    </View>
  );
}
