import { BottomFloatingCardProps } from 'componentsProps';
import React from 'react';
import { View } from 'react-native';
import { useSelector } from 'react-redux';
import { RootState } from '../../redux/store';
import { fontStyles } from '../../theme/fonts';
import { bottomFloatingCardStyles } from '../../theme/styles/cardStyles';
import { PrimaryBtn } from '../Buttons/Btns';

const BottomFloatingCard: React.FC<BottomFloatingCardProps> = ({
  btnText = '',
  onPress,
}) => {
  const { isKeybordshow } = useSelector((state: RootState) => state?.app);
  return isKeybordshow ? null : (
    <View style={bottomFloatingCardStyles.card}>
      <PrimaryBtn
        onPress={onPress}
        textStyle={fontStyles?.Maison_600_16PX_20LH}
        text={btnText}
      />
    </View>
  );
};

export default BottomFloatingCard;
