import React, { FC } from 'react';
import { Text, TextStyle, View, ViewStyle } from 'react-native';
import { useSelector } from 'react-redux';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontStyles } from '../../theme/fonts';
import { cardStyles } from '../../theme/styles/cardStyles';
import {
  marginStyles,
  paddingStyles,
} from '../../theme/styles/commonMarginPadding';
import { globalStyles } from '../../theme/styles/globalStyles';

interface WhyChooseUsProps {
  containerStyle?: ViewStyle;
  iconStyle?: ViewStyle;
  title: string;
  icon?: React.ReactNode;
  titleStyle?: TextStyle;
  data: Array<{ title: string }>;
}
const WhyChooseUs: FC<WhyChooseUsProps> = ({
  containerStyle,
  title,
  titleStyle,
  iconStyle,
  data,
  icon,
}) => {
  const colors = useTheme();
  const { isRTL } = useSelector((state: RootState) => state?.app);
  return (
    <View
      style={[
        cardStyles?.cardR8_P16_Transprent,
        containerStyle,
        marginStyles?.mt_20,
      ]}
    >
      <Text style={[fontStyles?.Maison_600_32PX_40LH, titleStyle]}>
        {title}
      </Text>
      {data?.map((itm, idx) => (
        <View
          style={[
            isRTL ? globalStyles.rowReverse : globalStyles.row,
            globalStyles?.flexDirectionRow,
            globalStyles?.justifyContentFlexStart,
            marginStyles.mt_10,
            paddingStyles?.p4,
          ]}
          key={`WhyChooseUs-${itm}-${idx}`}
        >
          {icon && (
            <View style={[iconStyle, marginStyles?.mr_8, marginStyles?.mt_4]}>
              {icon}
            </View>
          )}

          <Text
            style={[
              fontStyles?.Maison_500_16PX_18LH,
              { color: colors?.grey_700 },
            ]}
          >
            {itm?.title}
          </Text>
        </View>
      ))}
    </View>
  );
};

export default WhyChooseUs;
