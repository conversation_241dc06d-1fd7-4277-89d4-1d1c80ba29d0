import React, { FC } from 'react';
import { StyleProp, Text, TextStyle, View, ViewStyle } from 'react-native';
import { useSelector } from 'react-redux';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontStyles } from '../../theme/fonts';
import { cardStyles } from '../../theme/styles/cardStyles';
import {
  gapStyles,
  marginStyles,
} from '../../theme/styles/commonMarginPadding';
import { globalStyles } from '../../theme/styles/globalStyles';

interface InfoBannerProps {
  cardStyle?: StyleProp<ViewStyle>;
  leftIcon?: React.ReactNode;
  iconStyle?: ViewStyle;
  textStyle?: TextStyle;
  message: string;
}
const InfoBanner: FC<InfoBannerProps> = ({
  cardStyle,
  leftIcon,
  iconStyle,
  textStyle,
  message,
}) => {
  const { isRTL } = useSelector((state: RootState) => state?.app);
  const colors = useTheme();
  return (
    <View
      style={[
        cardStyles.cardR12_P10_BoxShadow1,
        marginStyles?.mt_20,
        { backgroundColor: colors?.primary_cream },
        cardStyle,
        isRTL ? globalStyles.rowReverse : globalStyles.row,
        gapStyles?.gap_6,
      ]}
    >
      {leftIcon && <View style={iconStyle}>{leftIcon}</View>}

      <Text
        style={[
          fontStyles.Maison_400_14PX_18LH,
          { color: colors?.grey_700 },
          textStyle,
        ]}
      >
        {message}
      </Text>
    </View>
  );
};

export default InfoBanner;
