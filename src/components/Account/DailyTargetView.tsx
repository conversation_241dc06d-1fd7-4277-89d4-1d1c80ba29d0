import { FC, useState } from 'react';
import { Text, View } from 'react-native';
import { useSelector } from 'react-redux';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontStyles } from '../../theme/fonts';
import {
  gapStyles,
  paddingStyles,
} from '../../theme/styles/commonMarginPadding';
import { globalStyles } from '../../theme/styles/globalStyles';
import CircularProgress from '../Badges/CircularProgress';
import { CommonCard } from '../Card/Card';
import EditDailyTargetBottomSheet from './EditDailyTargetBottomSheet';
interface DailyTargetViewProps {
  totalKcal: number;
  consumeKcal: number;
  totalProten: number;
  consumeProten: number;
  totalCarbs: number;
  consumeCarbs: number;
  totalFat: number;
  consumeFat: number;
  subscription_id: string;
}
const DailyTargetView: FC<DailyTargetViewProps> = ({
  consumeKcal,
  totalKcal,
  consumeProten,
  totalProten,
  consumeCarbs,
  totalCarbs,
  consumeFat,
  totalFat,
  subscription_id,
}) => {
  const [isOpen, setOpen] = useState(false);
  const colors = useTheme();
  const { translation } = useSelector((state: RootState) => state.auth);
  return (
    <CommonCard style={[paddingStyles.px16, gapStyles.gap_16]}>
      <View
        style={[
          globalStyles.flexDirectionRow,
          globalStyles.justifyContentSpaceBetween,
          globalStyles.alignItemsCenter,
        ]}
      >
        <Text
          style={[
            fontStyles.Maison_600_20PX_28LH,
            { color: colors?.primary_spinach },
          ]}
        >
          Your daily target
        </Text>
        <Text
          onPress={() => setOpen(true)}
          style={[
            fontStyles.Maison_600_14PX_16_8LH,
            { color: colors?.primary_grenade },
          ]}
        >
          Update
        </Text>
      </View>
      <View
        style={[
          globalStyles.flexDirectionRow,
          globalStyles.justifyContentSpaceBetween,
          globalStyles.alignItemsCenter,
        ]}
      >
        <CircularProgress
          label={translation?.KCAL}
          percentage={(consumeKcal * 100) / totalKcal || 0}
          left={totalKcal - consumeKcal + ' left'}
          total={totalKcal.toFixed(0)}
          consume={consumeKcal.toFixed(0)}
        />
        <CircularProgress
          label={translation?.PROTEIN}
          percentage={(consumeProten * 100) / totalProten || 0}
          left={totalProten - consumeProten + 'g left'}
          total={totalProten.toFixed(0) + 'g'}
          consume={consumeProten.toFixed(0)}
        />
        <CircularProgress
          label={translation?.CARB}
          percentage={(consumeCarbs * 100) / totalCarbs || 0}
          left={totalCarbs - consumeCarbs + 'g left'}
          total={totalCarbs.toFixed(0) + 'g'}
          consume={consumeCarbs.toFixed(0)}
        />
        <CircularProgress
          label={translation?.FAT}
          percentage={(consumeFat * 100) / totalFat || 0}
          left={totalFat - consumeFat + 'g left'}
          total={totalFat.toFixed(0) + 'g'}
          consume={consumeFat.toFixed(0)}
        />
      </View>
      <EditDailyTargetBottomSheet
        subscription_id={subscription_id}
        isOpen={isOpen}
        onclose={() => setOpen(false)}
      />
    </CommonCard>
  );
};

export default DailyTargetView;
