import { Lanobj } from 'app-actions';
import { DeliveryItemProps } from 'delivery-slice';
import { FC, ReactNode } from 'react';
import { Text, View } from 'react-native';
import { useSelector } from 'react-redux';
import { CouponIcon } from '../../assets/images';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontStyles, RFont } from '../../theme/fonts';
import {
  gapStyles,
  paddingStyles,
} from '../../theme/styles/commonMarginPadding';
import { globalStyles } from '../../theme/styles/globalStyles';
import { removeDuplicates } from '../../utils/functions';
import { GorhomBottomSheet } from '../BottomSheet/BottomSheet';
import { SelectableSlotChip, WarningChip } from '../Buttons/Chips';
interface ProtineValueProps {
  value: string;
  type: string;
}
const ProtineValue: FC<ProtineValueProps> = ({ type, value }) => {
  const colors = useTheme();
  return (
    <View
      style={[
        globalStyles.flexDirectionRow,
        globalStyles.alignItemsCenter,
        gapStyles.gap_4,
      ]}
    >
      <Text
        style={[
          fontStyles.Maison_600_20PX_28LH,
          { color: colors?.primary_spinach },
        ]}
      >
        {value}
      </Text>
      <Text
        style={[
          fontStyles.Maison_400_12PX_16LH,
          { color: colors?.primary_spinach },
        ]}
      >
        {type}
      </Text>
    </View>
  );
};

interface MealItemBottomSheetPrpos {
  meal_item: DeliveryItemProps;
  isOpen: boolean;
  onClose: () => void;
  avoid_ingredients_tl?: Array<Lanobj>;
  avoid_ingredients?: Array<string>;
  footer?: ReactNode | ReactNode[];
}
const MealItemBottomSheet: FC<MealItemBottomSheetPrpos> = ({
  isOpen,
  meal_item,
  onClose,
  avoid_ingredients,
  avoid_ingredients_tl,
  footer,
}) => {
  const colors = useTheme();
  const { lanKey } = useSelector((state: RootState) => state.app);
  let warningIngredients =
    avoid_ingredients_tl &&
    meal_item?.selected_meal?.ingredients_tl &&
    meal_item?.selected_meal?.variants.variant_ingredients_tl
      ? avoid_ingredients_tl
          ?.map((e) => e[lanKey])
          ?.filter(function (el) {
            return (
              [
                ...(meal_item?.selected_meal?.ingredients_tl?.map(
                  (e) => e[lanKey],
                ) || []),
                ...(meal_item?.selected_meal?.variants.variant_ingredients_tl?.map(
                  (e) => e[lanKey],
                ) || []),
              ].indexOf(el) > -1
            );
          }) || []
      : avoid_ingredients?.filter(function (el) {
          return (
            [
              ...(meal_item?.selected_meal?.ingredients || []),
              ...(meal_item?.selected_meal?.variants.variant_ingredients || []),
            ].indexOf(el) > -1
          );
        }) || [];
  let allIng =
    meal_item?.selected_meal?.ingredients_tl?.map((e) => e[lanKey]).length >
      0 &&
    meal_item?.selected_meal?.variants.variant_ingredients_tl?.map(
      (e) => e[lanKey],
    ).length > 0
      ? removeDuplicates([
          ...(meal_item?.selected_meal?.ingredients_tl?.map((e) => e[lanKey]) ||
            []),
          ...(meal_item?.selected_meal?.variants.variant_ingredients_tl?.map(
            (e) => e[lanKey],
          ) || []),
        ])
      : removeDuplicates([
          ...(meal_item?.selected_meal?.ingredients || []),
          ...(meal_item?.selected_meal?.variants.variant_ingredients || []),
        ]);
  return (
    <GorhomBottomSheet
      title={
        meal_item?.selected_meal?.dish_name_tl[lanKey] ||
        meal_item?.selected_meal?.dish_name
      }
      sheetOpen={isOpen}
      sheetClose={onClose}
      footer={footer}
    >
      <View style={gapStyles.gap_24}>
        <CouponIcon height={RFont(240)} width={'100%'} />
        <View
          style={[
            paddingStyles.px16,
            paddingStyles.py8,
            globalStyles.flexDirectionRow,
            globalStyles.justifyContentSpaceBetween,
          ]}
        >
          <ProtineValue
            type="Kcal"
            value={`${meal_item?.selected_meal?.variants?.kcal}`}
          />
          <ProtineValue
            type="Protein"
            value={`${meal_item?.selected_meal?.variants?.protein}g`}
          />
          <ProtineValue
            type="Carb"
            value={`${meal_item?.selected_meal?.variants?.carb}g`}
          />
          <ProtineValue
            type="Fat"
            value={`${meal_item?.selected_meal?.variants?.fat}g`}
          />
        </View>
        <Text
          style={[fontStyles.Maison_400_16PX_22LH, { color: colors?.grey_800 }]}
        >
          {meal_item?.selected_meal?.description_tl?.[lanKey] ||
            meal_item?.selected_meal?.description}
        </Text>
        <View style={gapStyles.gap_12}>
          <Text
            style={[
              fontStyles.Maison_600_20PX_28LH,
              { color: colors?.neutral_black },
            ]}
          >
            What’s in it?
          </Text>
          <View
            style={[
              globalStyles.flexDirectionRow,
              globalStyles.flexWrap,
              gapStyles.gap_8,
            ]}
          >
            {warningIngredients?.map((e) => {
              return (
                <WarningChip key={'warning - Ingredients ' + e} text={e} />
              );
            })}
          </View>
          <View
            style={[
              globalStyles.flexDirectionRow,
              globalStyles.flexWrap,
              gapStyles.gap_8,
            ]}
          >
            {allIng
              .filter((e) => !warningIngredients.includes(e))
              ?.map((e) => {
                return (
                  <SelectableSlotChip
                    key={e}
                    text={e}
                    style={{
                      backgroundColor: colors?.primary_cream,
                      borderColor: colors?.primary_cream,
                    }}
                  />
                );
              })}
          </View>
        </View>
      </View>
    </GorhomBottomSheet>
  );
};

export default MealItemBottomSheet;
