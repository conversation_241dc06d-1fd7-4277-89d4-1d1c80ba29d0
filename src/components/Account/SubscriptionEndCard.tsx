import moment from 'moment';
import { useEffect, useState } from 'react';
import { Text, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { useDispatch, useSelector } from 'react-redux';
import { CalanderXIcon, ClockYellowIcon } from '../../assets/images';
import { useAppNavigation } from '../../hooks/useAppNavigation';
import { renewSubscription } from '../../redux/action/subscriptionAction';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontStyles, RFont } from '../../theme/fonts';
import {
  gapStyles,
  paddingStyles,
} from '../../theme/styles/commonMarginPadding';
import {
  borderRadiusStyles,
  borderWidthStyles,
  globalStyles,
  widthStyles,
} from '../../theme/styles/globalStyles';
import {
  daysPending,
  getDataFromAsyncStorage,
  showErrorToast,
  storeDataToAsyncStorage,
} from '../../utils/functions';
import { PrimaryBtn } from '../Buttons/Btns';
interface SubscriptionEndCardProps {
  isExpired?: boolean;
}
const SubscriptionEndCard = ({ isExpired }: SubscriptionEndCardProps) => {
  const dispatch = useDispatch();
  const colors = useTheme();
  const navigation = useAppNavigation();
  const [hide, setHide] = useState(true);
  const { isRTL } = useSelector((state: RootState) => state.app);
  const { translation, authUser } = useSelector(
    (state: RootState) => state.auth,
  );
  const { subscriptionDetails } = useSelector(
    (state: RootState) => state.subscription,
  );
  let pending = daysPending(
    subscriptionDetails[subscriptionDetails.length - 1]?.end_date || '',
  );
  useEffect(() => {
    getDataFromAsyncStorage('EndCard').then((data) => {
      if (
        pending <= 10 &&
        (!data || (data && moment(data).isSameOrBefore(moment())))
      ) {
        setHide(false);
      } else {
        setHide(true);
      }
    });
  }, [pending]);
  return !hide ? null : (
    <LinearGradient
      start={{ x: 0.1, y: 1 }}
      end={{ x: 0.3, y: 1 }}
      colors={isExpired ? colors?.red_gradient : colors?.yellow_gradient}
      style={[gapStyles.gap_20, paddingStyles.p16, borderRadiusStyles.br20]}
    >
      <View style={gapStyles.gap_8}>
        {isExpired ? (
          <CalanderXIcon width={RFont(24)} height={RFont(24)} />
        ) : (
          <ClockYellowIcon width={RFont(24)} height={RFont(24)} />
        )}
        <Text
          style={[
            fontStyles.Maison_600_20PX_28LH,
            { color: isExpired ? colors?.red_600 : colors?.secondary_curry },
          ]}
        >
          {isExpired
            ? translation?.ENDED_SUBSCRIPTION_TITLE
            : `Only ${pending}  days left in your subscription`}{' '}
        </Text>
        <Text
          style={[fontStyles.Maison_400_14PX_18LH, { color: colors?.grey_800 }]}
        >
          {isExpired
            ? translation?.ENDED_SUBSCRIPTION_SUB_TITLE
            : 'Renew now to continue receiving meals without interruption.'}
        </Text>
      </View>
      <View
        style={[
          isExpired
            ? isRTL
              ? globalStyles.flexDirectionColumn
              : globalStyles.flexColumnReverse
            : isRTL
              ? globalStyles.rowReverse
              : globalStyles.flexDirectionRow,
          globalStyles.alignItemsCenter,
          gapStyles.gap_12,
          globalStyles.justifyContentSpaceBetween,
        ]}
      >
        <PrimaryBtn
          text={isExpired ? 'Edit my plan' : 'Not now'}
          style={[
            isExpired && widthStyles.w100,
            borderWidthStyles.bw1,
            {
              borderColor: colors?.primary_spinach_10_p,
              backgroundColor: colors?.transparent,
            },
          ]}
          textStyle={{
            color: colors?.primary_spinach,
          }}
          onPress={() => {
            if (isExpired) {
              // add logic of edit meal plan
            } else {
              setHide(true);
              storeDataToAsyncStorage(
                'EndCard',
                moment().add(1, 'day').format('YYYY-MM-DD'),
              );
            }
          }}
        />
        <PrimaryBtn
          text="Renew now"
          onPress={() => {
            dispatch(
              renewSubscription(
                {
                  customer_id: authUser?._id || '',
                  subscription_id:
                    subscriptionDetails[subscriptionDetails.length - 1]?._id ||
                    '',
                },
                (res) => {
                  if (res.status) {
                    navigation.navigate('MealPlan');
                  } else {
                    showErrorToast(res.message);
                  }
                },
              ),
            );
          }}
          style={[
            isExpired && widthStyles.w100,
            {
              backgroundColor: isExpired
                ? colors?.primary_grenade
                : colors?.primary_spinach,
            },
          ]}
        />
      </View>
    </LinearGradient>
  );
};

export default SubscriptionEndCard;
