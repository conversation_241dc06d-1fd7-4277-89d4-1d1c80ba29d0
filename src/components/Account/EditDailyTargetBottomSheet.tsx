import { useFormik } from 'formik';
import { FC, useEffect, useState } from 'react';
import { View } from 'react-native';
import { useSelector } from 'react-redux';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import {
  gapStyles,
  paddingStyles,
} from '../../theme/styles/commonMarginPadding';
import { borderRadiusStyles, boxStyle } from '../../theme/styles/globalStyles';
import { GorhomBottomSheet } from '../BottomSheet/BottomSheet';
import { PrimaryBtn } from '../Buttons/Btns';
import OptionCard from '../Card/OptionCard';
import { TextInputField } from '../TextInputField/TextInputField';
interface EditDailyTargetBottomSheetProps {
  isOpen: boolean;
  onclose: () => void;
  subscription_id: string;
}
const EditDailyTargetBottomSheet: FC<EditDailyTargetBottomSheetProps> = ({
  isOpen,
  onclose,
  subscription_id,
}) => {
  const { subscriptionDetails } = useSelector(
    (state: RootState) => state.subscription,
  );
  const colors = useTheme();
  const [isManual, setManual] = useState(false);
  const [selectoin, setSelection] = useState(false);

  const formik = useFormik({
    enableReinitialize: true,
    initialValues: {
      calories:
        subscriptionDetails?.find((e) => e._id == subscription_id)?.macros
          ?.calories || 0,
      protein:
        subscriptionDetails?.find((e) => e._id == subscription_id)?.macros
          ?.protein || 0,
      fat:
        subscriptionDetails?.find((e) => e._id == subscription_id)?.macros
          ?.fat || 0,
      carbs:
        subscriptionDetails?.find((e) => e._id == subscription_id)?.macros
          ?.carbs || 0,
    },
    onSubmit() {},
  });
  const { values, setFieldValue, resetForm } = formik;
  useEffect(() => {
    return () => {
      resetForm();
      setManual(false);
      setSelection(false);
    };
  }, [isOpen, resetForm]);
  return (
    <GorhomBottomSheet
      title="Update your daily target"
      sheetOpen={isOpen}
      sheetClose={onclose}
      footer={
        <View
          style={[
            boxStyle.shadow,
            borderRadiusStyles.bt20,
            paddingStyles.px16,
            paddingStyles.py20,
            { backgroundColor: colors?.neutral_white },
          ]}
        >
          <PrimaryBtn
            text="Recalculate"
            onPress={() => {
              if (!selectoin) {
                setSelection(true);
              }
            }}
          />
        </View>
      }
    >
      {selectoin ? (
        <View style={gapStyles.gap_16}>
          <TextInputField
            keyboardType={'number-pad'}
            header="Calories"
            value={values.calories?.toString()}
            onChangeText={(value) =>
              setFieldValue('calories', parseInt(value || '0'), true)
            }
          />
          <TextInputField
            keyboardType={'number-pad'}
            header="Protein (g)"
            value={values.protein?.toString()}
            onChangeText={(value) =>
              setFieldValue('protein', parseInt(value || '0'), true)
            }
          />
          <TextInputField
            keyboardType={'number-pad'}
            header="Carbs (g)"
            value={values.carbs?.toString()}
            onChangeText={(value) =>
              setFieldValue('carbs', parseInt(value || '0'), true)
            }
          />
          <TextInputField
            keyboardType={'number-pad'}
            header="Fat (g)"
            value={values.fat?.toString()}
            onChangeText={(value) =>
              setFieldValue('fat', parseInt(value || '0'), true)
            }
          />
        </View>
      ) : (
        <View>
          <OptionCard
            title="Recalculate using your details"
            isSelected={!isManual}
            onPress={() => setManual(false)}
          />
          <OptionCard
            title="Set target manually"
            isSelected={isManual}
            onPress={() => setManual(true)}
          />
        </View>
      )}
    </GorhomBottomSheet>
  );
};
export default EditDailyTargetBottomSheet;
