import moment from 'moment';
import { useSelector } from 'react-redux';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontStyles } from '../../theme/fonts';
import { gapStyles } from '../../theme/styles/commonMarginPadding';
import AccountMealItemcard from '../Card/AccountMealItemcard';
import { CommonCard } from '../Card/Card';

const MealsView = () => {
  const colors = useTheme();
  const { lanKey } = useSelector((state: RootState) => state.app);
  const { deliverybyDate, selectedDate } = useSelector(
    (state: RootState) => state.delivery,
  );
  return (
    <CommonCard
      title={'Your meals for ' + moment(selectedDate).format('ddd DD, MMM')}
      titleStyle={[
        fontStyles.Maison_600_20PX_28LH,
        { color: colors?.primary_spinach },
      ]}
      style={gapStyles.gap_20}
    >
      {deliverybyDate?.delivery_item?.map((item) => {
        return (
          <AccountMealItemcard
            meal_item={item}
            key={item?.meal_type}
            mealType={item?.meal_type}
            mealName={
              item?.selected_meal?.dish_name_tl?.[lanKey] ||
              item?.selected_meal?.dish_name
            }
            mealKcal={item?.selected_meal?.variants?.kcal + 'g'}
            mealVarient={item?.selected_meal?.variants?.protein_option}
            mealStaus={
              item?.selected_meal?.is_taken
                ? 'Taken'
                : deliverybyDate?.status === 'Delivered'
                  ? 'Delivered'
                  : 'NotDelivered'
            }
            rating={item?.selected_meal?.rating_id?.rating}
          />
        );
      })}
    </CommonCard>
  );
};

export default MealsView;
