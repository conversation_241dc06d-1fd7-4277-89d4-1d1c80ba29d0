import moment from 'moment';
import { Share, Text } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { useSelector } from 'react-redux';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontStyles } from '../../theme/fonts';
import {
  gapStyles,
  paddingStyles,
} from '../../theme/styles/commonMarginPadding';
import {
  borderRadiusStyles,
  borderWidthStyles,
} from '../../theme/styles/globalStyles';
import { showErrorToast } from '../../utils/functions';
import { PrimaryBtn } from '../Buttons/Btns';
const ReferFriendCard = () => {
  const colors = useTheme();
  const { translation, authUser } = useSelector(
    (state: RootState) => state.auth,
  );
  const { subscriptionDetails } = useSelector(
    (state: RootState) => state.subscription,
  );
  let startdate = moment()
    .tz('Asia/Dubai')
    .isAfter(
      moment.tz(
        moment(
          subscriptionDetails?.[subscriptionDetails?.length - 1]?.end_date,
        ),
        'Asia/Dubai',
      ),
    );
  return (
    subscriptionDetails.length > 0 && (
      <LinearGradient
        start={{ x: 0.1, y: 1 }}
        end={{ x: 0.3, y: 1 }}
        colors={colors?.cyan_gradient}
        style={[gapStyles.gap_20, paddingStyles.p16, borderRadiusStyles.br20]}
      >
        <Text
          style={[
            fontStyles.Maison_600_20PX_28LH,
            { color: colors?.neutral_black },
          ]}
        >
          Refer a friend, get AED 20 off
        </Text>
        <Text
          style={[fontStyles.Maison_400_14PX_18LH, { color: colors?.grey_800 }]}
        >
          Invite your friends to try Delicut and get AED 20 off your next plan
          when they subscribe.
        </Text>
        <PrimaryBtn
          onPress={() => {
            Share.share(
              {
                title: translation?.SHARE_TITLE,
                url:
                  process.env.canonical_URL +
                  '?referral_code=' +
                  authUser?.referral_code,
                message: translation?.SHARE_MESSAGE,
              },
              {
                dialogTitle: translation?.SHARE_TITLE,
                subject: translation?.SHARE_TITLE,
              },
            )
              .then(() => null)
              .catch((error) => showErrorToast(error.message));
          }}
          style={[
            !startdate && borderWidthStyles.bw2,
            {
              borderColor: startdate ? undefined : colors?.primary_spinach,
              backgroundColor: startdate ? colors?.primary_spinach : undefined,
            },
          ]}
          textStyle={{
            color: startdate ? colors?.neutral_white : colors?.primary_spinach,
          }}
          text="Refer & earn"
        />
      </LinearGradient>
    )
  );
};

export default ReferFriendCard;
