import { DeliveryItemProps, MealType } from 'delivery-slice';
import { useFormik } from 'formik';
import { FC } from 'react';
import { View } from 'react-native';
import StarRating from 'react-native-star-rating-widget';
import { useDispatch, useSelector } from 'react-redux';
import * as yup from 'yup';
import { getDeliveryByDate } from '../../redux/action/deliveryActions';
import { setRecipeRating } from '../../redux/action/recipesActions';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { RFont } from '../../theme/fonts';
import {
  gapStyles,
  paddingStyles,
} from '../../theme/styles/commonMarginPadding';
import {
  borderRadiusStyles,
  globalStyles,
  heightStyles,
} from '../../theme/styles/globalStyles';
import { GorhomBottomSheet } from '../BottomSheet/BottomSheet';
import { PrimaryBtn } from '../Buttons/Btns';
import { TextInputField } from '../TextInputField/TextInputField';

const recipeRatingSchema = yup.object({
  customer_id: yup.string().required('Customer ID is required'),
  type: yup.mixed<MealType>().required('Meal type is required'),
  delivery_id: yup.string().required('Delivery ID is required'),
  unique_id: yup.string().required('Unique ID is required'),
  count: yup.number().required('Count is required'),
  comment: yup
    .string()
    .trim()
    .when('count', ([count], schema) => {
      if (count < 4) {
        return schema
          .required('Feedback is required.')
          .min(25, 'Feedback must be at least 25 characters long.');
      }
      return schema.notRequired().nullable();
    }),
  review: yup.array().of(yup.string()).required('Review is required'),
});

interface MealRatingBottomSheetPrpos {
  isOpen: boolean;
  onClose: () => void;
  meal_item: DeliveryItemProps;
  delivery_id: string;
}
const MealRatingBottomSheet: FC<MealRatingBottomSheetPrpos> = ({
  isOpen,
  onClose,
  meal_item,
  delivery_id,
}) => {
  const { authUser } = useSelector((state: RootState) => state.auth);
  const { selectedDate } = useSelector((state: RootState) => state.delivery);
  const colors = useTheme();
  const dispatch = useDispatch();
  const formik = useFormik({
    initialValues: {
      customer_id: authUser?._id || '',
      type: meal_item?.meal_type,
      delivery_id: delivery_id,
      unique_id: meal_item?.selected_meal?.unique_id,
      count: meal_item?.selected_meal?.rating_id?.rating || 0,
      comment: meal_item?.selected_meal?.rating_id?.comment || '',
      review: meal_item?.selected_meal?.rating_id?.review || [],
    },
    onSubmit(values, formikHelpers) {
      dispatch(
        setRecipeRating(values, (res) => {
          formikHelpers.setSubmitting(false);
          if (res.status) {
            onClose();
            if (authUser?._id)
              dispatch(getDeliveryByDate(selectedDate, authUser?._id));
          }
        }),
      );
    },
    validationSchema: recipeRatingSchema,
  });
  const { values, setFieldValue, isSubmitting, handleSubmit, errors, touched } =
    formik;
  return (
    <GorhomBottomSheet
      title={'Rate your meal'}
      sheetOpen={isOpen}
      sheetClose={onClose}
      footer={
        <View
          style={[paddingStyles.px16, paddingStyles.pt16, paddingStyles.pb12]}
        >
          <PrimaryBtn
            text="Submit rating"
            disabled={isSubmitting}
            onPress={handleSubmit}
          />
        </View>
      }
    >
      <View style={gapStyles.gap_12}>
        <View>
          <StarRating
            starSize={RFont(48)}
            rating={values.count}
            onChange={(value) => setFieldValue('count', value, true)}
            maxStars={5}
            enableHalfStar={false}
            color={colors?.primary_grenade}
            emptyColor={colors?.grey_7A7A7A}
          />
        </View>
        <TextInputField
          placeholder="Anything you’d like us to improve?"
          numberOfLines={10}
          value={values.comment}
          onChangeText={(val) => setFieldValue('comment', val, true)}
          wrapperStyle={[
            borderRadiusStyles.br16,
            paddingStyles.px24,
            paddingStyles.py16,
            {
              borderColor:
                errors?.comment && touched?.comment
                  ? colors?.red_500
                  : colors?.secondary_warm_grey,
            },
          ]}
          inputStyles={[globalStyles.verticalAlignTop, heightStyles.h158]}
          error={errors?.comment}
          touched={touched?.comment}
        />
      </View>
    </GorhomBottomSheet>
  );
};

export default MealRatingBottomSheet;
