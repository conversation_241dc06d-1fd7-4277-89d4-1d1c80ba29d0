import { WeekDeliveryItemProps } from 'delivery-slice';
import moment from 'moment-timezone';
import { FC } from 'react';
import { Pressable, StyleSheet, Text, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { PlayIcon, SkippedTruckIcon } from '../../assets/images';
import { restartDelivery } from '../../redux/action/deliveryActions';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontStyles, RFont } from '../../theme/fonts';
import { gapStyles } from '../../theme/styles/commonMarginPadding';
import { showErrorToast, showSuccessToast } from '../../utils/functions';
import { CommonCard } from '../Card/Card';
import Divider from '../Divider';

interface SkippedMealCardProps {
  item: WeekDeliveryItemProps;
  onSuccessResume?: () => void;
}

const SkippedMealCard: FC<SkippedMealCardProps> = ({
  item,
  onSuccessResume,
}) => {
  const { authUser } = useSelector((state: RootState) => state.auth);
  const colors = useTheme();
  const dispatch = useDispatch();

  const now = moment().tz('Asia/Dubai');
  const targetTime = moment(moment(item?.delivery_date).format('YYYY-MM-DD'))
    .tz('Asia/Dubai')
    .subtract(2, 'days')
    .set({ hour: 12, minute: 0, second: 0 });
  let countDown = Math.floor(moment.duration(targetTime.diff(now)).asHours());

  return (
    <CommonCard style={gapStyles.gap_20}>
      <View style={style.titleWrapper}>
        <Text
          style={[
            fontStyles.Maison_600_20PX_28LH,
            { color: colors?.neutral_black },
          ]}
        >
          {moment(item?.delivery_date)?.format('ddd, DD MMM')}
        </Text>
        <View style={style.titleWrapper}>
          <SkippedTruckIcon height={RFont(19)} width={RFont(20)} />
          <Text
            style={[
              fontStyles.Marison_600_12PX_16LH,
              { color: colors?.grey_400 },
            ]}
          >
            Skipped
          </Text>
        </View>
      </View>
      <Text
        style={[fontStyles.Maison_400_14PX_16LH, { color: colors?.grey_600 }]}
      >
        {countDown > 0
          ? `You still have ${countDown} hours to resume delivery for this day`
          : 'cut-off time to update delivery is over.        '}
      </Text>
      {countDown > 0 && (
        <Divider margin={0} color={colors?.secondary_warm_grey_40_p} />
      )}
      {countDown > 0 && (
        <Pressable
          onPress={() => {
            if (authUser?._id)
              dispatch(
                restartDelivery(
                  {
                    customer_id: authUser?._id,
                    restart_dates: [
                      moment(item?.delivery_date).format('YYYY-MM-DD'),
                    ],
                    subscription_id: item?.subscription_id,
                  },
                  (res) => {
                    if (res.status) {
                      if (onSuccessResume) onSuccessResume();
                      showSuccessToast(res?.message);
                    } else {
                      showErrorToast(res?.message);
                    }
                  },
                ),
              );
          }}
          style={style.buttonWrapper}
        >
          <PlayIcon width={RFont(20)} height={RFont(20)} />
          <Text
            style={[
              fontStyles?.Maison_600_14PX_16_8LH,
              { color: colors?.primary_grenade },
            ]}
          >
            Resume delivery
          </Text>
        </Pressable>
      )}
    </CommonCard>
  );
};
const style = StyleSheet.create({
  buttonWrapper: {
    alignSelf: 'center',
    flexDirection: 'row',
    gap: RFont(8),
    justifyContent: 'center',
  },
  titleWrapper: {
    alignItems: 'center',
    flexDirection: 'row',
    gap: RFont(8),
    justifyContent: 'space-between',
  },
});
export default SkippedMealCard;
