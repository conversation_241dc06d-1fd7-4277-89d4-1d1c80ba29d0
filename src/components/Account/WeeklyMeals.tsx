import { ScrollView, Text, View } from 'react-native';
import { useSelector } from 'react-redux';
import { BuildPlanImg } from '../../assets/images';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontStyles } from '../../theme/fonts';
import { gapStyles } from '../../theme/styles/commonMarginPadding';
import { borderWidthStyles } from '../../theme/styles/globalStyles';
import { PrimaryBtn } from '../Buttons/Btns';
import { CommonCard } from '../Card/Card';
import WeeklyMealsItemCard from './WeeklyMealsItemCard';

const WeeklyMeals = () => {
  const colors = useTheme();
  const { weeklyRecipes } = useSelector((state: RootState) => state.recipe);
  const { lanKey } = useSelector((state: RootState) => state.app);

  return (
    <CommonCard style={gapStyles.gap_16}>
      <View style={gapStyles.gap_8}>
        <Text
          style={[
            fontStyles.Maison_600_20PX_28LH,
            { color: colors?.primary_spinach },
          ]}
        >
          This weeks meals
        </Text>
        <Text
          style={[fontStyles.Maison_400_14PX_18LH, { color: colors?.grey_800 }]}
        >
          Meals curated to match your preferences
        </Text>
      </View>
      <ScrollView horizontal contentContainerStyle={gapStyles.gap_12}>
        {weeklyRecipes?.meal?.slice(0, 3)?.map((item) => {
          return (
            <WeeklyMealsItemCard
              key={item?.recipe_id + item?.dish_name}
              kcal={item?.variants?.[0]?.kcal + 'g'}
              name={item?.dish_name_tl?.[lanKey] || item?.dish_name}
              star={item?.total_ratings}
              iamge={
                item?.website_image?.[0]
                  ? { uri: item?.website_image?.[0] }
                  : BuildPlanImg
              }
            />
          );
        })}

        {weeklyRecipes?.snack?.slice(0, 1)?.map((item) => {
          return (
            <WeeklyMealsItemCard
              key={item?.recipe_id + item?.dish_name}
              kcal={item?.variants?.[0]?.kcal + 'g'}
              name={item?.dish_name_tl?.[lanKey] || item?.dish_name}
              star={item?.total_ratings}
              iamge={BuildPlanImg}
            />
          );
        })}
        {weeklyRecipes?.breakfast?.slice(0, 1)?.map((item) => {
          return (
            <WeeklyMealsItemCard
              key={item?.recipe_id + item?.dish_name}
              kcal={item?.variants?.[0]?.kcal + 'g'}
              name={item?.dish_name_tl?.[lanKey] || item?.dish_name}
              star={item?.total_ratings}
              iamge={BuildPlanImg}
            />
          );
        })}
      </ScrollView>
      <PrimaryBtn
        text="Configure my plan"
        style={[
          borderWidthStyles.bw2,
          {
            backgroundColor: colors?.neutral_white,
            borderColor: colors?.primary_spinach,
          },
        ]}
        textStyle={[{ color: colors?.primary_spinach }]}
      />
    </CommonCard>
  );
};

export default WeeklyMeals;
