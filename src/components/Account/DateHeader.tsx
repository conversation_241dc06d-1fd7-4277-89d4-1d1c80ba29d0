import moment from 'moment';
import { Text, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { LeftArrow, RightArrow } from '../../assets/images';
import { setSelectedDate } from '../../redux/slices/deliverySlice';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontStyles, RFont } from '../../theme/fonts';
import {
  gapStyles,
  paddingStyles,
} from '../../theme/styles/commonMarginPadding';
import { boxStyle, globalStyles } from '../../theme/styles/globalStyles';
import { formatDateInDubai } from '../../utils/functions';

const DateHeader = () => {
  const colors = useTheme();
  const { authUser } = useSelector((state: RootState) => state.auth);
  const { selectedDate, deliveryList } = useSelector(
    (state: RootState) => state.delivery,
  );
  const dispatch = useDispatch();
  let index = deliveryList?.findIndex(
    (e) => moment(e.delivery_date).format('YYYY-MM-DD') === selectedDate,
  );

  return (
    <View
      style={[
        boxStyle.shadow,
        paddingStyles.px20,
        paddingStyles.py12,
        globalStyles.flexDirectionRow,
        globalStyles.alignItemsCenter,
        globalStyles.justifyContentCenter,
        gapStyles.gap_36,
        {
          backgroundColor: colors?.primary_cream,
        },
      ]}
    >
      {moment(selectedDate).isAfter(moment(deliveryList[0]?.delivery_date)) ? (
        <LeftArrow
          onPress={() => {
            dispatch(
              setSelectedDate(
                moment(deliveryList[index - 1].delivery_date).format(
                  'YYYY-MM-DD',
                ),
              ),
            );
          }}
          width={RFont(24)}
          height={RFont(24)}
        />
      ) : (
        <View style={{ width: RFont(24) }} />
      )}
      <Text
        style={[
          fontStyles.Maison_600_18PX_24LH,
          globalStyles.textAlignCenter,
          { color: colors?.black, minWidth: RFont(150) },
        ]}
      >
        {formatDateInDubai(selectedDate)}
      </Text>
      {moment(selectedDate).isBefore(
        moment(deliveryList[deliveryList?.length - 1]?.delivery_date).subtract(
          1,
          'day',
        ),
      ) ? (
        <RightArrow
          onPress={() => {
            if (authUser?._id) {
              dispatch(
                setSelectedDate(
                  moment(deliveryList[index + 1].delivery_date).format(
                    'YYYY-MM-DD',
                  ),
                ),
              );
            }
          }}
          width={RFont(24)}
          height={RFont(24)}
        />
      ) : (
        <View style={{ width: RFont(24) }} />
      )}
    </View>
  );
};
export default DateHeader;
