import { FC } from 'react';
import { Text, View } from 'react-native';
import { useSelector } from 'react-redux';
import { Success } from '../../assets/images';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontStyles, RFont } from '../../theme/fonts';
import { gapStyles } from '../../theme/styles/commonMarginPadding';
import { globalStyles } from '../../theme/styles/globalStyles';
import { CommonCard } from '../Card/Card';
interface TickListTextProps {
  title: string;
}
export const TickListText: FC<TickListTextProps> = ({ title }) => {
  const colors = useTheme();
  return (
    <View
      style={[
        gapStyles.gap_12,
        globalStyles.flexDirectionRow,
        globalStyles.alignItemsFlexStart,
      ]}
    >
      <Success width={RFont(16)} height={RFont(16)} />
      <Text
        style={[
          fontStyles.Maison_400_14PX_18LH,
          { color: colors?.grays_black },
        ]}
      >
        {title}
      </Text>
    </View>
  );
};
export const DelicutBenefits = () => {
  const colors = useTheme();
  const { translation } = useSelector((state: RootState) => state.auth);
  return (
    <CommonCard style={gapStyles.gap_16}>
      <Text
        style={[
          fontStyles.Maison_600_18PX_24LH,
          { color: colors?.primary_spinach },
        ]}
      >
        {translation?.BENEFITS_CARD_TITLE}
      </Text>
      <View style={gapStyles.gap_12}>
        {translation?.BENEFITS_LIST?.split('\n')?.map((e) => {
          return <TickListText title={e} key={e} />;
        })}
      </View>
    </CommonCard>
  );
};
export const DelicutExpectNext = () => {
  const colors = useTheme();
  const { translation } = useSelector((state: RootState) => state.auth);
  return (
    translation?.EXPECT_NEXT_LIST[0] !== ' ' && (
      <CommonCard style={gapStyles.gap_16}>
        <Text
          style={[
            fontStyles.Maison_600_18PX_24LH,
            { color: colors?.primary_spinach },
          ]}
        >
          {translation?.EXPECT_NEXT_CARD_TITLE}
        </Text>
        <View style={gapStyles.gap_12}>
          {translation?.EXPECT_NEXT_LIST?.split('\n')?.map((e) => {
            return <TickListText title={e} key={e} />;
          })}
        </View>
      </CommonCard>
    )
  );
};
