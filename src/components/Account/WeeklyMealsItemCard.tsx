import { FC } from 'react';
import {
  Image,
  ImageSourcePropType,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import { StarRatingDisplay } from 'react-native-star-rating-widget';
import { useTheme } from '../../theme';
import { fontStyles, RFont } from '../../theme/fonts';
import { gapStyles } from '../../theme/styles/commonMarginPadding';
import { globalStyles } from '../../theme/styles/globalStyles';
import CommonBadges from '../Badges/Badges';
interface weeklyMealsItemCardProps {
  name: string;
  kcal: string;
  star: string;
  iamge?: ImageSourcePropType;
}

const WeeklyMealsItemCard: FC<weeklyMealsItemCardProps> = ({
  kcal,
  name,
  star,
  iamge,
}) => {
  const colors = useTheme();
  return (
    <View style={style.wrapper}>
      {star && (
        <CommonBadges
          badge={star}
          leftIcon={
            <StarRatingDisplay
              starSize={RFont(16)}
              rating={1}
              maxStars={1}
              color="#EBAB21"
            />
          }
          textStyle={[{ color: colors?.primary_spinach }]}
          style={[
            style.badge,
            {
              backgroundColor: colors?.primary_cream,
            },
          ]}
        />
      )}
      <Image source={iamge} style={style.image} />
      <View style={gapStyles.gap_4}>
        <Text
          style={[
            fontStyles.Maison_600_16PX_20LH,
            { color: colors?.black_900 },
          ]}
        >
          {name}
        </Text>
        <View
          style={[
            globalStyles.flexDirectionRow,
            gapStyles.gap_6,
            globalStyles.alignItemsCenter,
          ]}
        >
          <Text
            style={[
              fontStyles.Maison_600_14PX_16_8LH,
              { color: colors?.primary_spinach },
            ]}
          >
            {kcal}
          </Text>
          <Text
            style={[
              fontStyles.Maison_400_12PX_16LH,
              { color: colors?.black_900 },
            ]}
          >
            kcal
          </Text>
        </View>
      </View>
    </View>
  );
};
const style = StyleSheet.create({
  badge: {
    alignItems: 'center',
    borderRadius: RFont(20),
    flexDirection: 'row',
    gap: RFont(2),
    left: -RFont(10),
    paddingHorizontal: RFont(16),
    position: 'absolute',
    top: RFont(6),
    zIndex: 100,
  },
  image: {
    height: RFont(130),
    resizeMode: 'cover',
    width: RFont(130),
  },
  wrapper: {
    gap: RFont(12),
    maxWidth: RFont(154),
    overflow: 'hidden',
    padding: RFont(12),
  },
});
export default WeeklyMealsItemCard;
