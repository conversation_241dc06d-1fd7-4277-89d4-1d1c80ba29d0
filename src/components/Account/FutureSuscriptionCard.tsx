import moment from 'moment';
import { Text, View } from 'react-native';
import { useSelector } from 'react-redux';
import { CommonCard } from '../../components/Card/Card';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontStyles } from '../../theme/fonts';
import { cardStyles } from '../../theme/styles/cardStyles';
import {
  gapStyles,
  marginStyles,
  paddingStyles,
} from '../../theme/styles/commonMarginPadding';
import {
  borderRadiusStyles,
  borderWidthStyles,
  globalStyles,
} from '../../theme/styles/globalStyles';
import { TitleCase } from '../../utils/functions';
const FutureSuscriptionCard = () => {
  const { subscriptionDetails } = useSelector(
    (state: RootState) => state.subscription,
  );
  const colors = useTheme();
  const sub_item = subscriptionDetails?.[0];
  const duration =
    parseInt(sub_item?.plan_duration_in_days || '1') /
    parseInt(sub_item?.delivery_days || '1');
  return (
    sub_item && (
      <CommonCard
        title="Your plan is confirmed!"
        titleStyle={[
          fontStyles.Maison_600_20PX_28LH,
          { color: colors?.primary_spinach },
        ]}
        style={[gapStyles.gap_8, paddingStyles.px16]}
      >
        <Text
          style={[fontStyles.Maison_400_14PX_18LH, { color: colors?.grey_800 }]}
        >
          Your meals start from{' '}
          {moment(subscriptionDetails?.[0]?.delivery_start_date).format(
            'DD MMM YYYY',
          )}
          , we will notify you once it is ready.
        </Text>
        <View
          style={[
            cardStyles.cardR16_P16_BoxShadow1,
            borderWidthStyles.bw1,
            paddingStyles.px12,
            gapStyles.gap_4,
            marginStyles.mt_8,
            { borderColor: colors?.secondary_warm_grey },
          ]}
        >
          <Text
            style={[
              fontStyles.Maison_600_18PX_24LH,
              { color: colors?.primary_grenade },
            ]}
          >
            {
              subscriptionDetails?.[0]?.selected_meal_type?.[0]
                ?.protein_category
            }
          </Text>
          <View
            style={[
              globalStyles.flexDirectionRow,
              gapStyles.gap_6,
              globalStyles.alignItemsCenter,
            ]}
          >
            <Text
              style={[
                fontStyles.Maison_400_14PX_18LH,
                { color: colors?.grey_800 },
              ]}
            >
              {`${duration} ${duration > 1 ? 'weeks' : 'week'}`}
            </Text>
            <View
              style={[
                paddingStyles.p4,
                borderRadiusStyles.br16,
                { backgroundColor: colors?.neutral_black },
              ]}
            />
            <Text
              style={[
                fontStyles.Maison_400_14PX_18LH,
                { color: colors?.grey_800 },
              ]}
            >
              {subscriptionDetails?.[0]?.selected_meal
                .map((e) => TitleCase(e))
                .join(', ')}
            </Text>
          </View>
        </View>
        <Text
          style={[
            fontStyles.Maison_600_18PX_21_6LH,
            globalStyles.textAlignCenter,
            marginStyles.mt_8,
            { color: colors?.primary_grenade },
          ]}
        >
          View my deliveries
        </Text>
      </CommonCard>
    )
  );
};

export default FutureSuscriptionCard;
