/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-unused-vars */
import CleverTap from 'clevertap-react-native';
import React, { useEffect, useState } from 'react';
import { Dimensions, View } from 'react-native';
import { useSelector } from 'react-redux';
import { RootState } from '../../redux/store';
const { width } = Dimensions.get('window');

export const NativeDisplay = () => {
  const { isRTL } = useSelector((state: RootState) => state.app);
  const [nativeDisplayData, setNativeDisplayData] = useState([]);

  useEffect(() => {
    // Fetch Native Display Messages
    CleverTap.getAllDisplayUnits((err, res) => {
      if (err) {
        console.error('Error fetching Native Display messages:', err);
      } else {
        setNativeDisplayData(res as []);
      }
    });
  }, []);

  return (
    <View>
      {/* <Carousel
        autoPlayReverse={isRTL}
        renderItem={({ item }: any) => (
          <TouchableOpacity
            onPress={() => CleverTap.pushDisplayUnitClickedEventForID(item.key)}
          >
            <View style={{ marginVertical: RFValue(16) }}>
              <Image
                source={{ uri: item?.media?.url }}
                style={{
                  width: '100%',
                  height: RFValue(200),
                  borderRadius: RFValue(8),
                }}
              />
              <Text style={fontStyles.Maison_700_18PX_21LH}>
                {item?.title?.text}
              </Text>
              <Text>{item?.message?.text}</Text>
            </View>
          </TouchableOpacity>
        )}
        loop
        width={width * 0.9}
        height={300}
        autoPlay
        autoPlayInterval={3000}
        data={nativeDisplayData?.[0]?.content}
        scrollAnimationDuration={1000}
        // onSnapToItem={(index) => setActiveSlide(index)}
      /> */}
      {/* Simple Message */}

      {/* <FlatList
        data={nativeDisplayData?.[0]?.content}
        keyExtractor={(item) => item.key}
        renderItem={({ item }) => (
          <TouchableOpacity
            onPress={() =>
              CleverTap.pushDisplayUnitViewedEventForID(item.wzrk_id)
            }
          >
            <View style={{ marginVertical: RFValue(16) }}>
              <Image
                source={{ uri: item?.media?.url }}
                style={{
                  width: '100%',
                  height: RFValue(200),
                  borderRadius: RFValue(8),
                }}
              />
              <Text style={fontStyles.Maison_700_18PX_21LH}>
                {item?.title?.text}
              </Text>
              <Text>{item?.message?.text}</Text>
            </View>
          </TouchableOpacity>
        )}
      /> */}
    </View>
  );
};
