/* eslint-disable react-native/no-inline-styles */
import React from 'react';
import {
  BaseToastProps,
  ErrorToast,
  SuccessToast,
} from 'react-native-toast-message';
import { useTheme } from '../../theme';
import { fontStyles } from '../../theme/fonts';

export const SuccessToastView = (
  props: React.JSX.IntrinsicAttributes & BaseToastProps,
) => {
  const colors = useTheme();
  return (
    <SuccessToast
      {...props}
      style={{
        bottom: 0,
        backgroundColor: colors?.secondary_berry,
        borderColor: colors?.primary_cream,
        width: '90%',
      }}
      text1Style={[
        fontStyles.Maison_400_12PX_16LH,
        { color: colors?.neutral_white },
      ]}
      text1NumberOfLines={10}
      text2NumberOfLines={0}
    />
  );
};
export const ErrorToastView = (
  props: React.JSX.IntrinsicAttributes & BaseToastProps,
) => {
  const colors = useTheme();
  return (
    <ErrorToast
      {...props}
      style={{
        backgroundColor: colors?.red_500,
        borderColor: colors?.warningOrange,
        width: '90%',
      }}
      text1Style={[
        fontStyles.Maison_400_12PX_16LH,
        { color: colors?.neutral_white },
      ]}
      text1NumberOfLines={10}
      text2NumberOfLines={0}
    />
  );
};
