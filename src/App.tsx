// import CleverTap from 'clevertap-react-native';
import React, { useEffect } from 'react';
import 'react-native-gesture-handler';
import 'react-native-reanimated';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { enableScreens } from 'react-native-screens';
import SplashScreen from 'react-native-splash-screen';
import Toast from 'react-native-toast-message';
import { Provider } from 'react-redux';
import ErrorWrapper from './components/ErrorBoundary';
import AppNavigator from './navigation/AppNavigator';
import store from './redux/store';

enableScreens();
// CleverTap.setDebugLevel(2);
// CleverTap.registerForPush();
// CleverTap.enablePersonalization();

const App = () => {
  useEffect(() => {
    setTimeout(() => {
      SplashScreen.hide();
    }, 2000);
    // CleverTap.setInstanceWithAccountId(Config.CLEVERTAP_ACCOUNT_ID as string);
    // CleverTap.enableDeviceNetworkInfoReporting(true);
  }, []);

  return (
    <SafeAreaProvider>
      <ErrorWrapper>
        <Provider store={store}>
          {/* <GoogleOAuthProvider clientId={Config.GOOGLE_CLIENT_ID as string}> */}
          <Toast />
          <AppNavigator />
          {/* </GoogleOAuthProvider> */}
        </Provider>
      </ErrorWrapper>
    </SafeAreaProvider>
  );
};

export default App;
