import React, { useEffect } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';

export const ExcludeIngredientsProvider: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  const { authUser } = useSelector((state: RootState) => state?.auth);
  const { subscriptionDetails } = useSelector(
    (state: RootState) => state?.subscription,
  );
  const formMethods = useForm({
    defaultValues: {
      customer_id: '',
      avoid_category: [] as string[],
      avoid_ingredients: [] as string[],
      is_vegetarian: false,
      new_selection: true,
    },
  });
  useEffect(() => {
    if (subscriptionDetails?.[0] && authUser)
      formMethods.reset({
        customer_id: '',
        avoid_category: subscriptionDetails?.[0]?.avoid_category,
        avoid_ingredients: subscriptionDetails?.[0]?.avoid_ingredients,
        is_vegetarian: subscriptionDetails?.[0]?.is_vegetarian || false,
        new_selection: true,
      });
  }, [authUser, formMethods, subscriptionDetails]);
  return <FormProvider {...formMethods}>{children}</FormProvider>;
};
