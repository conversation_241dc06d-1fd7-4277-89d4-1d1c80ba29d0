import React, { useEffect } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';
export type MealplanFormFormValue = {
  protein_category: string;
  avoid_category: string[];
  dayArray: number[];
  no_of_meals: number;
  no_of_breakfast: number;
  no_of_snacks: number;
  is_vegetarian: boolean;
  kcal_range: string;
  kcal: string;
  avoid_ingredients: string[];
  selected_meal_type: string[];
  plan_duration_in_days: number;
  per_day_price: string;
  delivery_days: string;
  qty: number;
  price: number;
};

export const MealplanFormProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const { cartDetails } = useSelector((state: RootState) => state?.cart);
  const cartItem = cartDetails?.cart_item?.[0];
  const formMethods = useForm({
    defaultValues: {
      protein_category: cartItem?.protein_category || 'balance',
      avoid_category: cartItem?.avoid_category || [],
      dayArray: [],
      no_of_meals: cartItem?.no_of_meals || 1,
      no_of_breakfast: cartItem?.no_of_breakfast || 0,
      no_of_snacks: cartItem?.no_of_snacks || 0,
      is_vegetarian: cartItem?.is_vegetarian || false,
      kcal_range: cartItem?.selected_meal_type
        ? cartItem?.selected_meal_type?.[0]?.kcal_range
        : 'Medium',
      kcal: cartItem?.selected_meal_type
        ? cartItem?.selected_meal_type?.[0]?.kcal
        : '',
      avoid_ingredients: cartItem?.avoid_ingredients || [],
      selected_meal_type: cartItem?.selected_meal
        ? cartItem?.selected_meal
        : ['lunch'],
      plan_duration_in_days: parseInt(
        cartItem?.plan_duration_in_days?.toString() || '20',
      ),
      per_day_price: '',
      delivery_days: '5',
      qty: 1,
      price: 0,
    },
    mode: 'all',
    reValidateMode: 'onChange',
    shouldUseNativeValidation: true,
  });
  useEffect(() => {
    formMethods.reset({
      protein_category: cartItem?.protein_category || 'balance',
      avoid_category: cartItem?.avoid_category || [],
      no_of_meals: cartItem?.no_of_meals || 1,
      no_of_breakfast: cartItem?.no_of_breakfast || 0,
      no_of_snacks: cartItem?.no_of_snacks || 0,
      is_vegetarian: cartItem?.is_vegetarian || false,
      kcal_range: cartItem?.selected_meal_type
        ? cartItem?.selected_meal_type?.[0]?.kcal_range
        : 'Medium',
      kcal: cartItem?.selected_meal_type
        ? cartItem?.selected_meal_type?.[0]?.kcal
        : '',
      avoid_ingredients: cartItem?.avoid_ingredients || [],
      selected_meal_type: cartItem?.selected_meal
        ? cartItem?.selected_meal
        : ['lunch'],
      plan_duration_in_days: parseInt(
        cartItem?.plan_duration_in_days?.toString() || '20',
      ),
      per_day_price: cartItem?.per_day_price || '',
      delivery_days: cartItem?.delivery_days?.toString() || '5',
      qty: 1,
      price: parseFloat(cartItem?.price || '0'),
      dayArray: formMethods.watch('dayArray') || [],
    });
  }, [cartItem, formMethods]);
  return <FormProvider {...formMethods}>{children}</FormProvider>;
};
