import { yupResolver } from '@hookform/resolvers/yup';
import { addUserProflieProps } from 'actions';
import parsePhoneNumberFromString from 'libphonenumber-js';
import React, { useEffect } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useSelector } from 'react-redux';
import * as yup from 'yup';
import { RootState } from '../redux/store';
import {
  AlphaNumericWithPunctuation,
  DigitsOnly9LengthRegex,
} from '../utils/regexMatch';
const profileSchma = yup.object().shape({
  first_name: yup
    .string()
    .required('Required')
    .matches(AlphaNumericWithPunctuation, 'Please enter text in English only'),
  last_name: yup
    .string()
    .required('Required')
    .matches(AlphaNumericWithPunctuation, 'Please enter text in English only'),
  email: yup
    .string()
    .email('Not Valid Email!')
    .required('Required')
    .matches(AlphaNumericWithPunctuation, 'Please enter text in English only'),
  whatsapp_number: yup
    .string()
    .required('Required')
    .matches(AlphaNumericWithPunctuation, 'Please enter text in English only')
    .test(
      'whatsapp_number',
      'Please enter a valid mobile number.',
      (value, { parent }) => {
        if (!value) return true;
        try {
          const parsedPhoneNumber = parsePhoneNumberFromString(value, {
            defaultCallingCode: parent?.whatsapp_country_code?.slice(1),
          });
          return parsedPhoneNumber?.isValid() || false;
        } catch (error) {
          return false;
        }
      },
    ),
  whatsapp_country_code: yup.string().required('Required'),
  country_code: yup.string().required('Required'),
  phone_number: yup
    .string()
    .required('Required')
    .matches(AlphaNumericWithPunctuation, 'Please enter text in English only')
    .matches(DigitsOnly9LengthRegex, 'Mobile number is not valid'),
});
export const UserDetailsProvider: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  const { authUser } = useSelector((state: RootState) => state?.auth);
  const formMethods = useForm<addUserProflieProps>({
    defaultValues: {
      first_name: '',
      last_name: '',
      email: '',
      phone_number: '',
      country_code: '',
      whatsapp_country_code: '',
      whatsapp_number: '',
    },
    resolver: yupResolver(profileSchma),
    reValidateMode: 'onChange',
  });
  useEffect(() => {
    formMethods.reset({
      first_name: authUser?.first_name || '',
      last_name: authUser?.last_name || '',
      email: authUser?.email || '',
      phone_number: authUser?.phone_number || '',
      whatsapp_number:
        authUser?.whatsapp_number || authUser?.phone_number || '',
      country_code: authUser?.country_code || '+971',
      whatsapp_country_code:
        authUser?.whatsapp_country_code || authUser?.country_code || '+971',
    });
  }, [authUser, formMethods]);
  return <FormProvider {...formMethods}>{children}</FormProvider>;
};
