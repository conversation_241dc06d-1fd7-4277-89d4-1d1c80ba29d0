import { yupResolver } from '@hookform/resolvers/yup';
import React, { useEffect } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useSelector } from 'react-redux';
import * as yup from 'yup';
import { RootState } from '../redux/store';
import { AlphaNumericWithPunctuation } from '../utils/regexMatch';
export const AddressAllDayEditProvider: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  const { translation, authUser } = useSelector(
    (state: RootState) => state?.auth,
  );
  const { subscriptionDetails } = useSelector(
    (state: RootState) => state?.subscription,
  );
  const validationSchema = yup.object().shape({
    customer_id: yup.string().required(translation?.REQUIRED),
    type: yup.string().required(translation?.REQUIRED),
    address_id: yup.string().required(translation?.REQUIRED),
    slot: yup.string().required(translation?.REQUIRED),
    subscription_id: yup.string().required(translation?.REQUIRED),
    instruction: yup.array().of(yup.string()),
  });
  const formMethods = useForm({
    defaultValues: {
      customer_id: '',
      type: 'all',
      address_id: '',
      slot: '',
      subscription_id: '',
      instruction: [],
    },
    resolver: yupResolver(validationSchema),
  });
  useEffect(() => {
    if (subscriptionDetails?.[0] && authUser)
      formMethods.reset({
        customer_id: authUser?._id,
        address_id: subscriptionDetails?.[0]?.address_id?._id,
        slot: subscriptionDetails?.[0]?.slot,
        subscription_id: subscriptionDetails?.[0]?._id,
        instruction: subscriptionDetails?.[0]?.instruction,
        type: 'all',
      });
  }, [authUser, formMethods, subscriptionDetails]);
  return <FormProvider {...formMethods}>{children}</FormProvider>;
};
export const WeekAddressEditProvider: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  const { translation } = useSelector((state: RootState) => state?.auth);
  const weekSchema = yup.object().shape({
    customer_id: yup.string().required(translation?.REQUIRED),
    type: yup.string().required(translation?.REQUIRED),
    address_id: yup.string().required(translation?.REQUIRED),
    slot: yup.string().required(translation?.REQUIRED),
    start_from: yup.string().required(translation?.REQUIRED),
    week_day: yup.string().required(translation?.REQUIRED),
    instruction: yup.array().of(yup.string()),
  });
  const formMethods = useForm({
    defaultValues: {
      customer_id: '',
      type: 'week_day',
      start_from: '',
      address_id: '',
      slot: '',
      instruction: [],
      week_day: '',
    },
    resolver: yupResolver(weekSchema),
  });
  return <FormProvider {...formMethods}>{children}</FormProvider>;
};
export const AddEditAddressProvider: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  const { subscriptionDetails } = useSelector(
    (state: RootState) => state?.subscription,
  );
  const { translation, addressByID, authUser } = useSelector(
    (state: RootState) => state.auth,
  );
  const AddEditSchema = yup.object().shape({
    customer_id: yup.string().required(translation?.REQUIRED),
    address_type: yup.string().required(translation?.REQUIRED),
    city: yup.string().required(translation?.REQUIRED),
    province: yup.string().required(translation?.REQUIRED),
    country: yup.string().required(translation?.REQUIRED),
    full_address: yup
      .string()
      .required(translation?.REQUIRED)
      .matches(
        AlphaNumericWithPunctuation,
        'Please enter text in English only',
      ),
    instruction: yup.array().of(yup.string()),
  });
  const formMethods = useForm({
    defaultValues: {
      address_type: '',
      city: '',
      province: '',
      country: 'United Arab Emirates',
      full_address: '',
      customer_id: '',
      instruction: [],
    },
    resolver: yupResolver(AddEditSchema),
  });
  useEffect(() => {
    if (authUser?._id)
      formMethods.reset({
        address_type: addressByID?.address_type || '',
        city: addressByID?.city || '',
        province: addressByID?.province || '',
        country: 'United Arab Emirates',
        full_address: addressByID?.full_address || '',
        customer_id: authUser?._id,
        instruction: subscriptionDetails?.[0]?.instruction,
      });
  }, [addressByID, formMethods, authUser?._id, subscriptionDetails]);
  return <FormProvider {...formMethods}>{children}</FormProvider>;
};
