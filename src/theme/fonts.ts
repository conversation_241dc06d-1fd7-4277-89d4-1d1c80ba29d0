import { PixelRatio, StyleSheet } from 'react-native';
import { RFValue } from 'react-native-responsive-fontsize';

export const fontFamily = {
  MaisonBold: 'Maison-Bold',
  MaisonDemi: '<PERSON><PERSON>-Demi',
  MaisonLight: '<PERSON>son-Light',
  MaisonMedium: 'Maison-Medium',
  MaisonMonoBold: 'MaisonMono-Bold',
  MaisonMonoLight: 'MaisonMono-Light',
  MaisonRegular: 'Maison-Regular',
  MaisonRegularOblique: 'Maison-RegularOblique',
};
export const RFont = (n: number, standardScreenHeight = 1000) => {
  const scaleFactor = 1.3;
  const adjustedSize = RFValue(n, standardScreenHeight);
  return (adjustedSize * scaleFactor) / PixelRatio.getFontScale(); // Normalize font scaling
};

export const fontStyles = StyleSheet.create({
  Maison_400_12PX_14LH: {
    fontFamily: fontFamily.MaisonRegular,
    fontSize: RFValue(12),
    lineHeight: RFValue(14),
    verticalAlign: 'auto',
  },
  Maison_400_12PX_16LH: {
    fontFamily: fontFamily.MaisonRegular,
    fontSize: RFont(12),
    lineHeight: RFont(16),
    verticalAlign: 'auto',
  },
  Maison_400_13PX_15_6LH: {
    fontFamily: fontFamily.MaisonRegular,
    fontSize: RFont(13),
    letterSpacing: RFont(0.01 * 13),
    lineHeight: RFont(15.6),
    verticalAlign: 'auto',
  },
  Maison_400_14PX_16LH: {
    fontFamily: fontFamily.MaisonRegular,
    fontSize: RFont(14),
    lineHeight: RFont(16),
    verticalAlign: 'auto',
  },
  Maison_400_14PX_18LH: {
    fontFamily: fontFamily.MaisonRegular,
    fontSize: RFont(14),
    lineHeight: RFont(18),
    verticalAlign: 'auto',
  },
  Maison_400_16PX_20LH: {
    fontFamily: fontFamily.MaisonRegular,
    fontSize: RFont(16),
    lineHeight: RFont(20),
    verticalAlign: 'auto',
  },
  Maison_400_16PX_22LH: {
    fontFamily: fontFamily.MaisonRegular,
    fontSize: RFont(16),
    letterSpacing: RFont(0.01 * 16),
    lineHeight: RFont(22),
    verticalAlign: 'auto',
  },
  Maison_400_18PX_22LH: {
    fontFamily: fontFamily.MaisonRegular,
    fontSize: RFont(18),
    lineHeight: RFont(22),
    verticalAlign: 'auto',
  },
  Maison_500_12PX_15_6LH: {
    fontFamily: fontFamily.MaisonMedium,
    fontSize: RFont(12),
    lineHeight: RFont(15.6),
    verticalAlign: 'auto',
  },
  Maison_500_12PX_16LH: {
    fontFamily: fontFamily.MaisonMedium,
    fontSize: RFont(12),
    lineHeight: RFont(16),
    verticalAlign: 'auto',
  },
  Maison_500_13PX_15_2LH: {
    fontFamily: fontFamily.MaisonMedium,
    fontSize: RFont(13),
    letterSpacing: -RFValue(0.01 * 13),
    lineHeight: RFont(15.2),
    verticalAlign: 'auto',
  },
  Maison_500_14PX_16_8LH: {
    fontFamily: fontFamily.MaisonMedium,
    fontSize: RFont(14),
    lineHeight: RFont(16.8),
    verticalAlign: 'auto',
  },
  Maison_500_14PX_18LH: {
    fontFamily: fontFamily.MaisonMedium,
    fontSize: RFont(14),
    lineHeight: RFont(18),
    verticalAlign: 'auto',
  },
  Maison_500_16PX_18LH: {
    fontFamily: fontFamily.MaisonMedium,
    fontSize: RFont(16),
    lineHeight: RFont(18),
    verticalAlign: 'auto',
  },
  Maison_500_16PX_19LH: {
    fontFamily: fontFamily.MaisonMedium,
    fontSize: RFont(16),
    lineHeight: RFont(19),
    verticalAlign: 'auto',
  },
  Maison_500_16PX_19_2LH: {
    fontFamily: fontFamily.MaisonMedium,
    fontSize: RFont(16),
    lineHeight: RFont(19.2),
    verticalAlign: 'auto',
  },
  Maison_500_16PX_20: {
    fontFamily: fontFamily.MaisonMedium,
    fontSize: RFont(16),
    letterSpacing: -RFont(0.01 * 16),
    lineHeight: RFont(20),
    verticalAlign: 'auto',
  },
  Maison_500_16PX_22LH: {
    fontFamily: fontFamily.MaisonMedium,
    fontSize: RFont(16),
    letterSpacing: -RFont(0.01 * 16),
    lineHeight: RFont(22),
    verticalAlign: 'auto',
  },
  Maison_500_18PX_21_6LH: {
    fontFamily: fontFamily.MaisonMedium,
    fontSize: RFont(18),
    lineHeight: RFont(21.6),
    verticalAlign: 'auto',
  },
  Maison_500_18PX_22LH: {
    fontFamily: fontFamily.MaisonMedium,
    fontSize: RFont(18),
    lineHeight: RFont(22),
    verticalAlign: 'auto',
  },
  Maison_500_18PX_24LH: {
    fontFamily: fontFamily.MaisonMedium,
    fontSize: RFValue(18),
    lineHeight: RFValue(24),
    verticalAlign: 'auto',
  },
  Maison_500_20PX_28LH: {
    fontFamily: fontFamily.MaisonMedium,
    fontSize: RFont(20),
    lineHeight: RFont(28),
    verticalAlign: 'auto',
  },
  Maison_600_12PX_18LH: {
    fontFamily: fontFamily.MaisonDemi,
    fontSize: RFont(12),
    lineHeight: RFont(18),
    verticalAlign: 'auto',
  },
  Maison_600_14PX_16_8LH: {
    fontFamily: fontFamily.MaisonDemi,
    fontSize: RFont(14),
    lineHeight: RFont(16.8),
    verticalAlign: 'auto',
  },

  Maison_600_14PX_18LH: {
    fontFamily: fontFamily.MaisonDemi,
    fontSize: RFont(14),
    lineHeight: RFont(18),
    verticalAlign: 'auto',
  },
  Maison_600_14PX_24LH: {
    fontFamily: fontFamily.MaisonDemi,
    fontSize: RFont(14),
    lineHeight: RFont(24),
    verticalAlign: 'auto',
  },
  Maison_600_15PX_18LH: {
    fontFamily: fontFamily.MaisonDemi,
    fontSize: RFont(15),
    lineHeight: RFont(18),
    verticalAlign: 'auto',
  },
  Maison_600_16PX_19_2LH: {
    fontFamily: fontFamily.MaisonDemi,
    fontSize: RFont(16),
    lineHeight: RFont(19.2),
    verticalAlign: 'auto',
  },
  Maison_600_16PX_20LH: {
    fontFamily: fontFamily.MaisonDemi,
    fontSize: RFont(16),
    lineHeight: RFont(20),
    verticalAlign: 'auto',
  },
  Maison_600_16PX_20_8LH: {
    fontFamily: fontFamily.MaisonDemi,
    fontSize: RFont(16),
    letterSpacing: -RFont(0.02 * 16),
    lineHeight: RFont(20.8),
    verticalAlign: 'auto',
  },
  Maison_600_16PX_22LH: {
    fontFamily: fontFamily.MaisonDemi,
    fontSize: RFont(16),
    lineHeight: RFont(22),
    verticalAlign: 'auto',
  },
  Maison_600_18PX_21_6LH: {
    fontFamily: fontFamily.MaisonDemi,
    fontSize: RFont(18),
    lineHeight: RFont(21.6),
    verticalAlign: 'auto',
  },
  Maison_600_18PX_22LH: {
    fontFamily: fontFamily.MaisonDemi,
    fontSize: RFont(18),
    letterSpacing: -RFont(0.01 * 18),
    lineHeight: RFont(22),
    verticalAlign: 'auto',
  },
  Maison_600_18PX_23_4LH: {
    fontFamily: fontFamily.MaisonDemi,
    fontSize: RFont(18),
    lineHeight: RFont(23.4),
    verticalAlign: 'auto',
  },
  Maison_600_18PX_24LH: {
    fontFamily: fontFamily.MaisonDemi,
    fontSize: RFont(18),
    lineHeight: RFont(24),
    verticalAlign: 'auto',
  },
  Maison_600_20PX_26LH: {
    fontFamily: fontFamily.MaisonDemi,
    fontSize: RFont(20),
    lineHeight: RFont(26),
    verticalAlign: 'auto',
  },
  Maison_600_20PX_28LH: {
    fontFamily: fontFamily.MaisonDemi,
    fontSize: RFont(20),
    lineHeight: RFont(28),
    verticalAlign: 'auto',
  },
  Maison_600_24PX_30LH: {
    fontFamily: fontFamily.MaisonDemi,
    fontSize: RFont(24),
    lineHeight: RFont(30),
    verticalAlign: 'auto',
  },
  Maison_600_24PX_32LH: {
    fontFamily: fontFamily.MaisonDemi,
    fontSize: RFont(24),
    lineHeight: RFont(32),
    verticalAlign: 'auto',
  },
  Maison_600_32PX_40LH: {
    fontFamily: fontFamily.MaisonDemi,
    fontSize: RFont(32),
    lineHeight: RFont(40),
    verticalAlign: 'auto',
  },
  Maison_600_48PX_53LH: {
    fontFamily: fontFamily.MaisonBold,
    fontSize: RFont(48),
    lineHeight: RFont(53),
    verticalAlign: 'auto',
  },
  Marison_600_12PX_14LH: {
    fontFamily: fontFamily.MaisonBold,
    fontSize: RFont(12),
    lineHeight: RFont(14),
    verticalAlign: 'auto',
  },
  Marison_600_12PX_16LH: {
    fontFamily: fontFamily.MaisonDemi,
    fontSize: RFont(12),
    lineHeight: RFont(16),
    verticalAlign: 'auto',
  },
  Marison_600_22PX_28LH: {
    fontFamily: fontFamily.MaisonBold,
    fontSize: RFont(22),
    lineHeight: RFont(28),
    verticalAlign: 'auto',
  },
  Marison_600_40PX_44LH: {
    fontFamily: fontFamily.MaisonBold,
    fontSize: RFont(40),
    lineHeight: RFont(54),
    verticalAlign: 'auto',
  },
});
