import { useEffect } from 'react';
import { Appearance } from 'react-native';
import { useDispatch } from 'react-redux';
import { setTheme } from '../redux/slices/themeSlice';

const useDeviceTheme = () => {
  const dispatch = useDispatch();

  useEffect(() => {
    const colorScheme = Appearance.getColorScheme();
    if (colorScheme) {
      dispatch(setTheme(colorScheme)); // Dispatch the theme action
    }

    const subscription = Appearance.addChangeListener(({ colorScheme }) => {
      if (colorScheme) {
        dispatch(setTheme(colorScheme)); // Dispatch the theme action on theme change
      }
    });

    return () => subscription.remove();
  }, [dispatch]);
};

export default useDeviceTheme;
