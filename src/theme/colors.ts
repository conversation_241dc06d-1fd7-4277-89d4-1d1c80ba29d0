import { ThemeProps } from '../../@types/theme';

export const lightTheme = {
  primary_grenade: '#FF3F1F',
  neutral_white: '#FFF',
  grey_900: '#19191A',
  primary_spinach: '#043F12',
  primary_spinach_10_p: '#043F121A',
  grey_600: '#62656A',
  secondary_curcuma: '#EF9700',
  secondary_avocado: '#6B8439',
  secondary_morning_sea: '#88ADAD',
  black: '#000000',
  black_900: '#1B1B1B',
  grey_800: '#313335',
  grey_100: '#E5E5E6',
  grey_200: '#CACCCE',
  grey_400: '#95989D',
  grey_700: '#4A4C4F',
  grey_7A7A7A: '#7A7A7A',
  grey_0000000F: '#0000000F',
  black_30_p: '#0000004D',
  black_50_p: '#00000080',
  grays_black: '#4A4C4F',
  AEC323: '#AEC323',
  DAEBB6: '#DAEBB6',
  neutral_black: '#121212',
  primary_cream: '#F9F4ED',
  secondary_warm_grey: '#DCD5D7',
  secondary_warm_grey_40_p: '#DCD5D766',
  secondary_berry: '#F5F5F5',
  green_477C7C: '#477C7C',
  red_500: '#FF0000',
  transparent: 'transparent',
  secondary_curry: '#D57903',
  red_gradient: ['#FFA5A54D', '#FF89894D'],
  yellow_gradient: ['#FFD9874D', '#FFEB574D'],
  red_600: '#CC0000',
  FFDDD199: '#FFDDD199',
  cyan_gradient: ['#40E0E04C', '#20C79F20'],
};
export const darkTheme = {
  primary_grenade: '#FF3F1F',
  neutral_white: '#FFF',
  grey_900: '#19191A',
  primary_spinach: '#043F12',
  primary_spinach_10_p: '#043F121A',
  grey_600: '#62656A',
  secondary_curcuma: '#EF9700',
  secondary_avocado: '#6B8439',
  black: '#000000',
  black_900: '#1B1B1B',
  grey_800: '#313335',
  grey_100: '#E5E5E6',
  grey_200: '#CACCCE',
  grey_400: '#95989D',
  grey_700: '#4A4C4F',
  setting_light_gray: '#00000080',
  grays_black: '#4A4C4F',
  AEC323: '#AEC323',
  DAEBB6: '#DAEBB6',
  neutral_black: '#121212',
  primary_cream: '#F9F4ED',
  secondary_warm_grey: '#DCD5D7',
  red_500: '#FF0000',
  secondary_curry: '#D57903',
  red_gradient: ['#FFA5A54D', '#FF89894D'],
  yellow_gradient: ['#FFD9874D', '#FFEB574D'],
  red_600: '#CC0000',
  FFDDD199: '#FFDDD199',
  cyan_gradient: ['#40E0E04C', '#20C79F20'],
} as const;

export const LightTheme: ThemeProps = {
  dark: false,
  colors: lightTheme,
  fonts: undefined,
};
