import { StyleSheet } from 'react-native';
import { colors } from 'theme-actions';
import { RFont } from '../fonts';

export const createRulerScreenStyles = (colors: colors) =>
  StyleSheet.create({
    container: {
      display: 'flex',
      flex: 1,
      height: '100%',
      justifyContent: 'flex-end',
      width: '100%',
    },
    indicatorLine: {
      backgroundColor: colors?.red_500 ?? '#FF0000',
      height: RFont(4),
      width: RFont(100),
      zIndex: 10,
    },
    rulerPrimaryBtn: {
      borderColor: colors?.grey_200 ?? '#ccc',
      borderRadius: RFont(32),
      borderWidth: 2,
      paddingHorizontal: RFont(16),
      paddingVertical: RFont(12),
    },
    tick: {
      backgroundColor: colors?.grey_200,
      height: 2,
    },
    tickMajor: {
      backgroundColor: colors?.grey_200,
      width: RFont(40),
    },
    tickMedium: {
      backgroundColor: colors?.grey_200,
      width: RFont(30),
    },
    tickMinor: {
      backgroundColor: colors?.grey_200,
      width: RFont(18),
    },
    tickRow: {
      alignItems: 'center',
      flexDirection: 'row',
      height: RFont(12),
      justifyContent: 'flex-end',
    },
    triangle: {
      borderBottomColor: colors?.primary_grenade,
      borderBottomWidth: 14,
      borderLeftColor: 'transparent',
      borderLeftWidth: 10,
      borderRightColor: 'transparent',
      borderRightWidth: 10,
      borderStyle: 'solid',
      height: 0,
      transform: [{ rotate: '-270deg' }],
      width: 0,
      zIndex: 11,
    },
  });
