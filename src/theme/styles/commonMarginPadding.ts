import { StyleSheet } from 'react-native';
import { RFont } from '../fonts';

export const marginStyles = StyleSheet.create({
  mb_12: {
    marginBottom: RFont(12),
  },
  mb_16: {
    marginBottom: RFont(16),
  },
  mb_20: {
    marginBottom: RFont(20),
  },
  mb_24: {
    marginBottom: RFont(24),
  },
  mb_90: {
    marginBottom: RFont(90),
  },
  ml_12: {
    marginLeft: RFont(12),
  },
  ml_16: {
    marginLeft: RFont(16),
  },
  ml_20: {
    marginLeft: RFont(20),
  },
  ml_30: {
    marginLeft: RFont(30),
  },
  ml_4: {
    marginLeft: RFont(4),
  },
  ml_8: {
    marginLeft: RFont(8),
  },
  mr_12: {
    marginRight: RFont(12),
  },
  mr_6: {
    marginRight: RFont(6),
  },
  mr_8: {
    marginRight: RFont(8),
  },
  mrn_10: {
    marginRight: RFont(-10),
  },
  mrn_20: {
    marginRight: RFont(-20),
  },
  mt_0: { marginTop: 0 },
  mt_10: {
    marginTop: RFont(10),
  },
  mt_12: {
    marginTop: RFont(12),
  },
  mt_16: {
    marginTop: RFont(16),
  },
  mt_2: {
    marginTop: RFont(2),
  },
  mt_20: {
    marginTop: RFont(20),
  },
  mt_24: {
    marginTop: RFont(24),
  },
  mt_32: {
    marginTop: RFont(32),
  },
  mt_4: {
    marginTop: RFont(4),
  },
  mt_6: {
    marginTop: RFont(6),
  },
  mt_8: {
    marginTop: RFont(8),
  },
  mt_auto: {
    marginTop: 'auto',
  },
  mtn_10: {
    marginTop: RFont(-10),
  },
  mx_16: {
    marginHorizontal: RFont(16),
  },
  mx_8: {
    marginHorizontal: RFont(8),
  },
  my_12: {
    marginVertical: RFont(12),
  },
  my_16: {
    marginVertical: RFont(16),
  },
  my_auto: {
    marginVertical: 'auto',
  },
});

export const gapStyles = StyleSheet.create({
  gap_10: {
    gap: RFont(10),
  },
  gap_12: {
    gap: RFont(12),
  },
  gap_16: {
    gap: RFont(16),
  },
  gap_2: {
    gap: RFont(2),
  },
  gap_20: {
    gap: RFont(20),
  },
  gap_24: {
    gap: RFont(24),
  },
  gap_32: {
    gap: RFont(32),
  },
  gap_36: {
    gap: RFont(36),
  },
  gap_4: {
    gap: RFont(4),
  },
  gap_6: {
    gap: RFont(6),
  },
  gap_8: {
    gap: RFont(8),
  },
});

export const paddingStyles = StyleSheet.create({
  p12: {
    padding: RFont(12),
  },
  p16: {
    padding: RFont(16),
  },
  p20: {
    padding: RFont(20),
  },
  p4: {
    padding: RFont(4),
  },
  p8: {
    padding: RFont(8),
  },
  pb12: {
    paddingBottom: RFont(12),
  },
  pb8: {
    paddingBottom: RFont(8),
  },
  pl130: {
    paddingLeft: RFont(130),
  },
  pt12: {
    paddingTop: RFont(12),
  },
  pt16: {
    paddingTop: RFont(16),
  },
  pt20: {
    paddingTop: RFont(20),
  },
  pt21: {
    paddingTop: RFont(21),
  },
  px0: {
    paddingHorizontal: RFont(0),
  },
  px12: {
    paddingHorizontal: RFont(12),
  },
  px14: {
    paddingHorizontal: RFont(14),
  },
  px16: {
    paddingHorizontal: RFont(16),
  },
  px20: {
    paddingHorizontal: RFont(20),
  },
  px24: {
    paddingHorizontal: RFont(24),
  },
  px6: {
    paddingHorizontal: RFont(6),
  },
  py0: {
    paddingVertical: RFont(0),
  },
  py12: {
    paddingVertical: RFont(12),
  },
  py16: {
    paddingVertical: RFont(16),
  },
  py2: {
    paddingVertical: RFont(2),
  },
  py20: {
    paddingVertical: RFont(20),
  },
  py24: {
    paddingVertical: RFont(24),
  },
  py32: {
    paddingVertical: RFont(32),
  },
  py6: {
    paddingVertical: RFont(6),
  },
  py8: {
    paddingVertical: RFont(8),
  },
});

export const leftStyles = StyleSheet.create({
  l_12: {
    left: RFont(12),
  },
});

export const rightStyles = StyleSheet.create({
  r_12: {
    right: RFont(12),
  },
});

export const topStyles = StyleSheet.create({
  t_12: {
    top: RFont(12),
  },
});
