import { Platform, StyleSheet, ViewStyle } from 'react-native';
import { RFValue } from 'react-native-responsive-fontsize';
import { lightTheme } from '../colors';

export type FormInputStylesType = {
  container: ViewStyle;
  dropdown: ViewStyle;
  dropdownIcon: ViewStyle;
  icon: ViewStyle;
  input: ViewStyle;
  multiselect: ViewStyle;
  primaryInput: ViewStyle;
  customInput: ViewStyle;
};

const obj: FormInputStylesType = {
  container: {
    alignItems: 'center',
    backgroundColor: lightTheme.neutral_white,
    borderRadius: RFValue(8),
    borderWidth: 1,
    paddingHorizontal: RFValue(16),
    paddingVertical: Platform.OS == 'ios' ? RFValue(10) : RFValue(4),
  },
  dropdown: {
    alignItems: 'center',
    backgroundColor: lightTheme.neutral_white,
    borderColor: lightTheme?.grey_100,
    borderRadius: RFValue(70),
    borderWidth: RFValue(1),
    paddingHorizontal: RFValue(20),
    paddingVertical: RFValue(14),
  },
  dropdownIcon: {
    height: RFValue(24),
    width: RFValue(24),
  },
  icon: {
    marginRight: RFValue(8),
  },
  input: {
    alignItems: 'center',
    borderRadius: RFValue(70),
    borderWidth: 1,
    flex: 1,
    paddingHorizontal: RFValue(20),
    paddingVertical: RFValue(14),
  },
  multiselect: {
    borderRadius: RFValue(8),
    borderWidth: 1,
    minHeight: RFValue(48),
    padding: RFValue(12),
  },
  primaryInput: {
    alignItems: 'center',
    backgroundColor: lightTheme.neutral_white,
    borderColor: lightTheme?.grey_100,
    borderRadius: RFValue(70),
    borderWidth: 1,
    paddingHorizontal: RFValue(20),
    paddingVertical: RFValue(16),
  },
  customInput: {
    borderRadius: RFValue(32),
    borderWidth: 1,
    borderColor: lightTheme?.grey_100,
    padding: RFValue(16),
    backgroundColor: lightTheme?.neutral_white,
    display: 'flex',
    alignItems: 'center',
    gap: RFValue(6),
  },
};

export const formInputStyles: FormInputStylesType = StyleSheet.create(obj);
