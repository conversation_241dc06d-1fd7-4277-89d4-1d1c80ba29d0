import { StyleSheet } from 'react-native';
import { lightTheme } from '../colors';
import { RFont } from '../fonts';

export const buildYourBgStyle = StyleSheet.create({
  background: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  container: {
    borderRadius: RFont(16),
    overflow: 'hidden',
  },
  image: {
    resizeMode: 'cover',
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0,0,0,0.3)',
  },
  title: {
    position: 'absolute',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 4,
  },
});

export const cardStyles = StyleSheet.create({
  cardR12_P10_BoxShadow1: {
    backgroundColor: lightTheme.primary,
    borderRadius: RFont(12),
    padding: RFont(8),
  },
  cardR16_P16_BoxShadow1: {
    backgroundColor: lightTheme.neutral_white,
    borderRadius: RFont(16),
    padding: RFont(16),
  },
  cardR20_P24_BoxShadow1: {
    backgroundColor: lightTheme.neutral_white,
    borderRadius: RFont(20),
    paddingHorizontal: RFont(12),
    paddingVertical: RFont(24),
  },
  cardR20_PT32_PB20_PX16: {
    backgroundColor: lightTheme.neutral_white,
    borderRadius: RFont(20),
    paddingBottom: RFont(20),
    paddingHorizontal: RFont(16),
    paddingTop: RFont(32),
  },
  cardR8_P16_Transprent: {
    backgroundColor: 'transparent',
    paddingHorizontal: RFont(16),
  },
});

export const macrosCardStyles = StyleSheet.create({
  card: {
    backgroundColor: lightTheme.neutral_white,
    borderRadius: RFont(8),
    gap: RFont(8),
    paddingHorizontal: RFont(16),
    paddingVertical: RFont(12),
  },
  indicatorCircle: {
    borderRadius: RFont(6),
    height: RFont(12),
    position: 'absolute',
    top: RFont(-5),
    transform: [{ translateX: '480%' }],
    width: RFont(12),
  },
  macrosView: {
    alignItems: 'center',
    flex: 1,
    flexDirection: 'row',
    gap: RFont(8),
  },
  macrosViewDetails: {
    flex: 1,
  },
  macrosViewIndicator: {
    borderRadius: RFont(2),
    height: RFont(2),
    position: 'relative',
    width: RFont(60),
  },
  macrosViewIndicatorWrapper: {
    alignItems: 'flex-start',
    flexDirection: 'column',
    gap: RFont(3),
    position: 'relative',
    width: '100%',
  },
  macrosViewWrapper: {
    gap: RFont(3),
  },
});

export const mealSelectionCardStyles = StyleSheet.create({
  discountBadge: {
    borderRadius: RFont(8),
    paddingHorizontal: RFont(10),
    paddingVertical: RFont(4),
  },
  selectAnyMealCard: {
    backgroundColor: 'black',
    borderRadius: RFont(8),
    paddingHorizontal: RFont(12),
    paddingVertical: RFont(8),
  },
  selectDurationBadge: {
    borderRadius: RFont(8),
    paddingHorizontal: RFont(8),
    paddingVertical: RFont(4),
  },
});

export const infoCardStyles = StyleSheet.create({
  card: {
    backgroundColor: lightTheme.secondary_berry,
    borderRadius: RFont(8),
    gap: RFont(8),
    padding: RFont(8),
  },
});
export const bottomFloatingCardStyles = StyleSheet.create({
  card: {
    backgroundColor: lightTheme.neutral_white,
    borderTopLeftRadius: RFont(20),
    borderTopRightRadius: RFont(20),
    bottom: 0,
    paddingBottom: RFont(12),
    paddingHorizontal: RFont(16),
    paddingTop: RFont(20),
    position: 'absolute',
    width: '100%',
  },
});
