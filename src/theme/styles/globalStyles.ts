import { StyleSheet } from 'react-native';
import { colors } from 'theme-actions';
import { RFont } from '../fonts';
export const globalStyles = StyleSheet.create({
  absolute: { position: 'absolute' },
  alignContentCenter: { alignContent: 'center' },
  alignItemsBaseline: { alignItems: 'baseline' },
  alignItemsCenter: { alignItems: 'center' },
  alignItemsFlexEnd: { alignItems: 'flex-end' },
  alignItemsFlexStart: { alignItems: 'flex-start' },
  alignSelfEnd: { alignSelf: 'flex-end' },
  alignSelfStart: { alignSelf: 'flex-start' },
  alignSelfStretch: { alignSelf: 'stretch' },
  border1: { borderWidth: 1 },
  border2: { borderWidth: 2 },
  capitalize: { textTransform: 'capitalize' },
  container: { flex: 1 },
  flex0: { flex: 0 },
  flex1: { flex: 1 },
  flexColumnReverse: { flexDirection: 'column-reverse' },
  flexDirectionColumn: { flexDirection: 'column' },
  flexDirectionRow: { flexDirection: 'row' },
  flexWrap: { flexWrap: 'wrap' },
  justifyContentCenter: { justifyContent: 'center' },
  justifyContentEnd: { justifyContent: 'flex-end' },
  justifyContentFlexStart: { justifyContent: 'flex-start' },
  justifyContentSpaceBetween: { justifyContent: 'space-between' },
  letterSpacingN1: { letterSpacing: -1 },
  letterSpacingN4: { letterSpacing: -4 },
  linethrough: { textDecorationLine: 'line-through' },
  maxHeight100: { maxHeight: '100%' },
  overflowHidden: { overflow: 'hidden' },
  overflowScroll: { overflow: 'scroll' },
  relative: { position: 'relative' },
  resizeModeContain: { resizeMode: 'contain' },
  row: { flexDirection: 'row' },
  rowReverse: { flexDirection: 'row-reverse' },
  safeAreaView: { flex: 1, padding: RFont(20), paddingBottom: RFont(32) },
  scrollView: { padding: RFont(20), paddingBottom: RFont(32) },
  textAlignCenter: { textAlign: 'center' },
  textDecorationLineThrough: { textDecorationLine: 'line-through' },
  textLeft: { textAlign: 'left' },
  textRight: { textAlign: 'right' },
  underline: { textDecorationLine: 'underline' },
  upperCase: { textTransform: 'uppercase' },
  verticalAlignTop: { verticalAlign: 'top' },
  writingDirectionLeft: { writingDirection: 'ltr' },
  writingDirectionRight: { writingDirection: 'rtl' },
});

export const heightStyles = StyleSheet.create({
  h100: {
    height: '100%',
  },
  h158: {
    height: RFont(158),
  },
  h20: {
    height: RFont(20),
  },
  h35: {
    height: RFont(35),
  },
  h40: {
    height: RFont(40),
  },
  h700: {
    height: RFont(700),
  },
  h8: {
    height: RFont(8),
  },
  mh580: {
    minHeight: RFont(580),
  },
});

export const widthStyles = StyleSheet.create({
  w100: {
    width: '100%',
  },
  w20: {
    width: RFont(20),
  },
  w8: {
    width: RFont(8),
  },
  w88: {
    width: RFont(88),
  },
});

export const borderRadiusStyles = StyleSheet.create({
  br12: {
    borderRadius: RFont(12),
  },
  br16: {
    borderRadius: RFont(16),
  },
  br20: {
    borderRadius: RFont(20),
  },
  br32: {
    borderRadius: RFont(32),
  },
  br6: {
    borderRadius: RFont(6),
  },
  br666: { borderRadius: RFont(666) },
  bt20: {
    borderTopLeftRadius: RFont(20),
    borderTopRightRadius: RFont(20),
  },
});

export const borderWidthStyles = StyleSheet.create({
  bbw1: {
    borderBottomWidth: RFont(1),
  },
  bw1: {
    borderWidth: RFont(1),
  },
  bw2: {
    borderWidth: RFont(2),
  },
});
export const opacityStyles = StyleSheet.create({
  opacity_0_2: { opacity: 0.2 },
  opacity_0_67: { opacity: 0.67 },
});

export const createCloseIconStyle = (colors: colors) =>
  StyleSheet.create({
    icon: {
      alignItems: 'center',
      backgroundColor: colors?.grey_100,
      borderRadius: RFont(36),
      height: RFont(24),
      justifyContent: 'center',
      position: 'absolute',
      right: RFont(16),
      top: RFont(12),
      width: RFont(24),
      zIndex: 9999,
    },
  });

export const boxStyle = StyleSheet.create({
  shadow: {
    elevation: RFont(4),
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: RFont(4) },
    shadowOpacity: 0.05,
    shadowRadius: RFont(4),
  },
});
