import { StyleSheet } from 'react-native';
import { lightTheme } from '../colors';
import { RFont } from '../fonts';

export const buttonStyles = StyleSheet.create({
  BorderBtn: {
    alignItems: 'center',
    backgroundColor: 'transparent',
    borderColor: lightTheme?.grey_100,
    borderRadius: 16,
    borderWidth: RFont(1),
    justifyContent: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  BorderLessBtn: {
    alignItems: 'center',
    backgroundColor: 'transparent',
    borderColor: 'transparent',
    justifyContent: 'center',
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  Checkbox: {
    borderRadius: RFont(100),
    borderWidth: RFont(1),
    height: RFont(20),
    width: RFont(20),
  },
  FeedbackCheckbox: {
    borderRadius: RFont(8),
    borderWidth: RFont(1),
    height: RFont(24),
    width: RFont(24),
  },
  PrimaryBtn: {
    alignItems: 'center',
    borderRadius: RFont(60),
    gap: RFont(12),
    justifyContent: 'center',
    paddingHorizontal: RFont(32),
    paddingVertical: RFont(16),
  },
  PrimarySelectablePill: {
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: RFont(32),
    borderWidth: RFont(1),
    gap: RFont(8),
    justifyContent: 'center',
    paddingHorizontal: RFont(16),
    paddingVertical: RFont(12),
  },
  Secondary: {
    alignItems: 'center',
    backgroundColor: 'transparent',
    borderColor: '#FF3F1F',
    borderRadius: RFont(60),
    borderWidth: 2,
    gap: RFont(12),
    justifyContent: 'center',
    paddingHorizontal: 14,
    paddingVertical: 8,
  },
  SecondarySelectablePill: {
    alignItems: 'center',
    backgroundColor: lightTheme?.primary_spinach,
    borderColor: lightTheme?.primary_spinach,
    borderRadius: 12,
    borderWidth: RFont(1),
    justifyContent: 'center',
    paddingHorizontal: 14,
    paddingVertical: 8,
  },
});
