import { LinkingOptions, PathConfig } from '@react-navigation/native';
import queryString from 'query-string';
import { Linking } from 'react-native';
import { RootStackParamList, RootTabParamList } from '../../@types/navigation';
import { storeDataToAsyncStorage } from './functions';
export const linking: LinkingOptions<RootTabParamList> = {
  prefixes: [
    'https://delicut.ae/',
    'delicut://',
    'https://pre.delicut.click',
    'pre.delicut.click://',
  ],
  config: {
    screens: {
      Main: {
        screens: {},
      } as PathConfig<RootStackParamList>,
    },
  },
  async getInitialURL() {
    const url = await Linking.getInitialURL();
    if (url) {
      const parsed = queryString.parseUrl(url);
      if (
        parsed?.query?.referral_code &&
        typeof parsed?.query?.referral_code === 'string'
      ) {
        storeDataToAsyncStorage('referral_code', parsed?.query?.referral_code);
      }
    }
    return url;
  },
  subscribe(listener) {
    const linkingSubscription = Linking.addEventListener('url', ({ url }) => {
      listener(url);
    });
    return () => {
      linkingSubscription.remove();
    };
  },
};
