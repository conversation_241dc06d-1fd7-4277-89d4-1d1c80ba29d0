export const ShareMessage = `
Hi 👋🏼\n
Delicut is a game-changer for everyday meals.🍲✨\n
No cooking stress- just clean, daily meals.\n
Tap the link to place your order now and get 15% OFF on your first order.\n
Use code “DELICUT15”.
`;

export const typeToSequence = {
  breakfast: 1,
  lunch: 2,
  dinner: 3,
  morning_snack: 4,
  evening_snack: 5,
};
export const mealsList = [
  { value: 'lunch', lable: 'Lunch' },
  { value: 'dinner', lable: 'Dinner' },
  { value: 'breakfast', lable: 'Breakfast' },
  { value: 'morning_snack', lable: 'Morning Snack' },
  { value: 'evening_snack', lable: 'Evening Snack' },
];
export const avilableSlotsData = [
  { value: '3AM - 6AM', lable: '3AM - 6AM' },
  { value: '6AM - 9AM', lable: '6AM - 9AM' },
  { value: '9AM - 2PM', lable: '9AM - 2PM' },
  { value: '2PM - 6PM', lable: '2PM - 6PM' },
  { value: '6PM - 10PM', lable: '6PM - 10PM' },
];

export const preferedDietData = [
  {
    value: 'low',
    lable: 'Low Carb/High Protein',
    isVeg: false,
    desc: 'Promotes weight loss, stabilizes blood sugar, and boosts energy levels.',
    macrosData: [
      { title: 'PROTEIN', value: '20-30%' },
      { title: 'CARB', value: '35-40%' },
      { title: 'FAT', value: '30-40%' },
    ],
  },
  {
    value: 'balance',
    lable: 'Balanced',
    isVeg: false,
    desc: 'For overall health, provides nutrients & sustains energy.',
    macrosData: [
      { title: 'PROTEIN', value: '20-25%' },
      { title: 'CARB', value: '40-50%' },
      { title: 'FAT', value: '25-30%' },
    ],
  },
  {
    value: 'balance',
    lable: 'Vegetarian',
    isVeg: true,
    desc: 'Balanced nutrition promoting overall health & sustaining energy.',
    macrosData: [
      { title: 'PROTEIN', value: '20-25%' },
      { title: 'CARB', value: '40-50%' },
      { title: 'FAT', value: '25-30%' },
    ],
  },
];

export const SelectDurationPlanData = [
  {
    durationPlan: '1 Week',
    finalPrice: '642.00',
    durationText: 'for 5 days',
  },
  {
    durationPlan: '4 Week',
    discount: '20% off',
    originalPrice: '882.00',
    finalPrice: '642.00',
    durationText: 'for 20 days',
  },
  {
    durationPlan: '12 Week',
    discount: '30% off',
    originalPrice: '2514.00',
    finalPrice: '1,814.00',
    durationText: 'for 60 days',
  },
];

export const AddressTypes = ['Home', 'Office', 'Other'];

export const WhyDelicutData = [
  { title: 'Fresh, never frozen meals with top ingredients.' },
  { title: 'Curated meal plans designed by nutritionists. ' },
  { title: 'Ready in under 2 minutes. Just heat and eat.' },
  { title: 'Free doorstep delivery across all plans.' },
];

export const meal = ['lunch', 'dinner'];
export const snacks = ['morning_snack', 'evening_snack'];

export const macrosData = [
  { title: 'PROTEIN', value: '150 - 170g' },
  { title: 'CARB', value: '150 - 170g' },
  { title: 'FAT', value: '150 - 170g' },
];
export const steps = [
  {
    title: 'What’s your primary goal with Delicut?',
    options: [
      { label: 'I want to lose some weight', value: 'lose' },
      { label: 'I’m looking to build muscle', value: 'build' },
      { label: 'I just want to eat healthier', value: 'maintain' },
    ],
    name: 'goal',
  },
  {
    title: 'What’s your current activity level?',
    options: [
      { label: 'Sedentary', value: 'sedentary' },
      { label: 'Lightly active', value: 'lightly_active' },
      { label: 'Moderately active', value: 'moderately_active' },
      { label: 'Very active', value: 'very_active' },
      { label: 'Athlete Mode', value: 'athlete_mode' },
    ],
    name: 'activity',
  },
  {
    title: 'Select your biological gender',
    options: [
      { label: 'Male', value: 'male' },
      { label: 'Female', value: 'female' },
    ],
    name: 'gender',
  },
  {
    title: 'How tall are you?',
    name: 'height',
  },
  {
    title: 'What’s your current weight?',
    name: 'weight',
  },
  {
    name: 'birthdate',
    title: 'What is your birthdate?',
  },
];

export const rulerData = [
  { value: 'cm', label: 'cm' },
  { value: 'ft-in', label: 'ft/in' },
];

export const rulerDataWeight = [
  { value: 'kg', label: 'kg' },
  { value: 'lbs', label: 'lbs' },
];

export const   modules = ['steps', 'calories_burned', 'sleep', 'weight'];
