import { Platform } from 'react-native';

/**
 * Creates a date range for today (midnight to 23:59:59.999)
 * @returns Object with start and end dates
 */
export const getTodayDateRange = () => {
  const start = new Date();
  start.setHours(0, 0, 0, 0);

  const end = new Date();
  end.setHours(23, 59, 59, 999);

  return {
    start,
    end,
    startISOString: start.toISOString(),
    endISOString: end.toISOString(),
  };
};

/**
 * Handles errors consistently across health data operations
 * @param context The context where the error occurred (for logging)
 * @param error The error that was thrown
 */
export const handleHealthDataError = (
  context: string,
  error: unknown,
): string => {
  let errorMessage = 'Unknown error';

  if (error && typeof error === 'object' && 'message' in error) {
    errorMessage = String((error as { message?: unknown }).message);
  } else if (typeof error === 'string') {
    errorMessage = error;
  }

  console.error(`[${context}] Error:`, error);
  return errorMessage;
};

/**
 * Determines if the platform is iOS
 */
export const isIOS = Platform.OS === 'ios';

/**
 * Determines if the platform is Android
 */
export const isAndroid = Platform.OS === 'android';

/**
 * Sets a date to start of day (00:00:00.000)
 */
export const setToStartOfDay = (date: Date): Date => {
  const newDate = new Date(date);
  newDate.setHours(0, 0, 0, 0);
  return newDate;
};

/**
 * Sets a date to end of day (23:59:59.999)
 */
export const setToEndOfDay = (date: Date): Date => {
  const newDate = new Date(date);
  newDate.setHours(23, 59, 59, 999);
  return newDate;
};
