import { GoogleSignin } from '@react-native-google-signin/google-signin';
import { Alert, Platform } from 'react-native';
import { supabase } from '../screens/fitness/supabaseClient';

export function formatMinutesToHours(minutes: number) {
  const hrs = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return `${hrs}h ${mins > 0 ? `${mins}m` : ''}`;
}

export async function getStepsDataFromDb(
  userId: string,
  start: string,
  end: string,
) {
  console.log(start, 'start');
  console.log(end, 'end');
  const { data, error } = await supabase.rpc('get_user_steps_sum', {
    p_user_id: userId,
    p_start_time: start,
    p_end_time: end,
  });
  return { data, error };
}

export async function getCaloriesDataFromDb(
  userId: string,
  start: string,
  end: string,
) {
  const { data, error } = await supabase.rpc('get_user_calories_sum', {
    p_user_id: userId,
    p_start_time: start,
    p_end_time: end,
  });
  return { data, error };
}

export async function getWeightDataFromDb(userId: string) {
  const { data, error } = await supabase
    .from('weight')
    .select('*')
    .eq('user_id', userId)
    .order('time', { ascending: false })
    .limit(1);
  return { data, error };
}

export async function getSleepDataFromDb(userId: string) {
  const { data, error } = await supabase
    .from('sleep_sessions')
    .select('*')
    .eq('user_id', userId)
    .order('start_time', { ascending: false })
    .limit(1);
  return { data, error };
}

export const checkGoogleSignIn = async () => {
  if (Platform.OS === 'android') {
    const isAvailable = await GoogleSignin.hasPlayServices({
      showPlayServicesUpdateDialog: true,
    });

    if (!isAvailable) {
      console.warn('Google Play Services not available');
      return null;
    }
  }

  try {
    const hasSignedIn = await GoogleSignin.hasPreviousSignIn(); // Make sure to `await` this
    const user = await GoogleSignin.getCurrentUser(); // Also `await`
    console.log('User already signed in:', user);

    if (hasSignedIn && user) {
      console.log('User already signed in:', user);
      return user;
    } else {
      console.log('User not signed in');
      return null;
    }
  } catch (error) {
    console.error('Error checking sign-in status:', error);
    return null;
  }
};

export async function fetchNotifications(userId: string) {
  try {
    console.log(
      '[fetchNotifications] Fetching notifications for user:',
      userId,
    );
    const response = await fetch(
      'https://ai-coach.delicut.click/notification',
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ user_id: userId }),
      },
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    console.log('[fetchNotifications] Notification data:', data);
    // showSuccessToast(data?.message || 'Notifications fetched');
    return data;
  } catch (error) {
    console.error('[fetchNotifications] Fetch error:', error);
    // showErrorToast('Failed to fetch notifications. Please try again.');
    throw error;
  }
}

export async function sendLiveDataNotification(userId: string, liveData: any) {
  try {
    console.log('[sendLiveDataNotification] Sending data for user:', userId);

    const response = await fetch(
      'https://ai-coach.delicut.click/notification_manual',
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          user_id: userId,
          live_data: liveData,
        }),
      },
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('[sendLiveDataNotification] Response:', data);

    Alert.alert(
      'Success',
      data?.data?.notification
        ? `${data.data.notification}\n\nPayload:\n${JSON.stringify(liveData, null, 2)}`
        : `Notification sent\n\nPayload:\n${JSON.stringify(liveData, null, 2)}`,
    );

    return data;
  } catch (error) {
    console.error('[sendLiveDataNotification] Error:', error);
    Alert.alert(
      'Failed',
      JSON.stringify(error) || 'Failed to send notification',
    );
    throw error;
  }
}

export async function verifyUser(userId: string) {
  try {
    const { data, error, status } = await supabase
      .from('users')
      .update({ is_verified: true })
      .eq('id', userId);

    if (error) {
      console.error('Supabase error:', error.message);
      return { success: false, message: error.message };
    }

    if (status !== 200 && status !== 204) {
      return { success: false, message: `Unexpected status code: ${status}` };
    }

    return { success: true, data };
  } catch (err: any) {
    console.error('Unexpected error:', err);
    return { success: false, message: err.message || 'Unknown error occurred' };
  }
}

export function kgToLb(weightKg: number): number {
  return parseFloat((weightKg * 2.20462).toFixed(2));
}

export function lbToKg(weightLb: number): number {
  return parseFloat((weightLb / 2.20462).toFixed(2));
}
