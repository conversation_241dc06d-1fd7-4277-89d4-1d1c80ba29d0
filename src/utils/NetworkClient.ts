/* eslint-disable prettier/prettier */
import axios, {
  AxiosError,
  AxiosHeaders,
  AxiosInstance,
  AxiosResponse,
} from 'axios';
import Config from 'react-native-config';
import store from '../redux/store';
import { getDataFromAsyncStorage } from './functions';
class NetworkClient {
  private service: AxiosInstance;

  constructor() {
    this.service = axios.create();

    // Attach interceptors
    this.service.interceptors.response.use(
      this.handleSuccess,
      this.handleError,
    );
    this.service.interceptors.request.use(async (config) => {
      config.baseURL = Config.BASE_URL;
      const token =
        store.getState().auth.token || (await getDataFromAsyncStorage('token'));
      if (token) config.headers.authorization = token;
      if (store.getState().app.lanKey)
        config.headers['language'] = store.getState().app.lanKey;
      return config;
    });
  }

  private handleSuccess(response: AxiosResponse): AxiosResponse {
    return response;
  }

  private handleError = (error: AxiosError): Promise<never> => {
    if (error?.response?.status === 401) {
      // Example: Clear user data from storage
    }
    return Promise.reject(error);
  };

  public async get<T>(
    path: string,
    headers?: AxiosHeaders,
  ): Promise<AxiosResponse> {
    const response = await this.service.get<T>(path, {
      headers,
    });
    return response;
  }

  public async patch<T>(
    path: string,
    payload: T,
    headers?: AxiosHeaders,
  ): Promise<AxiosResponse> {
    const response = await this.service.request<T>({
      method: 'PATCH',
      url: path,
      responseType: 'json',
      data: payload,
      headers,
    });
    return response;
  }

  public async post<T>(
    path: string,
    payload: T,
    headers?: AxiosHeaders,
  ): Promise<AxiosResponse> {
    const response = await this.service.request<T>({
      method: 'POST',
      url: path,
      responseType: 'json',
      data: payload,
      headers,
    });
    return response;
  }

  public async put<T>(
    path: string,
    payload: T,
    headers?: AxiosHeaders,
  ): Promise<AxiosResponse> {
    const response = await this.service.request<T>({
      method: 'PUT',
      url: path,
      responseType: 'json',
      data: payload,
      headers,
    });
    return response;
  }
}

export default new NetworkClient();
