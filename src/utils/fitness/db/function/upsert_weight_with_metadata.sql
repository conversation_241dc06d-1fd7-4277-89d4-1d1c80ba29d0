CREATE OR REPLACE FUNCTION public.upsert_weight_with_metadata(p_weights jsonb, p_user_id uuid)
 RETURNS void
 LANGUAGE plpgsql
AS $function$DECLARE
  rec jsonb;
  metadata_id uuid;
BEGIN
  FOR rec IN SELECT * FROM jsonb_array_elements(p_weights)
  LOOP
    -- ✅ Insert only if dataOrigin is "com.google.android.apps.fitness"
    IF rec->'metadata'->>'dataOrigin' = 'com.google.android.apps.fitness' THEN

      -- Upsert record_metadata
      INSERT INTO record_metadata (
        record_type,
        user_id,
        device_id,
        recording_method,
        client_record_version,
        client_record_id,
        data_origin,
        last_modified_time,
        synced_at
      )
      VALUES (
        'Weight',
        p_user_id,
        NULL,
        (rec->'metadata'->>'recordingMethod')::int,
        (rec->'metadata'->>'clientRecordVersion')::int,
        rec->'metadata'->>'id',
        rec->'metadata'->>'dataOrigin',
        (rec->'metadata'->>'lastModifiedTime')::timestamp,
        now()
      )
      ON CONFLICT (client_record_id) DO UPDATE
      SET
        last_modified_time = EXCLUDED.last_modified_time,
        synced_at = now()
      RETURNING id INTO metadata_id;

      -- Fallback if metadata_id is still null
      IF metadata_id IS NULL THEN
        SELECT id INTO metadata_id
        FROM record_metadata
        WHERE client_record_id = rec->'metadata'->>'id';
      END IF;

      -- Insert or update weight record
      IF metadata_id IS NOT NULL THEN
        INSERT INTO weight (
          user_id,
          record_metadata_id,
          weight_kg,
          weight_lb,
          position,
          created_at,
          updated_at,
          time
        )
        VALUES (
          p_user_id,
          metadata_id,
          (rec->'weight'->>'inKilograms')::numeric,
          (rec->'weight'->>'inPounds')::numeric,
          COALESCE((rec->>'position')::int, 0),
          now(),
          now(),
          (rec->>'time')::timestamp
        )
        ON CONFLICT (record_metadata_id) DO UPDATE
        SET
          weight_kg = EXCLUDED.weight_kg,
          weight_lb = EXCLUDED.weight_lb,
          position = EXCLUDED.position,
          updated_at = now(),
          time = EXCLUDED.time;
      END IF;

    END IF; -- ✅ End dataOrigin check
  END LOOP;
END;$function$
