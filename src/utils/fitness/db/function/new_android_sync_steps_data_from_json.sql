CREATE OR REPLACE FUNCTION public.new_android_sync_steps_data_from_json(p_user_id uuid, p_steps jsonb, p_start_filter timestamp with time zone, p_end_filter timestamp with time zone)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$DECLARE
  step_rec JSONB;
  sync_time TIMESTAMP := NOW();
  deleted_steps_count INT := 0;
  deleted_metadata_count INT := 0;
  inserted_metadata_count INT := 0;
  inserted_steps_count INT := 0;
  step_meta_id UUID;
  var_client_record_id TEXT;
  processed_time_range JSONB := jsonb_build_object('start', p_start_filter, 'end', p_end_filter);
BEGIN
  -- Step 1: Delete existing Android steps data in time range for this user
  DELETE FROM steps
  WHERE record_metadata_id IN (
    SELECT id FROM record_metadata
    WHERE user_id = p_user_id
      AND record_type = 'Steps'
      AND data_origin = 'com.google.android.apps.fitness'
  )
  AND start_time >= p_start_filter AND end_time <= p_end_filter;

  GET DIAGNOSTICS deleted_steps_count = ROW_COUNT;

  DELETE FROM record_metadata
  WHERE user_id = p_user_id
    AND record_type = 'Steps'
    AND data_origin = 'com.google.android.apps.fitness'
    AND id NOT IN (SELECT record_metadata_id FROM steps);

  GET DIAGNOSTICS deleted_metadata_count = ROW_COUNT;

  -- Step 2: Loop through each step record in p_steps JSON array
  FOR step_rec IN SELECT * FROM jsonb_array_elements(p_steps)
  LOOP
    -- ✅ Insert only if dataOrigin is 'com.google.android.apps.fitness'
    IF step_rec->'metadata'->>'dataOrigin' = 'com.google.android.apps.fitness' THEN
      -- Generate new client_record_id UUID for uniqueness
      var_client_record_id := gen_random_uuid()::text;

      -- Insert or ignore record_metadata row
      INSERT INTO record_metadata (
        id, user_id, record_type, data_origin,
        recording_method, client_record_version,
        client_record_id, last_modified_time,
        synced_at, created_at, updated_at
      )
      VALUES (
        gen_random_uuid(),          -- new UUID for PK
        p_user_id,
        'Steps',
        'com.google.android.apps.fitness',
        (step_rec->'metadata'->>'recordingMethod')::int,
        (step_rec->'metadata'->>'clientRecordVersion')::bigint,
        var_client_record_id,
        (step_rec->'metadata'->>'lastModifiedTime')::timestamptz,
        sync_time, sync_time, sync_time
      )
      ON CONFLICT (client_record_id) DO NOTHING;

      -- Fetch the metadata ID (existing or inserted)
      SELECT rm.id INTO step_meta_id
      FROM record_metadata rm
      WHERE rm.user_id = p_user_id
        AND rm.client_record_id = var_client_record_id
      LIMIT 1;

      IF step_meta_id IS NOT NULL THEN
        inserted_metadata_count := inserted_metadata_count + 1;

        -- Insert into steps table
        INSERT INTO steps (
          user_id, record_metadata_id, count,
          start_time, end_time, position,
          created_at, updated_at
        )
        VALUES (
          p_user_id,
          step_meta_id,
          (step_rec->>'count')::int,
          (step_rec->>'startTime')::timestamptz,
          (step_rec->>'endTime')::timestamptz,
          0,
          sync_time, sync_time
        );

        inserted_steps_count := inserted_steps_count + 1;
      END IF;
    END IF;
  END LOOP;

  -- Step 3: Return summary of operation
  RETURN jsonb_build_object(
    'success', true,
    'processed_time_range', processed_time_range,
    'input_records', jsonb_array_length(p_steps),
    'deleted_steps', deleted_steps_count,
    'deleted_metadata', deleted_metadata_count,
    'inserted_metadata', inserted_metadata_count,
    'inserted_steps', inserted_steps_count,
    'sync_timestamp', sync_time
  );
END;$function$
