CREATE OR REPLACE FUNCTION public.sync_ios_calories_data_from_json(p_user_id uuid, p_calories jsonb, p_start_filter timestamp without time zone, p_end_filter timestamp without time zone)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$
DECLARE
  rec JSONB;
  meta JSONB;
  s_time TIMESTAMP;
  e_time TIMESTAMP;
  kcal NUMERIC;
  kj NUMERIC;
  record_id UUID;
  metadata_id UUID;
  deleted_calories_count INT := 0;
  deleted_metadata_count INT := 0;
  inserted_metadata_count INT := 0;
  inserted_calories_count INT := 0;
  sync_time TIMESTAMP := NOW();
BEGIN
  -- Step 1: Delete existing iOS calorie records in the time range
  DELETE FROM calories
  WHERE record_metadata_id IN (
    SELECT id FROM record_metadata
    WHERE user_id = p_user_id
      AND record_type = 'Calories'
      AND data_origin LIKE 'com.apple.health%'
  )
  AND start_time >= p_start_filter AND end_time <= p_end_filter;

  GET DIAGNOSTICS deleted_calories_count = ROW_COUNT;

  DELETE FROM record_metadata
  WHERE user_id = p_user_id
    AND record_type = 'Calories'
    AND data_origin LIKE 'com.apple.health%'
    AND id NOT IN (SELECT record_metadata_id FROM calories);

  GET DIAGNOSTICS deleted_metadata_count = ROW_COUNT;

  -- Step 2: Insert new iOS calorie records
  FOR rec IN SELECT * FROM jsonb_array_elements(p_calories)
  LOOP
    meta := rec->'metadata'->0;
    IF meta IS NULL THEN CONTINUE; END IF;

    s_time := (rec->>'startDate')::timestamp;
    e_time := (rec->>'endDate')::timestamp;

    IF s_time >= p_start_filter AND e_time <= p_end_filter THEN
      kcal := (rec->>'value')::NUMERIC;
      kj := kcal * 4.184;
      record_id := gen_random_uuid();

      -- Insert metadata
      INSERT INTO record_metadata (
        id, user_id, record_type, data_origin,
        recording_method, client_record_version,
        client_record_id, last_modified_time, synced_at, created_at, updated_at
      )
      VALUES (
        gen_random_uuid(),
        p_user_id,
        'Calories',
        meta->>'sourceId',
        1,
        1,
        record_id::TEXT,
        e_time,
        sync_time, sync_time, sync_time
      )
      ON CONFLICT (client_record_id) DO NOTHING;

      -- Retrieve metadata ID using TEXT comparison
      SELECT id INTO metadata_id
      FROM record_metadata
      WHERE client_record_id = record_id::TEXT AND user_id = p_user_id
      LIMIT 1;

      inserted_metadata_count := inserted_metadata_count + 1;

      -- Insert calorie row
      INSERT INTO calories (
        user_id, record_metadata_id, in_kilocalories, in_kilojoules,
        start_time, end_time, created_at, updated_at
      )
      VALUES (
        p_user_id,
        metadata_id,
        kcal,
        kj,
        s_time,
        e_time,
        sync_time,
        sync_time
      );

      inserted_calories_count := inserted_calories_count + 1;
    END IF;
  END LOOP;

  RETURN jsonb_build_object(
    'success', true,
    'deleted_metadata', deleted_metadata_count,
    'deleted_calories', deleted_calories_count,
    'inserted_metadata', inserted_metadata_count,
    'inserted_calories', inserted_calories_count,
    'sync_timestamp', sync_time
  );
END;
$function$
