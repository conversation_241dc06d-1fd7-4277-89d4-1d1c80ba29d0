CREATE OR REPLACE FUNCTION public.sync_calories_data_ultimate(p_user_id uuid, p_client_record_ids text[], p_start_times timestamp without time zone[], p_end_times timestamp without time zone[], p_kcal_values numeric[], p_kj_values numeric[], p_recording_methods integer[], p_client_record_versions bigint[], p_last_modified_times timestamp without time zone[], p_start_filter timestamp without time zone, p_end_filter timestamp without time zone)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$
DECLARE
  result JSONB;
  deleted_count INT;
  processed_count INT;
BEGIN

  WITH
  -- Step 1: Delete calories in this range not in current payload
  deleted_calories AS (
    DELETE FROM calories
    WHERE record_metadata_id IN (
      SELECT id FROM record_metadata
      WHERE user_id = p_user_id
        AND record_type = 'Calories'
        AND data_origin = 'com.google.android.apps.fitness'
        AND client_record_id != ALL(p_client_record_ids)
    )
    AND start_time >= p_start_filter
    AND end_time <= p_end_filter
    RETURNING record_metadata_id
  ),

  deleted_metadata AS (
    DELETE FROM record_metadata
    WHERE id IN (SELECT record_metadata_id FROM deleted_calories)
    RETURNING 1
  ),

  -- Step 2: Upsert record_metadata
  upserted_metadata AS (
    INSERT INTO record_metadata (
      record_type,
      user_id,
      recording_method,
      client_record_version,
      client_record_id,
      data_origin,
      last_modified_time,
      synced_at
    )
    SELECT
      'Calories',
      p_user_id,
      UNNEST(p_recording_methods),
      UNNEST(p_client_record_versions),
      UNNEST(p_client_record_ids),
      'com.google.android.apps.fitness',
      UNNEST(p_last_modified_times),
      now()
    ON CONFLICT (client_record_id) DO UPDATE SET
      last_modified_time = EXCLUDED.last_modified_time,
      client_record_version = EXCLUDED.client_record_version,
      recording_method = EXCLUDED.recording_method,
      synced_at = now()
    RETURNING id, client_record_id
  ),

  -- Step 3: Upsert calories records
  upserted_calories AS (
    INSERT INTO calories (
      user_id,
      record_metadata_id,
      in_kilocalories,
      in_kilojoules,
      start_time,
      end_time,
      created_at,
      updated_at
    )
    SELECT
      p_user_id,
      um.id,
      s.kcal,
      s.kj,
      s.start_time,
      s.end_time,
      now(),
      now()
    FROM (
      SELECT
        UNNEST(p_client_record_ids) AS client_record_id,
        UNNEST(p_kcal_values) AS kcal,
        UNNEST(p_kj_values) AS kj,
        UNNEST(p_start_times) AS start_time,
        UNNEST(p_end_times) AS end_time
    ) s
    JOIN upserted_metadata um ON s.client_record_id = um.client_record_id
    WHERE s.start_time >= p_start_filter AND s.end_time <= p_end_filter
    ON CONFLICT (record_metadata_id) DO UPDATE SET
      in_kilocalories = EXCLUDED.in_kilocalories,
      in_kilojoules = EXCLUDED.in_kilojoules,
      start_time = EXCLUDED.start_time,
      end_time = EXCLUDED.end_time,
      updated_at = now()
    RETURNING 1
  )

  -- Final result
  SELECT
    (SELECT COUNT(*) FROM deleted_metadata)::INT,
    (SELECT COUNT(*) FROM upserted_calories)::INT
  INTO deleted_count, processed_count;

  result := jsonb_build_object(
    'processed', processed_count,
    'deleted', deleted_count
  );

  RETURN result;

END;
$function$
