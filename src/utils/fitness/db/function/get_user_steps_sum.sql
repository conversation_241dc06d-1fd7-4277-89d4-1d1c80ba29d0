CREATE OR REPLACE FUNCTION public.get_user_steps_sum(p_user_id uuid, p_start_time timestamp without time zone, p_end_time timestamp without time zone)
 RETURNS TABLE(total_steps bigint)
 LANGUAGE plpgsql
 STABLE
AS $function$
BEGIN
  RETURN QUERY
  SELECT COALESCE(SUM(count), 0)
  FROM steps
  WHERE user_id = p_user_id
    AND start_time >= p_start_time
    AND end_time <= p_end_time;
END;
$function$
