CREATE OR REPLACE FUNCTION public.sync_calories_data_from_json(p_user_id uuid, p_calories jsonb, p_start_filter timestamp without time zone, p_end_filter timestamp without time zone)
 RETURNS void
 LANGUAGE plpgsql
AS $function$DECLARE
  client_record_ids text[] := '{}';
  kcal_values NUMERIC[] := '{}';
  kj_values NUMERIC[] := '{}';
  start_times TIMESTAMP[] := '{}';
  end_times TIMESTAMP[] := '{}';
  recording_methods INT[] := '{}';
  client_record_versions BIGINT[] := '{}';
  last_modified_times TIMESTAMP[] := '{}';
  rec JSONB;
  s_time TIMESTAMP;
  e_time TIMESTAMP;
BEGIN
  FOR rec IN SELECT * FROM jsonb_array_elements(p_calories)
  LOOP
    IF rec->'metadata'->>'dataOrigin' = 'com.google.android.apps.fitness' THEN
      s_time := (rec->>'startTime')::TIMESTAMP;
      e_time := (rec->>'endTime')::TIMESTAMP;

      IF s_time >= p_start_filter AND e_time <= p_end_filter THEN
        -- Cast ID to text
        client_record_ids := client_record_ids || (rec->'metadata'->>'id')::text;
        kcal_values := kcal_values || (rec->'energy'->>'inKilocalories')::NUMERIC;
        kj_values := kj_values || (rec->'energy'->>'inKilojoules')::NUMERIC;
        start_times := start_times || s_time;
        end_times := end_times || e_time;
        recording_methods := recording_methods || (rec->'metadata'->>'recordingMethod')::INT;
        client_record_versions := client_record_versions || (rec->'metadata'->>'clientRecordVersion')::BIGINT;
        last_modified_times := last_modified_times || (rec->'metadata'->>'lastModifiedTime')::TIMESTAMP;
      END IF;
    END IF;
  END LOOP;

  PERFORM sync_calories_data_ultimate(
    p_user_id,
    client_record_ids,
    start_times,
    end_times,
    kcal_values,
    kj_values,
    recording_methods,
    client_record_versions,
    last_modified_times,
    p_start_filter,
    p_end_filter
  );
END;$function$
