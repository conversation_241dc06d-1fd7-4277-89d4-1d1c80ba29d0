CREATE OR REPLACE FUNCTION public.get_user_dashboard_data_new(p_user_id uuid, p_start_time timestamp with time zone, p_end_time timestamp with time zone)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$
declare
  v_steps integer := 0;
  v_calories_burned numeric := 0;
  v_calories_consumed numeric := 0;
  v_target_steps integer := 0;
  v_target_calories numeric := 0;
  v_last_synced timestamp;
  v_activities jsonb;
begin
  -- Total steps
  select coalesce(sum(count), 0)
  into v_steps
  from steps
  where user_id = p_user_id
    and start_time >= p_start_time
    and end_time <= p_end_time;

  -- Calories burned
  v_calories_burned := v_steps * 0.04;

  -- Calories consumed
  select coalesce(sum(total_calories), 0)
  into v_calories_consumed
  from meal_logs
  where user_id = p_user_id
    and meal_time >= p_start_time
    and meal_time <= p_end_time;

  -- User goals
  select 
    coalesce(daily_goals_steps, 0),
    coalesce(target_calorie, 0)
  into v_target_steps, v_target_calories
  from user_fitness_profiles
  where user_id = p_user_id;

  -- Last sync
  select max(last_synced_at)
  into v_last_synced
  from user_module_sync
  where user_id = p_user_id
    and module_name = 'steps';

  -- Activities (steps first, then meals)
  select coalesce(
    jsonb_agg(obj order by (obj->>'time')::timestamptz),
    '[]'::jsonb
  )
  into v_activities
  from (
    -- Steps first
    select jsonb_build_object(
      'type', 'step',
      'time', start_time,
      'meal_name', null,
      'total_calories', (count * 0.04)::numeric,
      'protein_grams', null,
      'carbs_grams', null,
      'fat_grams', null
    ) as obj
    from steps
    where user_id = p_user_id
      and start_time between p_start_time and p_end_time

    union all

    -- Then Meals
    select jsonb_build_object(
      'type', 'meal',
      'time', meal_time,
      'meal_name', meal_name,
      'total_calories', total_calories,
      'protein_grams', protein_grams,
      'carbs_grams', carbs_grams,
      'fat_grams', fat_grams
    ) as obj
    from meal_logs
    where user_id = p_user_id
      and meal_time between p_start_time and p_end_time
  ) all_data;

  -- Final JSON response
  return jsonb_build_object(
    'steps', v_steps,
    'calories_burned', v_calories_burned,
    'calories_consumed', v_calories_consumed,
    'target_steps', v_target_steps,
    'target_calories', v_target_calories,
    'calorie_balance', v_calories_consumed - v_calories_burned,
    'last_synced_at', v_last_synced,
    'activities', v_activities
  );
end;
$function$
