/* eslint-disable @typescript-eslint/no-require-imports */
const fs = require('fs');
const path = require('path');
const { Client } = require('pg');

// === CONFIG ===
const OUTPUT_DIR = path.resolve(__dirname, './');

const client = new Client({
  host: 'aws-0-ap-south-1.pooler.supabase.com',
  port: 6543,
  user: 'postgres.ertqxifvqfavjjnezbfz',
  password: 'Asdfg;lkjh@12345',
  database: 'postgres',
  ssl: { rejectUnauthorized: false },
});

async function fetchFunctions() {
  const res = await client.query(`
    SELECT
      p.proname AS function_name,
      pg_get_functiondef(p.oid) AS function_definition
    FROM
      pg_proc p
    JOIN
      pg_namespace n ON n.oid = p.pronamespace
    WHERE
      n.nspname = 'public'
    ORDER BY
      function_name;
  `);
  return res.rows;
}

async function saveFunctions(functions) {
  if (!fs.existsSync(OUTPUT_DIR)) {
    fs.mkdirSync(OUTPUT_DIR, { recursive: true });
  }

  for (const { function_name, function_definition } of functions) {
    const filename = `${function_name}.sql`;
    const filepath = path.join(OUTPUT_DIR, filename);

    let shouldWrite = true;

    // If file exists and content is the same, skip writing
    if (fs.existsSync(filepath)) {
      const existing = fs.readFileSync(filepath, 'utf-8');
      if (existing.trim() === function_definition.trim()) {
        shouldWrite = false;
      }
    }

    if (shouldWrite) {
      fs.writeFileSync(filepath, function_definition);
      console.log(`✅ Updated: ${filename}`);
    } else {
      console.log(`↪️ Skipped (no change): ${filename}`);
    }
  }
}

async function main() {
  try {
    await client.connect();
    const functions = await fetchFunctions();
    await client.end();

    await saveFunctions(functions);
    console.log(`🎉 All functions saved or updated at ${OUTPUT_DIR}`);
  } catch (err) {
    console.error('❌ Error:', err);
    process.exit(1);
  }
}

main();
