CREATE OR REPLACE FUNCTION public.android_sync_steps_data_from_json(p_user_id uuid, p_steps jsonb, p_start_filter timestamp without time zone, p_end_filter timestamp without time zone)
 RETURNS boolean
 LANGUAGE plpgsql
AS $function$
DECLARE
    client_record_ids text[] := '{}';
    counts int[] := '{}';
    start_times timestamp[] := '{}';
    end_times timestamp[] := '{}';
    recording_methods int[] := '{}';
    client_record_versions bigint[] := '{}';
    last_modified_times timestamp[] := '{}';
    rec jsonb;
    s_time timestamp;
    e_time timestamp;
    temp_id text;
    temp_count int;
    temp_recording_method int;
    temp_client_version bigint;
    temp_last_modified timestamp;
    processed_count int := 0;
    total_count int := 0;
BEGIN
    -- Validate input parameters
    IF p_steps IS NULL OR jsonb_typeof(p_steps) != 'array' THEN
        RAISE NOTICE 'Invalid or null p_steps parameter';
        RETURN false;
    END IF;
    
    RAISE NOTICE 'Starting to process steps data for user: %', p_user_id;
    RAISE NOTICE 'Filter range: % to %', p_start_filter, p_end_filter;
    
    -- Process each record in the JSON array
    FOR rec IN SELECT * FROM jsonb_array_elements(p_steps)
    LOOP
        total_count := total_count + 1;
        
        -- Check if record has required structure
        IF rec ? 'metadata' AND rec ? 'startTime' AND rec ? 'endTime' THEN
            -- Check data origin (make case-insensitive comparison)
            IF LOWER(rec->'metadata'->>'dataOrigin') = 'com.google.android.apps.fitness' THEN
                BEGIN
                    -- Parse timestamps with better error handling
                    BEGIN
                        s_time := CASE 
                            WHEN rec->>'startTime' ~ '^\d+$' THEN 
                                to_timestamp((rec->>'startTime')::bigint / 1000.0)
                            ELSE 
                                (rec->>'startTime')::timestamp
                        END;
                        
                        e_time := CASE 
                            WHEN rec->>'endTime' ~ '^\d+$' THEN 
                                to_timestamp((rec->>'endTime')::bigint / 1000.0)
                            ELSE 
                                (rec->>'endTime')::timestamp
                        END;
                    EXCEPTION WHEN others THEN
                        RAISE NOTICE 'Invalid timestamp format in record %: startTime=%, endTime=%', 
                            total_count, rec->>'startTime', rec->>'endTime';
                        CONTINUE;
                    END;
                    
                    -- Apply time filter
                    IF s_time >= p_start_filter AND e_time <= p_end_filter THEN
                        -- Extract and validate all fields with proper error handling
                        BEGIN
                            -- ID field
                            temp_id := rec->'metadata'->>'id';
                            IF temp_id IS NULL OR temp_id = '' THEN
                                RAISE NOTICE 'Missing or empty ID in record %', total_count;
                                CONTINUE;
                            END IF;
                            
                            -- Count field - handle different possible field names
                            temp_count := COALESCE(
                                (rec->>'count')::int,
                                (rec->>'steps')::int,
                                (rec->'value'->>'intVal')::int,
                                0
                            );
                            
                            -- Recording method
                            temp_recording_method := COALESCE(
                                (rec->'metadata'->>'recordingMethod')::int,
                                0
                            );
                            
                            -- Client record version
                            temp_client_version := COALESCE(
                                (rec->'metadata'->>'clientRecordVersion')::bigint,
                                1
                            );
                            
                            -- Last modified time
                            BEGIN
                                temp_last_modified := CASE 
                                    WHEN rec->'metadata'->>'lastModifiedTime' ~ '^\d+$' THEN 
                                        to_timestamp((rec->'metadata'->>'lastModifiedTime')::bigint / 1000.0)
                                    ELSE 
                                        COALESCE(
                                            (rec->'metadata'->>'lastModifiedTime')::timestamp,
                                            s_time
                                        )
                                END;
                            EXCEPTION WHEN others THEN
                                temp_last_modified := s_time;
                            END;
                            
                            -- Add to arrays
                            client_record_ids := client_record_ids || temp_id;
                            counts := counts || temp_count;
                            start_times := start_times || s_time;
                            end_times := end_times || e_time;
                            recording_methods := recording_methods || temp_recording_method;
                            client_record_versions := client_record_versions || temp_client_version;
                            last_modified_times := last_modified_times || temp_last_modified;
                            
                            processed_count := processed_count + 1;
                            
                            RAISE NOTICE 'Processed step record %: ID=%, Count=%, Time=% → %', 
                                processed_count, temp_id, temp_count, s_time, e_time;
                                
                        EXCEPTION WHEN others THEN
                            RAISE NOTICE 'Error processing record %: %', total_count, SQLERRM;
                            CONTINUE;
                        END;
                    ELSE
                        RAISE NOTICE 'Filtered out step (time range): % → % (outside % to %)', 
                            s_time, e_time, p_start_filter, p_end_filter;
                    END IF;
                    
                EXCEPTION WHEN others THEN
                    RAISE NOTICE 'Error processing record %: %', total_count, SQLERRM;
                    CONTINUE;
                END;
            ELSE
                RAISE NOTICE 'Skipped record % - wrong data origin: %', 
                    total_count, rec->'metadata'->>'dataOrigin';
            END IF;
        ELSE
            RAISE NOTICE 'Skipped record % - missing required fields (metadata, startTime, or endTime)', total_count;
        END IF;
    END LOOP;
    
    RAISE NOTICE 'Processing complete: % records processed out of % total records', 
        processed_count, total_count;
    
    -- Only call the ultimate function if we have data to process
    IF processed_count > 0 THEN
        RAISE NOTICE 'Calling android_sync_steps_data_ultimate with % records', processed_count;
        
        PERFORM android_sync_steps_data_ultimate(
            p_user_id,
            client_record_ids,
            start_times,
            end_times,
            counts,
            recording_methods,
            client_record_versions,
            last_modified_times,
            p_start_filter,
            p_end_filter
        );
        
        RAISE NOTICE 'Successfully called android_sync_steps_data_ultimate';
        RETURN true;
    ELSE
        RAISE NOTICE 'No records to process - skipping android_sync_steps_data_ultimate call';
        RETURN false;
    END IF;
    
EXCEPTION WHEN others THEN
    RAISE EXCEPTION 'Fatal error in android_sync_steps_data_from_json: %', SQLERRM;
END;
$function$
