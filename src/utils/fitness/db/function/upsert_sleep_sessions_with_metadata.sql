CREATE OR REPLACE FUNCTION public.upsert_sleep_sessions_with_metadata(p_sleep jsonb, p_user_id uuid)
 RETURNS void
 LANGUAGE plpgsql
AS $function$DECLARE
  rec jsonb;
  metadata_id uuid;
BEGIN
  FOR rec IN SELECT * FROM jsonb_array_elements(p_sleep)
  LOOP
    -- ✅ Insert only if dataOrigin is "com.google.android.apps.fitness"
    IF rec->'metadata'->>'dataOrigin' = 'com.google.android.apps.fitness' THEN
      
      -- Upsert into record_metadata
      INSERT INTO record_metadata (
        record_type,
        user_id,
        device_id,
        recording_method,
        client_record_version,
        client_record_id,
        data_origin,
        last_modified_time,
        synced_at
      )
      VALUES (
        'Sleep',
        p_user_id,
        NULL,
        (rec->'metadata'->>'recordingMethod')::int,
        (rec->'metadata'->>'clientRecordVersion')::bigint,
        rec->'metadata'->>'id',
        rec->'metadata'->>'dataOrigin',
        (rec->'metadata'->>'lastModifiedTime')::timestamp,
        now()
      )
      ON CONFLICT (client_record_id) DO UPDATE
      SET
        last_modified_time = EXCLUDED.last_modified_time,
        synced_at = now()
      RETURNING id INTO metadata_id;

      -- Fallback to manually get metadata_id
      IF metadata_id IS NULL THEN
        SELECT id INTO metadata_id FROM record_metadata
        WHERE client_record_id = rec->'metadata'->>'id';
      END IF;

      -- Upsert sleep_sessions only if metadata_id is valid
      IF metadata_id IS NOT NULL THEN
        INSERT INTO sleep_sessions (
          user_id,
          record_metadata_id,
          start_time,
          end_time,
          minutes,
          created_at,
          updated_at
        )
        VALUES (
          p_user_id,
          metadata_id,
          (rec->>'startTime')::timestamp,
          (rec->>'endTime')::timestamp,
          (rec->>'minutes')::int,
          now(),
          now()
        )
        ON CONFLICT (record_metadata_id) DO UPDATE
        SET
          start_time = EXCLUDED.start_time,
          end_time = EXCLUDED.end_time,
          minutes = EXCLUDED.minutes,
          updated_at = now();
      END IF;

    END IF; -- ✅ End dataOrigin check
  END LOOP;
END;$function$
