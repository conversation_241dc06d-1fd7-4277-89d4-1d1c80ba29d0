CREATE OR REPLACE FUNCTION public.sync_ios_steps_data_from_json(p_user_id uuid, p_steps jsonb, p_start_filter timestamp without time zone, p_end_filter timestamp without time zone)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$DECLARE
  wrapper JSONB;
  step_rec JSONB;
  actual_steps JSONB;
  meta JSONB;
  processed_time_range JSONB;
  deleted_steps_count INT := 0;
  deleted_metadata_count INT := 0;
  inserted_metadata_count INT := 0;
  inserted_steps_count INT := 0;
  sync_time TIMESTAMP := NOW();
  step_meta_id UUID;
  generated_client_record_id TEXT;
BEGIN
  -- Step 1: Detect format (wrapped or flat)
  wrapper := p_steps->0;

  IF wrapper ? 'count' THEN
    actual_steps := wrapper->'count';
    processed_time_range := jsonb_build_object(
      'start', wrapper->>'startTime',
      'end', wrapper->>'endTime'
    );
  ELSE
    actual_steps := p_steps;
    processed_time_range := jsonb_build_object(
      'start', p_start_filter,
      'end', p_end_filter
    );
  END IF;

  -- Step 2: Delete existing iOS data in the specified time range
  DELETE FROM steps
  WHERE record_metadata_id IN (
    SELECT id FROM record_metadata
    WHERE user_id = p_user_id
      AND record_type = 'Steps'
      AND data_origin LIKE 'com.apple.health%'
  )
  AND start_time >= p_start_filter AND end_time <= p_end_filter;

  GET DIAGNOSTICS deleted_steps_count = ROW_COUNT;

  DELETE FROM record_metadata
  WHERE user_id = p_user_id
    AND record_type = 'Steps'
    AND data_origin LIKE 'com.apple.health%'
    AND id NOT IN (SELECT record_metadata_id FROM steps);

  GET DIAGNOSTICS deleted_metadata_count = ROW_COUNT;

  -- Step 3: Loop through each step record
  FOR step_rec IN SELECT * FROM jsonb_array_elements(actual_steps)
  LOOP
    IF jsonb_typeof(step_rec->'metadata') = 'array' THEN
      -- Step 3.1: Loop through metadata array
      FOR meta IN SELECT * FROM jsonb_array_elements(step_rec->'metadata')
      LOOP
        -- Generate a new client record ID for uniqueness
        generated_client_record_id := gen_random_uuid();

        -- Insert record metadata
        INSERT INTO record_metadata (
          id, user_id, record_type, data_origin,
          recording_method, client_record_version,
          client_record_id, last_modified_time,
          synced_at, created_at, updated_at
        )
        VALUES (
          gen_random_uuid(),
          p_user_id,
          'Steps',
          meta->>'sourceId',
          1,
          1,
          generated_client_record_id,
          (step_rec->>'endDate')::timestamptz,
          sync_time, sync_time, sync_time
        )
        ON CONFLICT (client_record_id) DO NOTHING;

        -- Fetch inserted metadata ID
        SELECT rm.id INTO step_meta_id
        FROM record_metadata rm
        WHERE rm.client_record_id = generated_client_record_id
          AND rm.user_id = p_user_id
        LIMIT 1;

        inserted_metadata_count := inserted_metadata_count + 1;

        -- Insert the step record
        INSERT INTO steps (
          user_id, record_metadata_id, count,
          start_time, end_time, position, created_at, updated_at
        )
        VALUES (
          p_user_id,
          step_meta_id,
          ROUND((step_rec->>'value')::numeric)::int,
          (step_rec->>'startDate')::timestamptz,
          (step_rec->>'endDate')::timestamptz,
          0,
          sync_time, sync_time
        );

        inserted_steps_count := inserted_steps_count + 1;
      END LOOP;
    END IF;
  END LOOP;

  -- Step 4: Return sync summary
  RETURN jsonb_build_object(
    'success', true,
    'processed_time_range', processed_time_range,
    'input_records', jsonb_array_length(actual_steps),
    'deleted_steps', deleted_steps_count,
    'deleted_metadata', deleted_metadata_count,
    'inserted_metadata', inserted_metadata_count,
    'inserted_steps', inserted_steps_count,
    'sync_timestamp', sync_time
  );
END;$function$
