CREATE OR REPLACE FUNCTION public.get_user_calories_sum(p_user_id uuid, p_start_time timestamp without time zone, p_end_time timestamp without time zone)
 RETURNS numeric
 LANGUAGE plpgsql
AS $function$
DECLARE
  total NUMERIC;
BEGIN
  SELECT COALESCE(SUM(in_kilocalories), 0)
  INTO total
  FROM calories
  WHERE user_id = p_user_id
    AND start_time >= p_start_time
    AND end_time <= p_end_time;

  RETURN total;
END;
$function$
