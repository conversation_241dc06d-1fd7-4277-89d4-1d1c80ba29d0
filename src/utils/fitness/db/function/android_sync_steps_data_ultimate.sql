CREATE OR REPLACE FUNCTION public.android_sync_steps_data_ultimate(p_user_id uuid, p_client_record_ids text[], p_start_times timestamp without time zone[], p_end_times timestamp without time zone[], p_counts integer[], p_recording_methods integer[], p_client_record_versions bigint[], p_last_modified_times timestamp without time zone[], p_start_filter timestamp without time zone, p_end_filter timestamp without time zone)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$
DECLARE
  result jsonb;
  deleted_count int;
  processed_count int;
BEGIN

  WITH
  deleted_steps AS (
    DELETE FROM steps
    WHERE record_metadata_id IN (
      SELECT id FROM record_metadata
      WHERE user_id = p_user_id
        AND record_type = 'Steps'
        AND data_origin = 'com.google.android.apps.fitness'
        AND client_record_id NOT IN (
          SELECT UNNEST(p_client_record_ids)::text
        )
    )
    AND start_time >= p_start_filter
    AND end_time <= p_end_filter
    RETURNING record_metadata_id
  ),

  deleted_metadata AS (
    DELETE FROM record_metadata
    WHERE id IN (SELECT record_metadata_id FROM deleted_steps)
    RETURNING 1
  ),

  upserted_metadata AS (
    INSERT INTO record_metadata (
      record_type, user_id, recording_method, client_record_version,
      client_record_id, data_origin, last_modified_time, synced_at
    )
    SELECT
      'Steps',
      p_user_id,
      UNNEST(p_recording_methods),
      UNNEST(p_client_record_versions),
      UNNEST(p_client_record_ids)::text,
      'com.google.android.apps.fitness',
      UNNEST(p_last_modified_times),
      now()
    ON CONFLICT (client_record_id) DO UPDATE SET
      last_modified_time = EXCLUDED.last_modified_time,
      client_record_version = EXCLUDED.client_record_version,
      recording_method = EXCLUDED.recording_method,
      synced_at = now()
    RETURNING id, client_record_id
  ),

  upserted_steps AS (
    INSERT INTO steps (
      user_id, record_metadata_id, count,
      start_time, end_time, position, created_at
    )
    SELECT
      p_user_id,
      um.id,
      s.count,
      s.start_time,
      s.end_time,
      0,
      now()
    FROM (
      SELECT
        UNNEST(p_client_record_ids) AS client_record_id_text,
        UNNEST(p_counts) AS count,
        UNNEST(p_start_times) AS start_time,
        UNNEST(p_end_times) AS end_time
    ) s
    JOIN upserted_metadata um ON s.client_record_id_text::text = um.client_record_id
    WHERE s.start_time >= p_start_filter AND s.end_time <= p_end_filter
    ON CONFLICT (record_metadata_id) DO UPDATE SET
      count = EXCLUDED.count,
      start_time = EXCLUDED.start_time,
      end_time = EXCLUDED.end_time,
      position = EXCLUDED.position,
      created_at = EXCLUDED.created_at
    RETURNING 1
  )

  SELECT
    (SELECT COUNT(*) FROM deleted_metadata)::int,
    (SELECT COUNT(*) FROM upserted_steps)::int
  INTO deleted_count, processed_count;

  result := jsonb_build_object(
    'processed', processed_count,
    'deleted', deleted_count
  );

  RETURN result;

END;
$function$
