import {
  AddEditAddressPayload,
  BodyMetricsPayload,
  editDeliveryAddressPayload,
  FcmTokenPayload,
  GetDeliveryByDatePayload,
  GetMealPreferencesPayload,
  getWeekWiseDeliveryPayload,
  RecipeRatingPayload,
  renewSubscriptionPayload,
  RestartDeliveryPayload,
  SetMealLogPayload,
} from 'actions';
import NetworkClient from './NetworkClient';

import axios from 'axios';

export async function getAllCityApi() {
  return NetworkClient.get('v1/master-data/get-all-city');
}
export async function getAppMasterDataApi() {
  return NetworkClient.get('v1/master-data/get-all');
}
export async function getAppMasterRmsDataApi() {
  return NetworkClient.post('v1/master-data/rms', { key: '' });
}
export async function getDeliveryAreaApi(name: string) {
  return NetworkClient.get(`v1/master-data/get-delivery-area?cityName=${name}`);
}
export async function getDelivery<PERSON>lotApi(name: string, areaName: string) {
  return NetworkClient.get(
    `v1/master-data/get-delivery-slot?cityName=${name}&areaName=${areaName}`,
  );
}
export async function getIngredientDataApi() {
  return NetworkClient.get('v1/master-data/get-ingredient');
}
export async function getMealPreferencesApi(obj: GetMealPreferencesPayload) {
  return NetworkClient.post('v1/master-data/rms', obj);
}
export async function getTranslationdataApi() {
  return NetworkClient.get('v1/translation/get-my-translation');
}
export async function getUserAddressByIdApi(id: string) {
  return NetworkClient.get('v1/address/edit/' + id);
}
export async function getUserFromTokenApi() {
  return NetworkClient.get('v1/customer/get_details');
}
export async function saveFcmTokenApi(obj: FcmTokenPayload) {
  return NetworkClient.post('v1/customer/save-fcm-token', obj);
}
export async function removeFcmTokenApi(obj: FcmTokenPayload) {
  return NetworkClient.post('v1/customer/remove-fcm-token', obj);
}
export async function getDeliveryDetailsApi(obj: GetDeliveryByDatePayload) {
  return NetworkClient.post('v1/delivery/get-details', obj);
}
export async function getDeliveryListApi() {
  return NetworkClient.get('v1/delivery/list');
}
export async function setMealLogApi(obj: SetMealLogPayload) {
  return NetworkClient.post('v2/delivery/meal-log', obj);
}
export async function setRecipeRatingApi(obj: RecipeRatingPayload) {
  return NetworkClient.post('v1/delivery/add-recipe-rating', obj);
}

export async function getSubscriptionDetailsApi() {
  return NetworkClient.get('v1/subscription/get-details');
}

export async function editDeliveryAddressApi(obj: editDeliveryAddressPayload) {
  return NetworkClient.post('v1/delivery/edit-address', obj);
}
export async function weekWiseDeliveryApi(obj: getWeekWiseDeliveryPayload) {
  return NetworkClient.post('v1/delivery/week-wise', obj);
}
export async function updateUserAddressByIdApi(
  id: string,
  Obj: AddEditAddressPayload,
) {
  return NetworkClient.post('v1/address/update/' + id, Obj);
}
export async function StoreUserAddressApi(Obj: AddEditAddressPayload) {
  return NetworkClient.post('v1/address/save', Obj);
}
export async function setMacroCalculationApi(obj: BodyMetricsPayload) {
  return NetworkClient.post('v2/macro-nutrient/macro-calculation', obj);
}
export async function restartDeliveryApi(obj: RestartDeliveryPayload) {
  return NetworkClient.post('v1/delivery/restart', obj);
}
export async function getweeklyRecipesApi(week?: string) {
  return NetworkClient.get(
    `v1/recipes/fetch-all-weekly${week ? `?week=${week}` : ''}`,
  );
}
export async function getSubscriptionPriceapi() {
  return NetworkClient.get('v1/subscription-price/get-subscription-price');
}
export async function renewSubscriptionapi(obj: renewSubscriptionPayload) {
  return NetworkClient.post('v1/subscription/re-new', obj);
}
export async function analyzeFoodApi(payload: {
  file: {
    uri: string;
    name: string;
    type: string;
  };
  user_id: string;
  analysis_mode: string;
}) {
  const formData = new FormData();
  formData.append('file', payload.file);
  formData.append('user_id', payload.user_id);
  formData.append('analysis_mode', payload.analysis_mode);

  console.log('📤 Sending direct Axios API request to /analyze-food');

  return axios.post(
    'https://food-detection-api.delicut.click/analyze-food',
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
  );
}

export async function logMealToServer(payload: any) {
  return axios.post(
    'https://food-detection-api.delicut.click/log-meal',
    payload,
  );
}
