import { AxiosError, AxiosResponse } from 'axios';
import { call, put, takeLatest } from 'redux-saga/effects';
import {
  getSubscriptionDetailsApi,
  getSubscriptionPriceapi,
  renewSubscriptionapi,
} from '../../utils/api';
import { renewSubscription } from '../action/subscriptionAction';
import { getCartDetailsByCustomerId } from '../slices/cartSlice';
import {
  getSubscriptionDetails,
  getSubscriptionDetailsSuccess,
  getSubscriptionPrice,
  getSubscriptionPriceSuccess,
  renewSubscriptionSuccess,
} from '../slices/subscriptionSlices';
function* getSubscriptionDetailsSaga() {
  try {
    const response: AxiosResponse = yield call(getSubscriptionDetailsApi);
    if (response.data) {
      yield put(getSubscriptionDetailsSuccess(response?.data?.data));
    }
  } catch (error) {
    if (error instanceof AxiosError) {
      yield put(getSubscriptionDetailsSuccess([]));
    }
    console.log('Error in getSubscriptionDetailsSaga', error);
  }
}
function* getSubscriptionPriceSaga() {
  try {
    const response: AxiosResponse = yield call(getSubscriptionPriceapi);
    if (response.data) {
      yield put(getSubscriptionPriceSuccess(response?.data?.data));
    }
  } catch (error) {
    if (error instanceof AxiosError) {
      yield put(getSubscriptionPriceSuccess([]));
    }
    console.log('Error in getSubscriptionPriceSaga', error);
  }
}

function* renewSubscriptionSaga(action: ReturnType<typeof renewSubscription>) {
  try {
    const response: AxiosResponse = yield call(
      renewSubscriptionapi,
      action.payload,
    );
    if (response.data) {
      if (action.meta.callBack) {
        action.meta.callBack(response.data);
      }
      yield put(getCartDetailsByCustomerId(action?.payload?.customer_id));
      yield put(renewSubscriptionSuccess());
    }
  } catch (error) {
    if (error instanceof AxiosError) {
      if (action.meta.callBack) {
        action.meta.callBack(error?.response?.data);
      }
      yield put(renewSubscriptionSuccess());
    }
    console.log('Error in renewSubscriptionSaga', error);
  }
}

export function* watchSubscriptionSagas(): Generator {
  yield takeLatest(getSubscriptionDetails.type, getSubscriptionDetailsSaga);
  yield takeLatest(getSubscriptionPrice.type, getSubscriptionPriceSaga);
  yield takeLatest(renewSubscription.type, renewSubscriptionSaga);
}
