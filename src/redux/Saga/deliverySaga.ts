import { AxiosError, AxiosResponse } from 'axios';
import { call, put, takeLatest } from 'redux-saga/effects';
import {
  editDeliveryAddressApi,
  getDeliveryDetailsApi,
  getDeliveryListApi,
  restartDeliveryApi,
  setMealLogApi,
  weekWiseDeliveryApi,
} from '../../utils/api';
import { showErrorToast } from '../../utils/functions';
import {
  editDeliveryAddress,
  getDeliveryByDate,
  getWeekWiseDelivery,
  restartDelivery,
  setMealLog,
} from '../action/deliveryActions';
import {
  editDeliveryAddressSuccess,
  getDeliveryByDateSuccess,
  getDeliveryList,
  getDeliveryListSuccess,
  getWeekWiseDeliverySuccess,
} from '../slices/deliverySlice';

function* getWeekWiseDeliverySaga(
  action: ReturnType<typeof getWeekWiseDelivery>,
) {
  try {
    const response: AxiosResponse = yield call(
      weekWiseDeliveryApi,
      action?.payload,
    );
    if (response.data.status) {
      yield put(getDeliveryList());
      yield put(getWeekWiseDeliverySuccess(response.data?.data));
    }
  } catch (error) {
    if (error instanceof AxiosError) {
      console.log('Error in getWeekWiseDeliverySaga', error?.response?.data);
      yield put(getWeekWiseDeliverySuccess([]));
    }
  }
}
function* editDeliveryAddressSaga(
  action: ReturnType<typeof editDeliveryAddress>,
) {
  try {
    const response: AxiosResponse = yield call(
      editDeliveryAddressApi,
      action?.payload,
    );
    if (response.data) {
      yield put(editDeliveryAddressSuccess());
      if (action?.meta?.callBack) action?.meta?.callBack(response.data);
    }
  } catch (error) {
    if (error instanceof AxiosError) {
      yield put(editDeliveryAddressSuccess());
      console.log('Error in editDeliveryAddressSaga', error?.response?.data);
      if (action?.meta?.callBack) {
        action?.meta?.callBack(error?.response?.data);
      }
    }
  }
}

function* getDeliveryByDateSaga(action: ReturnType<typeof getDeliveryByDate>) {
  try {
    const response: AxiosResponse = yield call(
      getDeliveryDetailsApi,
      action?.payload,
    );
    if (response?.data?.status) {
      yield put(getDeliveryByDateSuccess(response.data?.data));
    }
  } catch (error) {
    if (error instanceof AxiosError) {
      showErrorToast(error?.response?.data?.message || 'Error in get Delivery');
      yield put(getDeliveryByDateSuccess(undefined));
      console.log('Error in getDeliveryByDateSaga', error?.response?.data);
    }
  }
}
function* getDeliveryListSaga() {
  try {
    const response: AxiosResponse = yield call(getDeliveryListApi);
    if (response?.data?.status) {
      yield put(getDeliveryListSuccess(response.data?.data));
    }
  } catch (error) {
    if (error instanceof AxiosError) {
      showErrorToast(error?.response?.data?.message || 'Error in get Delivery');
      yield put(getDeliveryListSuccess([]));
      console.log('Error in getDeliveryList', error?.response?.data);
    }
  }
}
function* setMealLogSaga(action: ReturnType<typeof setMealLog>) {
  try {
    const response: AxiosResponse = yield call(setMealLogApi, action.payload);
    if (action?.meta?.callBack) {
      action?.meta?.callBack(response.data);
    }
  } catch (error) {
    if (error instanceof AxiosError) {
      if (action?.meta?.callBack) {
        action?.meta?.callBack(error?.response?.data);
      }
      console.log('Error in setMealLog', error?.response?.data);
    }
  }
}
function* restartDeliverySaga(action: ReturnType<typeof restartDelivery>) {
  try {
    const response: AxiosResponse = yield call(
      restartDeliveryApi,
      action.payload,
    );
    if (action?.meta?.callBack) {
      action?.meta?.callBack(response.data);
    }
  } catch (error) {
    if (error instanceof AxiosError) {
      if (action?.meta?.callBack) {
        action?.meta?.callBack(error?.response?.data);
      }
      console.log('Error in restartDelivery', error?.response?.data);
    }
  }
}
export function* watchDeliverySagas(): Generator {
  yield takeLatest(getWeekWiseDelivery, getWeekWiseDeliverySaga);
  yield takeLatest(editDeliveryAddress, editDeliveryAddressSaga);
  yield takeLatest(getDeliveryByDate.type, getDeliveryByDateSaga);
  yield takeLatest(getDeliveryList.type, getDeliveryListSaga);
  yield takeLatest(setMealLog.type, setMealLogSaga);
  yield takeLatest(restartDelivery.type, restartDeliverySaga);
}
