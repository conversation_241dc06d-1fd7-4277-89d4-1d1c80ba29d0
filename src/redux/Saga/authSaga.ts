import { AxiosError, AxiosResponse } from 'axios';
import { call, put, takeLatest } from 'redux-saga/effects';
import {
  getTranslationdataApi,
  getUserAddressByIdApi,
  getUserFromTokenApi,
  removeFcmTokenApi,
  saveFcmTokenApi,
  StoreUserAddressApi,
  updateUserAddressByIdApi,
} from '../../utils/api';
import { removeItemFromAsyncStorage } from '../../utils/functions';
import {
  getUserAddressById,
  removeFcmToken,
  saveFcmToken,
  StoreUserAddress,
  updateUserAddressById,
} from '../action/authAction';
import {
  getTranslationdata,
  getTranslationdataSuccess,
  getUserAddressByIdSuccess,
  getUserFromToken,
  getUserFromTokenSuccess,
  removeFcmTokenSuccess,
  saveFcmTokenSuccess,
  setToken,
  StoreUserAddressSuccess,
  updateUserAddressByIdSuccess,
} from '../slices/authSlice';
import { getCartDetailsByCustomerId } from '../slices/cartSlice';
import { getSubscriptionDetails } from '../slices/subscriptionSlices';

function* getUserFromTokenSaga() {
  try {
    const response: AxiosResponse = yield call(getUserFromTokenApi);
    if (response?.data?.data?.[0]) {
      yield put(getUserFromTokenSuccess(response?.data?.data?.[0]));
      yield put(getCartDetailsByCustomerId(response?.data?.data?.[0]?._id));
      if (response?.data?.data?.[0]?.user_register_flag == 'user_verified')
        yield put(getSubscriptionDetails());
    }
  } catch (error) {
    if (error instanceof AxiosError) {
      yield put(getUserFromTokenSuccess(undefined));
      console.error('Error in getUserFromTokenSaga', error?.response?.data);
      removeItemFromAsyncStorage('token');
      yield put(setToken(null));
    }
  }
}
function* getUserAddressByIdSaga(
  action: ReturnType<typeof getUserAddressById>,
) {
  try {
    const response: AxiosResponse = yield call(
      getUserAddressByIdApi,
      action?.payload,
    );
    if (response.data) {
      yield put(getUserAddressByIdSuccess(response.data?.data));
    }
  } catch (error) {
    if (error instanceof AxiosError) {
      yield put(getUserAddressByIdSuccess(undefined));
      console.error('Error in User Address By Id', error?.response?.data);
    }
  }
}
function* StoreUserAddressSaga(action: ReturnType<typeof StoreUserAddress>) {
  try {
    const response: AxiosResponse = yield call(
      StoreUserAddressApi,
      action?.payload,
    );
    if (response.data) {
      yield put(StoreUserAddressSuccess(response.data?.data));
      yield put(getUserFromToken());
      if (action.meta.callBack) {
        action.meta.callBack(response.data);
      }
    }
  } catch (error) {
    if (error instanceof AxiosError) {
      yield put(StoreUserAddressSuccess(undefined));
      if (action.meta.callBack) {
        action.meta.callBack(error?.response?.data);
      }
      console.log('Error in StoreUserAddress', error?.response?.data);
    }
  }
}
function* updateUserAddressByIdSaga(
  action: ReturnType<typeof updateUserAddressById>,
) {
  try {
    const response: AxiosResponse = yield call(
      updateUserAddressByIdApi,
      action?.payload.id,
      action?.payload?.values,
    );
    if (response.data) {
      yield put(updateUserAddressByIdSuccess(response.data?.data));
      yield put(getUserFromToken());
      if (action.meta.callBack) {
        action.meta.callBack(response.data);
      }
    }
  } catch (error) {
    if (error instanceof AxiosError) {
      yield put(updateUserAddressByIdSuccess(undefined));
      if (action.meta.callBack) {
        action.meta.callBack(error?.response?.data);
      }
      console.log('Error in updateUserAddressById', error?.response?.data);
    }
  }
}
function* saveFcmTokenSaga(action: ReturnType<typeof saveFcmToken>) {
  try {
    const response: AxiosResponse = yield call(
      saveFcmTokenApi,
      action?.payload,
    );
    if (response.data) {
      yield put(saveFcmTokenSuccess(response.data?.data));
      if (action.meta.callBack) {
        action.meta.callBack(response.data);
      }
    }
  } catch (error) {
    if (error instanceof AxiosError) {
      yield put(saveFcmTokenSuccess(undefined));
      if (action.meta.callBack) {
        action.meta.callBack(error?.response?.data);
      }
      console.error('Error in saveFcmToken', error?.response?.data);
    }
  }
}
function* removeFcmTokenSaga(action: ReturnType<typeof removeFcmToken>) {
  try {
    const response: AxiosResponse = yield call(
      removeFcmTokenApi,
      action?.payload,
    );
    if (response.data) {
      yield put(removeFcmTokenSuccess(response.data?.data));
      if (action.meta.callBack) {
        action.meta.callBack(response.data);
      }
    }
  } catch (error) {
    if (error instanceof AxiosError) {
      yield put(removeFcmTokenSuccess(undefined));
      if (action.meta.callBack) {
        action.meta.callBack(error?.response?.data);
      }
      console.error('Error in removeFcmToken', error?.response?.data);
    }
  }
}
function* getTranslationdataSaga() {
  try {
    const response: AxiosResponse = yield call(getTranslationdataApi);
    if (response.data) {
      yield put(getTranslationdataSuccess(response.data?.data));
    }
  } catch (error) {
    if (error instanceof AxiosError) {
      yield put(getTranslationdataSuccess(undefined));
      console.error('Error in getTranslationdata', error?.response?.data);
    }
  }
}
export function* watchAuthSagas(): Generator {
  yield takeLatest(getUserFromToken, getUserFromTokenSaga);
  yield takeLatest(getUserAddressById, getUserAddressByIdSaga);
  yield takeLatest(StoreUserAddress, StoreUserAddressSaga);
  yield takeLatest(updateUserAddressById, updateUserAddressByIdSaga);
  yield takeLatest(saveFcmToken, saveFcmTokenSaga);
  yield takeLatest(removeFcmToken, removeFcmTokenSaga);
  yield takeLatest(getTranslationdata, getTranslationdataSaga);
}
