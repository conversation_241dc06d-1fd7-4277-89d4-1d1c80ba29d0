import { AxiosError, AxiosResponse } from 'axios';
import { call, put, takeLatest } from 'redux-saga/effects';
import {
  getweeklyRecipesApi,
  setMacroCalculationApi,
  setRecipeRatingApi,
} from '../../utils/api';
import {
  getweeklyRecipes,
  setMacroCalculation,
  setRecipeRating,
} from '../action/recipesActions';
import { getweeklyRecipesSuccess } from '../slices/recipesSlice';

function* setRecipeRatingSaga(action: ReturnType<typeof setRecipeRating>) {
  try {
    const response: AxiosResponse = yield call(
      setRecipeRatingApi,
      action.payload,
    );
    if (action?.meta?.callBack) {
      action?.meta?.callBack(response.data);
    }
  } catch (error) {
    if (error instanceof AxiosError) {
      if (action?.meta?.callBack) {
        action?.meta?.callBack(error?.response?.data);
      }
      console.error('Error in setRecipeRating', error?.response?.data);
    }
  }
}

function* setMacroCalculationSaga(
  action: ReturnType<typeof setMacroCalculation>,
) {
  try {
    const response: AxiosResponse = yield call(
      setMacroCalculationApi,
      action.payload,
    );
    if (action?.meta?.callBack) {
      action?.meta?.callBack(response.data);
    }
  } catch (error) {
    if (error instanceof AxiosError) {
      if (action?.meta?.callBack) {
        action?.meta?.callBack(error?.response?.data);
      }
      console.log('Error in setMacroCalculation', error?.response?.data);
    }
  }
}
function* getweeklyRecipesSaga(action: ReturnType<typeof getweeklyRecipes>) {
  try {
    const response: AxiosResponse = yield call(
      getweeklyRecipesApi,
      action.payload,
    );
    if (response?.data?.status) {
      yield put(getweeklyRecipesSuccess(response?.data?.data));
    }
  } catch (error) {
    if (error instanceof AxiosError) {
      yield put(getweeklyRecipesSuccess(error?.response?.data?.data));
      console.log('Error in getweeklyRecipes', error?.response?.data);
    }
  }
}
export function* watchRecipeSagas(): Generator {
  yield takeLatest(getweeklyRecipes.type, getweeklyRecipesSaga);
  yield takeLatest(setRecipeRating.type, setRecipeRatingSaga);
  yield takeLatest(setMacroCalculation.type, setMacroCalculationSaga);
}
