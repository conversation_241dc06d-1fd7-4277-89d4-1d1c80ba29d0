import { all } from 'redux-saga/effects';
import { watchAppSagas } from './appSaga';
import { watchAuthSagas } from './authSaga';
import { watchDeliverySagas } from './deliverySaga';
import { watchRecipeSagas } from './recipesSaga';
import { watchSubscriptionSagas } from './subscriptionSaga';
export default function* rootSaga() {
  yield all([
    watchAppSagas(),
    watchAuthSagas(),
    watchSubscriptionSagas(),
    watchDeliverySagas(),
    watchRecipeSagas(),
  ]);
}
