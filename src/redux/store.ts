import { configureStore } from '@reduxjs/toolkit';
import createSagaMiddleware from 'redux-saga';
import { removeFcmToken, saveFcmToken } from './action/authAction';
import { setMealLog } from './action/deliveryActions';
import { setMacroCalculation, setRecipeRating } from './action/recipesActions';
import { renewSubscription } from './action/subscriptionAction';
import rootSaga from './Saga';
import rootReducer from './slices';

const sagaMiddleware = createSagaMiddleware();
const store = configureStore({
  reducer: rootReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [
          'delivery/editDeliveryAddress',
          'auth/getUserAddressById',
          'delivery/getWeekWiseDelivery',
          'auth/updateUserAddressById',
          'auth/StoreUserAddress',
          saveFcmToken.type,
          removeFcmToken.type,
          setMealLog.type,
          setRecipeRating.type,
          setMacroCalculation.type,
          renewSubscription.type,
        ],
      },
    }).concat(sagaMiddleware),
});
sagaMiddleware.run(rootSaga);
export default store;
export type RootState = ReturnType<typeof rootReducer>;
