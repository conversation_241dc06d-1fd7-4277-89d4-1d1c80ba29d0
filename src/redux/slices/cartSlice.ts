import { createSlice } from '@reduxjs/toolkit';
import { cartSlicesProps } from 'cart-slice';

const initialState: cartSlicesProps = {
  loader: false,
  cartDetails: undefined,
};
const cartSlices = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    getCartDetailsByCustomerId: (state, { payload }) => {
      state.loader = true;
    },
    getCartDetailsByCustomerIdSuccess: (state, { payload }) => {
      state.loader = false;
      state.cartDetails = payload;
    },
  },
});

export const { getCartDetailsByCustomerId, getCartDetailsByCustomerIdSuccess } =
  cartSlices.actions;
export default cartSlices.reducer;
