import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ThemeState } from 'theme-actions';
import { darkTheme, lightTheme } from '../../theme/colors';

const initialState: ThemeState = {
  currentTheme: 'light',
  colors: {
    light: lightTheme,
    dark: darkTheme,
  },
};

const themeSlice = createSlice({
  name: 'theme',
  initialState,
  reducers: {
    toggleTheme: (state) => {
      state.currentTheme = state.currentTheme === 'light' ? 'dark' : 'light';
    },
    setTheme(state, action: PayloadAction<'light' | 'dark'>) {
      const selectedTheme = action.payload;
      state.currentTheme = selectedTheme;
    },
  },
});

export const { toggleTheme, setTheme } = themeSlice.actions;
export default themeSlice.reducer;
