import { createSlice } from '@reduxjs/toolkit';
import { recipesSlicesProps } from 'recipe-slice';
import {
  getweeklyRecipes,
  setMacroCalculation,
} from '../action/recipesActions';

const initialState: recipesSlicesProps = {
  loaders: [],
  weeklyRecipes: undefined,
};
const recipesSlices = createSlice({
  name: 'recipes',
  initialState,
  reducers: {
    macroCalculationSuccess: (state) => {
      state.loaders = state.loaders.filter(
        (e) => e !== setMacroCalculation.type,
      );
    },
    getweeklyRecipesSuccess: (state, { payload }) => {
      state.loaders = state.loaders.filter((e) => e !== getweeklyRecipes.type);
      state.weeklyRecipes = payload;
    },
    cleanweeklyRecipes: (state) => {
      state.weeklyRecipes = undefined;
    },
  },
  extraReducers(builder) {
    builder.addCase(getweeklyRecipes, (state) => {
      return {
        ...state,
        loaders: [...state.loaders, ...[getweeklyRecipes.type]],
        weeklyRecipes: undefined,
      };
    });
    builder.addCase(setMacroCalculation, (state) => {
      return {
        ...state,
        loaders: [...state.loaders, ...[setMacroCalculation.type]],
      };
    });
  },
});

export const {
  macroCalculationSuccess,
  cleanweeklyRecipes,
  getweeklyRecipesSuccess,
} = recipesSlices.actions;
export default recipesSlices.reducer;
