import { combineReducers } from 'redux';
import appSlice from './appSlice';
import authSlice from './authSlice';
import cartSlice from './cartSlice';
import deliverySlices from './deliverySlice';
import recipesSlices from './recipesSlice';
import subscriptionSlices from './subscriptionSlices';
import themeReducer from './themeSlice';
const rootReducer = combineReducers({
  theme: themeReducer,
  auth: authSlice,
  subscription: subscriptionSlices,
  cart: cartSlice,
  app: appSlice,
  delivery: deliverySlices,
  recipe: recipesSlices,
});
export default rootReducer;
