import { createSlice } from '@reduxjs/toolkit';
import { DeliveryListProps, DeliverySlicesProps } from 'delivery-slice';
import moment from 'moment';
import { getNearestFutureDate } from '../../utils/functions';
import {
  editDeliveryAddress,
  getDeliveryByDate,
  getWeekWiseDelivery,
} from '../action/deliveryActions';
import { logout } from './appSlice';

const initialState: DeliverySlicesProps = {
  loader: false,
  deliverybyDate: undefined,
  deliveryList: [],
  selectedDate: moment().format('YYYY-MM-DD'),
  weekDelivryList: [],
  listLoader: [],
};
const deliverySlices = createSlice({
  name: 'delivery',
  initialState,
  reducers: {
    clanDeliveryByDate: (state) => {
      state.deliverybyDate = undefined;
    },
    getDeliveryByDateSuccess: (state, { payload }) => {
      state.listLoader = state.listLoader.filter(
        (e) => e !== getDeliveryByDate.type,
      );
      state.deliverybyDate = payload;
    },
    getDeliveryList: (state) => {
      state.loader = false;
      state.deliveryList = [];
    },
    getDeliveryListSuccess: (
      state,
      { payload }: { payload: DeliveryListProps[] },
    ) => {
      state.loader = false;
      state.deliveryList = payload;
      if (
        !payload.find(
          (e) =>
            moment(e.delivery_date).format('YYYY-MM-DD') === state.selectedDate,
        )
      ) {
        state.selectedDate = moment(
          getNearestFutureDate(
            payload?.map((e) => moment(e.delivery_date).format('YYYY-MM-DD')),
          ),
        ).format('YYYY-MM-DD');
      }
    },
    setSelectedDate: (state, { payload }) => {
      state.selectedDate = payload;
    },
    getWeekWiseDeliverySuccess: (state, { payload }) => {
      state.loader = false;
      state.weekDelivryList = payload;
    },
    clearWeekWiseDelivery: (state) => {
      state.weekDelivryList = [];
    },
    editDeliveryAddressSuccess: (state) => {
      state.loader = false;
    },
    cleargetDeliveryList: (state) => {
      state.deliveryList = [];
    },
    restatrDeliverySuccess: (state) => {
      state.loader = false;
    },
    freezeDeliverySuccess: (state) => {
      state.loader = false;
    },
    editDeliveryRecipeSuccess: (state) => {
      state.loader = false;
    },
  },
  extraReducers(builder) {
    builder.addCase(getDeliveryByDate, (state) => {
      return {
        ...state,
        listLoader: [...state.listLoader, getDeliveryByDate.type],
        deliverybyDate: undefined,
      };
    });
    builder.addCase(logout, () => initialState);
    builder.addCase(editDeliveryAddress, (old) => ({ ...old, loader: true }));
    builder.addCase(getWeekWiseDelivery, (old) => ({ ...old, loader: true }));
  },
});

export const {
  getWeekWiseDeliverySuccess,
  clearWeekWiseDelivery,
  editDeliveryAddressSuccess,
  clanDeliveryByDate,
  getDeliveryByDateSuccess,
  getDeliveryList,
  getDeliveryListSuccess,
  setSelectedDate,
  cleargetDeliveryList,
  editDeliveryRecipeSuccess,
  freezeDeliverySuccess,
  restatrDeliverySuccess,
} = deliverySlices.actions;
export default deliverySlices.reducer;
