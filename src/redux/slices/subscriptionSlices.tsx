import { createSlice } from '@reduxjs/toolkit';
import { subscriptionSlicesProps } from 'subscription-slice';

const initialState: subscriptionSlicesProps = {
  loader: false,
  subscriptionDetails: [],
  priceData: {},
};
const subscriptionSlices = createSlice({
  name: 'subscription',
  initialState,
  reducers: {
    getSubscriptionDetails: (state) => {
      state.loader = true;
    },
    getSubscriptionDetailsSuccess: (state, { payload }) => {
      state.loader = false;
      state.subscriptionDetails = payload;
    },
    getSubscriptionPrice: (state) => {
      state.loader = true;
    },
    getSubscriptionPriceSuccess: (state, { payload }) => {
      state.loader = false;
      state.priceData = payload;
    },
  },
});
export const {
  getSubscriptionDetails,
  getSubscriptionDetailsSuccess,
  getSubscriptionPrice,
  getSubscriptionPriceSuccess,
} = subscriptionSlices.actions;
export default subscriptionSlices.reducer;
