import { createSlice } from '@reduxjs/toolkit';
import { subscriptionSlicesProps } from 'subscription-slice';
import {
  createOrder,
  getAvailableDeliveryData,
  renewSubscription,
} from '../action/subscriptionAction';
import { logout } from './appSlice';

const initialState: subscriptionSlicesProps = {
  loader: false,
  subscriptionDetails: [],
  priceData: undefined,
  availableDate: undefined,
};
const subscriptionSlices = createSlice({
  name: 'subscription',
  initialState,
  reducers: {
    getSubscriptionDetails: (state) => {
      state.loader = true;
    },
    getSubscriptionDetailsSuccess: (state, { payload }) => {
      state.loader = false;
      state.subscriptionDetails = payload;
    },
    getSubscriptionPrice: (state) => {
      state.loader = true;
    },
    getSubscriptionPriceSuccess: (state, { payload }) => {
      state.loader = false;
      state.priceData = payload;
    },
    clearSubscriptionPrice: (state) => {
      state.priceData = undefined;
    },
    renewSubscriptionSuccess: (state) => {
      state.loader = false;
    },
    getAvailableDeliveryDataSuccess: (state, { payload }) => {
      state.loader = false;
      state.availableDate = payload;
    },
    createOrderSuccess: (state) => {
      state.loader = false;
    },
    editIngredientsSuccess: (state) => {
      state.loader = false;
    },
  },
  extraReducers(builder) {
    builder.addCase(logout, () => initialState);
    builder.addCase(renewSubscription, (old) => ({ ...old, loader: true }));
    builder.addCase(createOrder, (old) => ({ ...old, loader: true }));
    builder.addCase(getAvailableDeliveryData, (old) => ({
      ...old,
      availableDate: undefined,
      loader: true,
    }));
  },
});
export const {
  editIngredientsSuccess,
  getSubscriptionDetails,
  getSubscriptionDetailsSuccess,
  getSubscriptionPrice,
  getSubscriptionPriceSuccess,
  clearSubscriptionPrice,
  renewSubscriptionSuccess,
  getAvailableDeliveryDataSuccess,
  createOrderSuccess,
} = subscriptionSlices.actions;
export default subscriptionSlices.reducer;
