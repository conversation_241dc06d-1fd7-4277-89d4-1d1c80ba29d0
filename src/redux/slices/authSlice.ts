import { createSlice } from '@reduxjs/toolkit';
import { authSliceProps } from 'auth-actions';
import { INIT_translation } from '../../../@types/translation';
import {
  getUserAddressById,
  StoreUserAddress,
  updateUserAddressById,
} from '../action/authAction';
import { logout } from './appSlice';

const initialState: authSliceProps = {
  translation: INIT_translation,
  loader: false,
  authUser: undefined,
  tokenLoader: false,
  token: undefined,
  addressByID: undefined,
  loyaltyTransactionList: [],
  loyaltyTransactionData: undefined,
};
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setToken: (state, { payload }) => {
      state.token = payload;
    },
    getUserFromToken: (state) => {
      state.tokenLoader = true;
    },
    getUserFromTokenSuccess: (state, { payload }) => {
      state.tokenLoader = false;
      state.authUser = payload;
    },
    saveFcmTokenSuccess: (state) => {
      state.loader = false;
    },
    removeFcmTokenSuccess: (state) => {
      state.loader = false;
    },
    getTranslationdata: () => {},
    getTranslationdataSuccess: (state, { payload }) => {
      state.translation = { ...state.translation, ...payload };
    },
    getUserAddressByIdSuccess: (state, { payload }) => {
      state.loader = false;
      state.addressByID = payload;
    },
    clearddressById: (state) => {
      state.addressByID = undefined;
    },
    StoreUserAddressSuccess: (state) => {
      state.loader = false;
    },
    updateUserAddressByIdSuccess: (state) => {
      state.loader = false;
    },
  },
  extraReducers(builder) {
    builder.addCase(logout, (state) => {
      return {
        ...state,
        loader: false,
        authUser: undefined,
        tokenLoader: false,
        token: undefined,
        addressByID: undefined,
      };
    });
    builder.addCase(getUserAddressById, (state) => {
      return {
        ...state,
        loader: true,
        addressByID: undefined,
      };
    });
    builder.addCase(StoreUserAddress, (state) => {
      return {
        ...state,
        loader: true,
      };
    });
    builder.addCase(updateUserAddressById, (state) => {
      return {
        ...state,
        loader: true,
      };
    });
  },
});

export const {
  removeFcmTokenSuccess,
  saveFcmTokenSuccess,
  getUserFromToken,
  getUserFromTokenSuccess,
  setToken,
  getTranslationdata,
  getTranslationdataSuccess,
  getUserAddressByIdSuccess,
  StoreUserAddressSuccess,
  clearddressById,
  updateUserAddressByIdSuccess,
} = authSlice.actions;
export default authSlice.reducer;
