import { createAction } from '@reduxjs/toolkit';
import { BodyMetricsPayload, RecipeRatingPayload } from 'actions';
import { ActionMeta } from 'auth-actions';

export const setRecipeRating = createAction(
  'recipes/setRecipeRating',
  (
    payload: RecipeRatingPayload,
    callBack: ActionMeta<undefined>['callBack'],
  ) => {
    return {
      payload: payload,
      meta: { callBack: callBack },
    };
  },
);

export const setMacroCalculation = createAction(
  'recipes/setMacroCalculation',
  (
    payload: BodyMetricsPayload,
    callBack: ActionMeta<undefined>['callBack'],
  ) => {
    return {
      payload: payload,
      meta: { callBack: callBack },
    };
  },
);

export const getweeklyRecipes = createAction(
  'recipes/getweeklyRecipes',
  (payload?: string) => {
    return {
      payload: payload,
    };
  },
);
