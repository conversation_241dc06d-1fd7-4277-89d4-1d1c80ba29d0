import { createAction } from '@reduxjs/toolkit';
import { AddEditAddressPayload, FcmTokenPayload } from 'actions';
import { ActionMeta } from 'auth-actions';

export const getUserAddressById = createAction(
  'auth/getUserAddressById',
  (payload: string) => {
    return {
      payload: payload,
    };
  },
);
export const StoreUserAddress = createAction(
  'auth/StoreUserAddress',
  (
    payload: AddEditAddressPayload,
    callBack: ActionMeta<undefined>['callBack'],
  ) => {
    return {
      payload: payload,
      meta: { callBack: callBack },
    };
  },
);
export const updateUserAddressById = createAction(
  'auth/updateUserAddressById',
  (
    id: string,
    values: AddEditAddressPayload,
    callBack: ActionMeta<undefined>['callBack'],
  ) => {
    return {
      payload: {
        id,
        values,
      },
      meta: { callBack: callBack },
    };
  },
);
export const saveFcmToken = createAction(
  'auth/saveFcmToken',
  (payload: FcmTokenPayload, callBack: ActionMeta<undefined>['callBack']) => {
    return {
      payload: payload,
      meta: { callBack: callBack },
    };
  },
);
export const removeFcmToken = createAction(
  'auth/removeFcmToken',
  (payload: FcmTokenPayload, callBack: ActionMeta<undefined>['callBack']) => {
    return {
      payload: payload,
      meta: { callBack: callBack },
    };
  },
);
