import { createAction } from '@reduxjs/toolkit';
import {
  editDeliveryAddressPayload,
  getWeekWiseDeliveryPayload,
  RestartDeliveryPayload,
  SetMealLogPayload,
} from 'actions';
import { ActionMeta } from 'auth-actions';

export const getDeliveryByDate = createAction(
  'delivery/getDeliveryByDate',
  (data: string, customer_id: string) => {
    return {
      payload: {
        date: data,
        customer_id: customer_id,
      },
    };
  },
);
export const setMealLog = createAction(
  'delivery/setMealLog',
  (payload: SetMealLogPayload, callBack: ActionMeta<undefined>['callBack']) => {
    return {
      payload: payload,
      meta: { callBack: callBack },
    };
  },
);

export const editDeliveryAddress = createAction(
  'delivery/editDeliveryAddress',
  (
    payload: editDeliveryAddressPayload,
    callBack: ActionMeta<undefined>['callBack'],
  ) => {
    return {
      payload: payload,
      meta: { callBack: callBack },
    };
  },
);
export const restartDelivery = createAction(
  'delivery/RestartDelivery',
  (
    payload: RestartDeliveryPayload,
    callBack: ActionMeta<string>['callBack'],
  ) => {
    return {
      payload: payload,
      meta: { callBack: callBack },
    };
  },
);
export const getWeekWiseDelivery = createAction(
  'delivery/getWeekWiseDelivery',
  (payload: getWeekWiseDeliveryPayload) => {
    return {
      payload: payload,
    };
  },
);
