import { createAction } from '@reduxjs/toolkit';
import {
  createOrderPayload,
  getAvailableDeliveryPayload,
  renewSubscriptionPayload,
} from 'actions';
import { ActionMeta } from 'auth-actions';
import { cartDetailsProps } from 'cart-slice';

export const renewSubscription = createAction(
  'subscription/renewSubscription',
  (
    payload: renewSubscriptionPayload,
    callBack: ActionMeta<cartDetailsProps>['callBack'],
  ) => {
    return {
      payload: payload,
      meta: { callBack: callBack },
    };
  },
);
export const getAvailableDeliveryData = createAction(
  'subscription/getAvailableDeliveryData',
  (payload: getAvailableDeliveryPayload) => {
    return {
      payload: payload,
    };
  },
);
export const createOrder = createAction(
  'subscription/createOrder',
  (
    payload: createOrderPayload,
    callBack: ActionMeta<{
      transaction: {
        id: string;
        payment_session_secret: string;
        payment_session_token: string;
      };
      delivery_start_date_validation: boolean;
    }>['callBack'],
  ) => {
    return {
      payload: payload,
      meta: { callBack: callBack },
    };
  },
);
