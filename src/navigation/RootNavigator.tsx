import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import React from 'react';
import { View } from 'react-native';
import { RootStackParamList, RootTabParamList } from '../../@types/navigation';
import Header from '../components/Header/Header';
import MyTabBar from '../components/TabNavigator/TabBar';
import { AddEditAddressProvider } from '../FormContext/AddressContext';
import OrderSummary from '../screens/Both/OrderSummary';
import Account from '../screens/Users/<USER>/Account';
import AddNewAddress from '../screens/Users/<USER>/AddNewAddress';
import DeliveryDetails from '../screens/Users/<USER>/DeliveryDetails';
import MealPlan from '../screens/Users/<USER>';
import SettingScreen from '../screens/Users/<USER>/SettingScreen';
import { globalStyles } from '../theme/styles/globalStyles';
const Stack = createStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<RootTabParamList>();
function StackNavigator() {
  return (
    <Stack.Navigator initialRouteName="Account">
      <Stack.Screen
        name="MealPlan"
        component={MealPlan}
        options={{
          headerShown: true,
          header: () => <Header headerTitle="Build your plan" />,
        }}
      />
      <Stack.Screen
        name="OrderSummary"
        component={OrderSummary}
        options={{
          headerShown: true,
          header: () => <Header headerTitle="Order Summary" />,
        }}
      />
      <Stack.Screen
        name="Account"
        component={Account}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="AddNewAddress"
        options={{
          headerShown: false,
        }}
      >
        {(props) => (
          <View style={globalStyles.flex1}>
            <AddEditAddressProvider>
              <AddNewAddress {...props} />
            </AddEditAddressProvider>
          </View>
        )}
      </Stack.Screen>
      <Stack.Screen
        name="Settings"
        component={SettingScreen}
        options={{
          headerShown: true,
          header: () => <Header headerTitle="My Account" showDivider={false} />,
        }}
      />
      <Stack.Screen
        name="DeliveryDetails"
        component={DeliveryDetails}
        options={{
          headerShown: true,
          header: () => <Header headerTitle="Delivery details" />,
        }}
      />
    </Stack.Navigator>
  );
}
function RootNavigator() {
  return (
    <Tab.Navigator
      tabBar={(props) => <MyTabBar {...props} />}
      screenOptions={{
        headerShown: false,
      }}
    >
      <Tab.Screen name="Main" component={StackNavigator} />
    </Tab.Navigator>
  );
}

export default RootNavigator;
