import { NavigationProp, useNavigation } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import React from 'react';
import { RootGuestStackParamList } from '../../@types/navigation';
import LoginScreen from '../screens/Guest/LoginScreen';
import { LightTheme } from '../theme/colors';

const Stack = createStackNavigator<RootGuestStackParamList>();

const GuestNavigator = () => {
  const theme = LightTheme;
  const navigation = useNavigation<NavigationProp<RootGuestStackParamList>>();
  return (
    <Stack.Navigator
      initialRouteName="Login"
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen
        name="Login"
        component={LoginScreen}
        options={{
          headerShown: false,
        }}
        initialParams={{ theme, navigation }}
      />
    </Stack.Navigator>
  );
};

export default GuestNavigator;
