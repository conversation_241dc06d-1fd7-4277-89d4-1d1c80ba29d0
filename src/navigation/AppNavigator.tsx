/* eslint-disable react-native/split-platform-components */
import messaging from '@react-native-firebase/messaging';
import { GoogleSignin } from '@react-native-google-signin/google-signin';
import {
  createNavigationContainerRef,
  NavigationContainer,
} from '@react-navigation/native';
import CleverTap from 'clevertap-react-native';
import React, { useCallback, useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Keyboard,
  PermissionsAndroid,
  Platform,
  StatusBar,
  Text,
  View,
} from 'react-native';
import Config from 'react-native-config';
import Geolocation from 'react-native-geolocation-service';
import { SafeAreaView } from 'react-native-safe-area-context';
import Toast, { BaseToastProps } from 'react-native-toast-message';
import { useDispatch, useSelector } from 'react-redux';
import { RootStackParamList, RootTabParamList } from '../../@types/navigation';
import { ErrorToastView, SuccessToastView } from '../components/Toast';
import { saveFcmToken } from '../redux/action/authAction';
import {
  getAllCity,
  getIngredientData,
  getMasterData,
  setKeyboardState,
  setLanguage,
} from '../redux/slices/appSlice';
import {
  getTranslationdata,
  getUserFromToken,
  setToken,
} from '../redux/slices/authSlice';
import { getDeliveryList } from '../redux/slices/deliverySlice';
import { getSubscriptionPrice } from '../redux/slices/subscriptionSlices';
import { RootState } from '../redux/store';
import { useTheme } from '../theme';
import { globalStyles } from '../theme/styles/globalStyles';
import { linking } from '../utils/DeepLinking';
import { checkGoogleSignIn } from '../utils/fitnessFunctions';
import {
  formatDateAsEpoch,
  getDataFromAsyncStorage,
  showErrorToast,
} from '../utils/functions';
import FitnessNavigator from './FitnessNavigator';
import GuestNavigator from './GuestNavigator';
import RootNavigator from './RootNavigator';

const navigationRef = createNavigationContainerRef<RootTabParamList>();
export function getCurrentRouteName() {
  return navigationRef.isReady()
    ? (navigationRef.getCurrentRoute()?.name as keyof RootStackParamList)
    : null;
}
const toastConfig = {
  success: (props: React.JSX.IntrinsicAttributes & BaseToastProps) => (
    <SuccessToastView {...props} />
  ),
  error: (props: React.JSX.IntrinsicAttributes & BaseToastProps) => (
    <ErrorToastView {...props} />
  ),
};

const onStateChange = () => {
  const currentScreen = getCurrentRouteName() as string;

  if (currentScreen) {
    CleverTap.recordScreenView(currentScreen);
    CleverTap.recordEvent('Screen View', { 'Screen Name': currentScreen });
  }
};
const AppNavigator = () => {
  const [loader, setLoader] = useState(true);
  const [refresh, setRefreshToken] = useState(false);
  const [isFitnessUser, setFitnessUser] = useState(false);
  const { token, authUser } = useSelector((state: RootState) => state.auth);
  const { lanKey } = useSelector((state: RootState) => state.app);
  const dispatch = useDispatch();
  const color = useTheme();
  const getFitnessUser = useCallback(async () => {
    const isSignedIn = await checkGoogleSignIn();
    if (isSignedIn) {
      setFitnessUser(true);
    } else {
      setFitnessUser(false);
    }
    if (refresh) {
      setRefreshToken(false);
    }
  }, [refresh]);
  const getFCMToken = useCallback(async () => {
    try {
      const token = await messaging().getToken();
      if (token) {
        CleverTap.setFCMPushToken(token);
        dispatch(
          saveFcmToken({ fcm_token: [`${token}`] }, (res) => {
            if (res?.status) {
              /* empty */
            } else {
              console.error('res error >>>', res);
            }
          }),
        );
      }
    } catch (error) {
      console.error('Error fetching FCM token:', error);
    }
  }, [dispatch]);

  const requestLocationPermission = useCallback(async () => {
    try {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        {
          title: 'Location Permission',
          message: 'App needs access to your location to trigger geofences',
          buttonPositive: 'OK',
        },
      );
      if (granted === PermissionsAndroid.RESULTS.GRANTED) {
        console.log('Location permission granted');

        Geolocation.getCurrentPosition(
          (position) => {
            const { latitude, longitude } = position.coords;
            CleverTap.setLocation(latitude, longitude);
          },
          (error) => console.log(error),
          {
            enableHighAccuracy: true,
            timeout: 20000,
            maximumAge: 1000,
            forceRequestLocation: true,
            showLocationDialog: true,
          },
        );
      } else {
        console.log('Location permission denied');
      }
    } catch (err) {
      console.warn(err);
    }
  }, []);

  const requestFCMPermission = useCallback(async () => {
    if (Platform.OS == 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
        );
        if (granted === PermissionsAndroid.RESULTS.GRANTED) {
          getFCMToken();
          console.log('Notification permission granted');
        } else {
          console.log('Notification permission denied', granted);
        }
      } catch (error) {
        console.warn('Error requesting notification permission:', error);
      }
    } else {
      const authStatus = await messaging().requestPermission();
      const enabled =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;

      if (enabled) {
        getFCMToken();
      } else {
        showErrorToast('Enable notifications in settings.');
      }
    }
  }, [getFCMToken]);

  useEffect(() => {
    getDataFromAsyncStorage('token').then((data) => {
      setTimeout(() => {
        setLoader(false);
      }, 10);
      if (data) {
        dispatch(getUserFromToken());
        dispatch(setToken(data));
        dispatch(getDeliveryList());
      }
    });
    dispatch(getSubscriptionPrice());
    dispatch(getIngredientData());
    dispatch(getMasterData());
    dispatch(getAllCity());
  }, [dispatch]);

  useEffect(() => {
    const show = Keyboard.addListener('keyboardDidShow', () => {
      dispatch(setKeyboardState(true));
    });
    const Hide = Keyboard.addListener('keyboardDidHide', () => {
      dispatch(setKeyboardState(false));
    });
    getDataFromAsyncStorage('language').then((data) => {
      setTimeout(() => {
        setLoader(false);
      }, 10);
      if (data) {
        dispatch(setLanguage(data));
      }
    });
    CleverTap.addListener(
      CleverTap.CleverTapInAppNotificationButtonTapped,
      (event: string) => {
        handleCleverTapEvent(
          CleverTap.CleverTapInAppNotificationButtonTapped,
          event,
        );
      },
    );
    CleverTap.addListener(CleverTap.CleverTapPushNotificationClicked, () => {
      return null;
    });
    return () => {
      CleverTap.removeListener(
        CleverTap.CleverTapInAppNotificationButtonTapped,
      );
      CleverTap.removeListener(CleverTap.CleverTapPushNotificationClicked);
      show.remove();
      Hide.remove();
    };
  }, [dispatch]);
  useEffect(() => {
    if (authUser?._id && token) {
      requestFCMPermission();
      requestLocationPermission();
      // getHuaweiPushToken();
      CleverTap.profileSet({
        Name: authUser?.first_name + ' ' + authUser?.last_name,
        'First Name': authUser?.first_name,
        'Last Name': authUser?.last_name,
        Identity: authUser?._id,
        Email: authUser?.email,
        Phone: authUser?.whatsapp_country_code + authUser?.whatsapp_number,
        Timezone: new Date().getTimezoneOffset(),
        Country: authUser?.country,
        City: authUser?.city,
        Area: authUser?.province,
        Location:
          authUser?.full_address +
          ', ' +
          authUser?.province +
          ', ' +
          authUser?.city +
          ', ' +
          authUser?.country,
        'Preferred language ( OS )':
          Intl.DateTimeFormat().resolvedOptions().locale,
        Delicoins: authUser?.reward_wallet,
        Referrer: authUser?.referred_users?.length > 0 ? 'yes' : 'no',
        'Number of orders till date': authUser?.total_orders,
        'Revenue contributed till date': authUser?.total_spent,
        'Recent visit date': formatDateAsEpoch(new Date()),
        RegistrationStartDate: formatDateAsEpoch(authUser?.createdAt),
        REFERAL_CODE: authUser?.referral_code,
      });
    }
  }, [
    authUser?._id,
    authUser?.city,
    authUser?.country,
    authUser?.createdAt,
    authUser?.email,
    authUser?.first_name,
    authUser?.full_address,
    authUser?.last_name,
    authUser?.province,
    authUser?.referral_code,
    authUser?.referred_users?.length,
    authUser?.reward_wallet,
    authUser?.total_orders,
    authUser?.total_spent,
    authUser?.whatsapp_country_code,
    authUser?.whatsapp_number,
    requestFCMPermission,
    requestLocationPermission,
    token,
  ]);

  const handleCleverTapEvent = (eventName: string, event: string) => {
    console.log('CleverTap Event called - ', eventName, event);
  };
  useEffect(() => {
    dispatch(getTranslationdata());
  }, [dispatch, lanKey]);

  useEffect(() => {
    GoogleSignin.configure({
      webClientId: Config.WEB_CLIENT_ID,
      iosClientId: Config.IOS_CLIENT_ID,
      offlineAccess: true,
    });
    getFitnessUser();
  }, [refresh]);
  return (
    <NavigationContainer
      ref={navigationRef}
      linking={linking}
      onStateChange={onStateChange}
      fallback={
        <View style={{ backgroundColor: '#FFFFFF', flex: 1 }}>
          <Text>{'loading...'}</Text>
        </View>
      }
      onUnhandledAction={(v: {
        type: string;
        payload?: { params?: { tokenRefresher?: string } } | null | undefined;
        source?: string;
        target?: string;
      }) => {
        if (v?.payload?.params?.tokenRefresher === 'tokenRefresh') {
          setRefreshToken(true);
        }
      }}
    >
      <SafeAreaView
        style={[
          globalStyles.container,
          { backgroundColor: color.primary_cream },
        ]}
        edges={
          Platform.OS === 'ios'
            ? ['right', 'top', 'left']
            : ['right', 'top', 'left', 'bottom']
        }
      >
        <StatusBar
          barStyle={'dark-content'}
          backgroundColor={color.primary_cream}
        />
        {refresh || loader || (token ? !authUser : false) ? (
          <ActivityIndicator size={'large'} style={{ flex: 1 }} />
        ) : isFitnessUser && !token ? (
          <FitnessNavigator />
        ) : token ? (
          <RootNavigator />
        ) : (
          <GuestNavigator />
        )}
      </SafeAreaView>
      <Toast config={toastConfig} />
    </NavigationContainer>
  );
};

export default AppNavigator;
