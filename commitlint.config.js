module.exports = {
  extends: ['@commitlint/config-conventional'],
  rules: {
    'type-enum': [
      2, // Error level: 2 (error), 1 (warning), 0 (off)
      'always',
      [
        'feat',
        'fix',
        'docs',
        'style',
        'refactor',
        'test',
        'chore',
        'perf',
        'ci',
        'build',
        'revert',
        'hotfix',
        'dep',
        'infra',
        'security',
        'ui',
        'ops',
      ],
    ],
    'subject-case': [
      2,
      'never',
      ['sentence-case'], // Ensures the subject starts with a capital letter
    ],
    'header-max-length': [
      2,
      'always',
      72, // Limits the commit message header to 72 characters
    ],
  },
};
