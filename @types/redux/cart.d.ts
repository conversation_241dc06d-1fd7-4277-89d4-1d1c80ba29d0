declare module 'cart-slice' {
  import { protein_category } from 'delivery-slice';

  export interface cart_itemProps {
    protein_category: protein_category;
    avoid_category: string[];
    is_vegetarian: boolean;
    avoid_ingredients: string[];
    no_of_meals: number;
    no_of_breakfast: number;
    no_of_snacks: number;
    plan_duration_in_days: string;
    delivery_days: string;
    qty: number;
    price: number;
    per_day_price: string;
    selected_meal: string[];
    selected_meal_type: SelectedMealType[];
    delivery_start_date: Date | string;
    delivery_details: DeliveryDetails;
    cart_status: string;
    body_metrics?: {
      body_metrics: BodyMetrics;
      target_calories?: number;
      target_macros?: TargetMacros;
    };
  }
  export interface cartDetailsProps {
    _id?: string;
    address_data?: {
      latitude: number;
      longitude: number;
      _id: string;
      customer_id: string;
      city: string;
      province: string;
      country: string;
      full_address: string;
    };
    billing_address_data?: {
      billing_address1: string;
      billing_address2: string;
    };
    bag_info?: { name: 'Refundable Bag' | string };
    customer_id?: string;
    addon_discount?: Array<{
      discount: number;
      discount_percent: number;
      discount_type: string;
      discount_name: string;
    }>;
    cart_item?: Array<cart_itemProps> | [];
    cart_status?: string;
    cart_subtotal?: number;
    cart_total?: number;
    cart_type?: string;
    cart_vat?: number;
    cart_vat_value?: number;
    combo_discount?: number;
    coupon_id?: {
      _id?: string;
      name?: string;
      coupon_code?: string;
      description?: string;
      discount_type?: string;
      discount_value?: number;
    };
    createdAt?: string;
    customer_type?: string;
    cart_is_corporate?: boolean | null;
    additional_discount?: number;
    additional_discount_type?: 'corporate' | null;
    discount?: number;
    discount_type?: string;
    discount_eligibility_type?: string;
    final_total?: number;
    refundable_deposite?: number;
    instruction?: Array<any>;
    qty_count?: number;
    shipping_charge?: number;
    same_as_delivery?: boolean;
    unique_id?: string;
    updatedAt?: string;
    delivery_start_date?: string;
    slot?: string;
    delivery_note?: string;
    is_renew?: boolean;
    is_used_reward_wallet?: boolean;
    reward_id?: string;
    reward_type?: string;
    reward_value?: number;
    reward_wallet?: number;
    usable_reward_wallet?: number;
    referral_discount?: number;
    is_deposite_box?: boolean;
    apply_refund_deposite?:
      | 'refundable_deposite'
      | 'box_deposite'
      | 'paper_bag_deposite'
      | 'true'
      | 'false'
      | null;
    redeem_amount_wallet?: number;
    reward_aed?: number;
    price_type?: number;
    box_deposite?: number;
    offer_applicable?: boolean;
    offer_type?: string;
    cart_goal?: string;
  }

  export interface cartSlicesProps {
    loader: boolean;
    cartDetails?: cartDetailsProps;
  }
  interface TargetMacros {
    protein: number;
    fat: number;
    carbs: number;
  }

  interface MealRecommendation {
    rank: number;
    diet_type: string;
    combo_name: string;
    meal_parts: string[];
    meal_data: MealData;
    distance: number;
    size: string;
  }

  interface BodyMetrics {
    weight: number;
    weight_unit: 'kg' | 'lbs';
    height: number;
    height_unit: 'cm' | 'inch';
    activity_level:
      | 'sedentary'
      | 'light'
      | 'moderate'
      | 'active'
      | 'athlete_mode';
    goal: 'lose' | 'gain' | 'maintain';
    gender: 'male' | 'female' | 'other';
    birth_date: string;
  }

  interface nutritionPlanProps {
    body_metrics?: BodyMetrics;
    target_calories?: number;
    target_macros?: TargetMacros;
    mealRecommendations?: MealRecommendation[];
  }
  export interface cartSlicesProps {
    loader: boolean;
    cartDetails?: cartDetailsProps;
    nutritionPlan?: nutritionPlanProps;
  }

  export interface MacroRange {
    start: number;
    end: number;
  }

  export interface Macro {
    range: MacroRange;
    color: string;
  }

  export interface Portion {
    portion: string;
    macro: Macro[];
  }

  export interface KcalRange {
    start: number;
    end: number;
  }

  export interface Size {
    size: string;
    kcalRange: KcalRange;
  }

  export interface MealType {
    name: string;
    portioning: Portion[];
    sizes: Size[];
  }

  export interface DietaryPreferences {
    protein_category: string;
    is_vegetarian: boolean;
    avoid_category: string[];
    avoid_ingredients: string[];
    kcal_range: string;
    is_recommended: boolean;
    body_metrics?: {
      body_metrics: BodyMetrics;
      target_calories?: number;
      target_macros?: TargetMacros;
    };
    selected_meal: string[];
    kcal: number;
  }

  export interface KeyValueProps {
    [key: string]: any;
  }

  export interface SubscriptionPriceProps {
    meal_type: string;
    meal_tag: string;
    number_of_meal: number | string;
    protein_category: string;
  }

}
