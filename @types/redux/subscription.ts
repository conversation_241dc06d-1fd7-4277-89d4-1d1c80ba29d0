declare module 'subscription-slice' {
export interface subscriptionDetailsState {
  end_date?: string;
  kcal?: string;
  status?: string;
  instruction?: Array<string>;
  createdAt?: string;
  is_refundable?: boolean;
  _id?: string;
  customer_id?: string;
  address_id?: {
    _id?: string;
    type?: string;
    full_address?: string;
    city?: string;
    province?: string;
    country?: string;
  };
  order_id?: string;
  plan_type?: string;
  delivery_days?: string;
  plan_duration_in_days?: string;
  is_vegetarian?: boolean;
  avoid_ingredients?: Array<string>;
  avoid_category?: Array<string>;
  kcal_range?: string;
  selected_meal_type?: Array<{
    meal_type: string;
    kcal_range: string;
    kcal: string;
    qty: number;
    protein_category: string;
  }>;
  selected_meal: Array<string> | [];
  price?: number;
  delivery_start_date?: string;
  slot?: string;
  delivery_note?: string;
  week_address?: Array<{
    key:
      | 'monday'
      | 'tuesday'
      | 'wednesday'
      | 'thursday'
      | 'friday'
      | 'saturday';
    _id: string;
    address_id: string;
    slot: string;
    instruction:string[]
  }>;
  no_of_meals?: number;
  no_of_breakfast?: number;
  no_of_snacks?: number;
  macros?:{
    calories:number;
    protein:number;
    carbs:number;
    fat:number;
  }
}
export interface subscription_priceProps{
  protein_category: string,
  _id: string,
  number_of_meal: string,
  meal_type: string,
  meal_tag: string,
  day_5: number,
  day_6: number,
  day_20: number,
  day_24: number,
  price_type: number,
  is_active: boolean,
  box_deposite: number,
  refundable_deposite: number,
  box_deposite_monthly: number,
  box_deposite_trial: number,
  is_mandatory: Boolean,
}
export type SubscriptionPriceWithDays = subscription_priceProps &Record<`day_${string | number}`, number>;
export interface SubscriptionsPriceData{
  price_type: number,
  subscription_price: Array<SubscriptionPriceWithDays>,
  subscription_fix_price: {
      breakfast: string,
      evening_snack: string,
      morning_snack: string,
      durations: Array<{ week_day: number, days: Array<number> }>
  }
}
export interface subscriptionSlicesProps {
  loader: boolean;
  subscriptionDetails: Array<subscriptionDetailsState>;
  priceData?: SubscriptionsPriceData;
  availableDate?: {
    "availableWeeklyDate"?: string,
    "availableMonthlyDate"?: string
  },
}
}
