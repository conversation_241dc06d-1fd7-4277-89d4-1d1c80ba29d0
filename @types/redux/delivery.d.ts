declare module 'delivery-slice' {
  import { Lanobj } from 'app-actions';
  import { address_data_props } from 'auth-actions';

  export type ProteinCategory = 'low' | 'balance' | 'high';
  export type MealType = 'breakfast' | 'lunch' | 'dinner' | 'morning_snack' | 'evening_snack';
  export type KcalRange = 'Extra_large' | 'Large' | 'Extra_small' | 'Small';

  export interface RatingDataProps {
    _id: string;
    customer_id: string;
    delivery_id: string;
    recipe_id: string;
    createdAt: string;
    delivery_date: string;
    delivery_type: string;
    rating: number;
    review: string[];
    updatedAt: string;
    comment: string;
  }

  export interface MealSelectedVariantsProps {
    variant_ingredients_tl: Array<Lanobj>;
    protein_category: ProteinCategory;
    variant_ingredients: string[];
    protein_option: string;
    net_qty: number;
    size: string;
    kcal: number;
    fat: number;
    carb: number;
    price: number;
    protein: number;
  }
  export interface selected_meal_props {
    _id: string;
    recipe_id: string;
    meal_category: string;
    dish_name: string;
    dish_name_tl: Lanobj;
    selected_meal: DeliveryItemProps;
    category_type: string;
    cuisine: string;
    dish_type: string[];
    description: string;
    description_tl: Lanobj;
    ingredients: string[];
    ingredients_tl: Array<Lanobj>;
    is_taken: boolean;
    allergens_contain: string[];
    allergens_free_from: string[];
    plating_instruction: string;
    packaging_material: [];
    packaging_material_original: string[];
    website_image: string[];
    other_variants: string[];
    protein_size: string[];
    spice_level: string;
    cooking_complexity: string;
    plating_complexity: string;
    highly_perishable: boolean;
    rating_id: RatingDataProps;
    variants: MealSelectedVariantsProps;
    is_auto_select: boolean;
    unique_id: string;
  }
  export interface DeliveryItemProps {
    protein_category: ProteinCategory;
    meal_type: MealType;
    kcal_range: KcalRange;
    kcal: string;
    selected_meal: selected_meal_props;
    qty: number;
  }

  export interface SelectedMealTypeProps {
    meal_type: MealType;
    kcal_range: KcalRange;
    kcal: string;
    protein_category: ProteinCategory;
    qty: number;
    _id: string;
  }

  export interface SelectedMealProps {
    _id: string;
    recipe_id: string;
    meal_category: string;
    label_instruction: string;
    plating_instruction: string;
    dish_name: string;
    category_type: string;
    cuisine: string;
  }



  export interface WeekDeliveryItemProps {
    _id: string;
    customer_id: string;
    subscription_id: string;
    address_id: string;
    delivery_type: string;
    delivery_date: string;
    slot: string;
    status: string;
    not_deliverable: boolean;
    is_delivery_freezed: boolean;
    avoid_ingredients_tl: Array<Lanobj>;
    selected_meal_type: SelectedMealTypeProps[];
    delivery_item: DeliveryItemProps[];
    end_date: string;
    avoid_ingredients: string[];
    addressData: address_data_props;
  }

  export interface DeliveryListProps {
    delivery_date: string;
    is_delivery_freezed: boolean;
    not_deliverable: boolean;
    status: string;
    _id: string;
  }

  export interface DeliverySlicesProps {
    loader: boolean;
    listLoader: string[];
    deliverybyDate?: WeekDeliveryItemProps;
    deliveryList: DeliveryListProps[];
    selectedDate: string;
    weekDelivryList: Array<WeekDeliveryItemProps>;
  }
}
