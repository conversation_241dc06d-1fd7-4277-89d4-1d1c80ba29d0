declare module 'auth-actions' {
  export interface keys {
    YOUR_WELLNESS_JOURNEY_STARTS_HERE: string;
    GET_STARTED: string;
    PURCHASED_ON_WEB: string;
    LOGIN: string;
    BUILD_YOUR_PLAN: string;
    SELECT_YOUR_PREFERRED_DIET: string;
    MOST_POPULAR: string;
    NOT_SURE_WHAT_TO_PICK: string;
    NOT_SURE_WHAT_TO_PICK_DESC: string;
    SELECT_MEALS_DAILY: string;
    SELECT_CALORIE_RANGE_DAILY: string;
    SELECT_PLAN_DURATION: string;
    START_PLAN_ON: string;
    START_PLAN_ON_NOTE: string;
    DELIVERY_SLOT_TITLE: string;
    HELP_TEXT: string;
    AVAILABLE_SLOTS: string;
    WHAT_MAKES_DELICUT_BETTER: string;
    SELECT_INGREDIENTS_YOU_DISLIKE: string;
    FINE_TUNE_PLAN_AFTER_ORDER: string;
    DELIVERY_DETAILS: string;
    CHANGE: string;
    CHANGE_DELIVERY_DETAILS: string;
    CONTACT_DETAILS: string;
    SELECTED_DELIVERY_DAYS_AND_SLOT: string;
    ADD_SPECIAL_INSTRUCTIONS: string;
    LOGIN_TO_DELICUT: string;
    OR: string;
    LOGIN_USING_PHONE_NUMBER: string;
    CONTINUE: string;
    BY_CONTINUING_YOU_AGREE_TO_DELICUT_S: string;
    TERMS_AND_CONDITIONS: string;
    PRIVACY_POLICY: string;
    AND: string;
    VERIFY_CODE: string;
    ENTER_6_DIGIT_CODE_SENT_TO: string;
    VERIFYING_YOUR_OTP: string;
    CODE_VERIFIED_SUCCESSFULLY: string;
    GOOD_AFTERNOON: string;
    GOOD_EVENING: string;
    GOOD_MORNING: string;
    KCAL: string;
    PROTEIN: string;
    PRO: string;
    CARB: string;
    FAT: string;
    ADD_NEW_ADDRESS: string;
    ADDRESS: string;
    REQUIRED: string;
    EDIT: string;
    SKIPPED: string;
    BENEFITS_CARD_TITLE: string;
    BENEFITS_LIST: string;
    EXPECT_NEXT_CARD_TITLE: string;
    EXPECT_NEXT_LIST: string;
    ENDED_SUBSCRIPTION_TITLE: string;
    ENDED_SUBSCRIPTION_SUB_TITLE: string;
    SHARE_MESSAGE: string;
    SHARE_TITLE: string;
    LOGIN_WITH_GOOGLE: string;
  }

  export type translation = keys &
    Record<`SAVE_AED_${string | number}`, string>;
  export interface address_data_props {
    _id: string;
    customer_id: string;
    address_type: string;
    full_address: string;
    city: string;
    province: string;
    country: string;
    updatedAt: string;
    createdAt: string;
  }
  export interface save_card_props {
    src_id: string;
    expiry_month: number;
    expiry_year: number;
    scheme: string;
    last4: string;
    bin: string;
    type: 'card' | string;
    name: string;
    card_type: 'CREDIT' | string;
    last_used: boolean;
  }
  export interface AuthUserProps {
    customer_type?: 'corporate' | null;
    ethnicity?: string;
    cuisine?: Array<{ [key: string]: string }>;
    body_metrics: {
      weight_unit: 'kg';
      height_unit: 'cm';
      progress_speed: 'standard';
      protein_category: 'non_vegetarian';
    };
    is_ndd?: boolean;
    _id: string;
    email: string;
    country: string;
    latitude: number;
    longitude: number;
    is_verified: boolean;
    user_register_flag: string;
    total_spent: number;
    total_orders: number;
    shopify_total_orders: number;
    total_ndd_order: number;
    total_subscription_order: number;
    internal_notes: string;
    fcm_token: Array<string>;
    createdAt: string;
    updatedAt: string;
    country_code: string;
    first_name: string;
    last_name: string;
    phone_number: string;
    whatsapp_country_code: string;
    whatsapp_number: string;
    full_address: string;
    billing_address1: string;
    billing_address2: string;
    city: string;
    province: string;
    address_data: Array<address_data_props>;
    is_subscription: boolean;
    shopify_total_spent: number;
    is_refundable: true;
    bag: Array<string>;
    bag_count: number;
    loyalty_id: string;
    reward_wallet: number;
    reward_amount_wallet: number;
    referral_code: string;
    level: string;
    referred_users: Array<{ customer_id: string }>;
    saved_cards: Array<save_card_props>;
  }
  export interface authSliceProps {
    translation: translation;
    loader: boolean;
    authUser?: AuthUserProps | null;
    tokenLoader: boolean;
    token?: string;
    addressByID?: addressByIDProps;
    loyaltyTransactionData?: {
      message?: string;
      status?: boolean;
      total_count?: number;
      last_page?: number;
      current_page?: number;
    };
    loyaltyTransactionList: Array<{
      _id: string;
      customer_id: string;
      loyalty_benefits: string;
      loyalty_id: string;
      date: string;
      is_credit: boolean;
      points_earned: number;
      earned_amount_wallet: number;
      reward_type: string;
      order_id: string;
      redeem_amount_wallet: number;
      redeem_points: number;
    }>;
  }
  export interface ActionMeta<T = any> {
    callBack: (e: { status: boolean; data: T; message: string }) => void;
  }
}
