declare module 'theme-actions' {
  type theme = 'light' | 'dark';

  interface colors {
    primary_grenade: string;
    neutral_white: string;
    grey_400: string;
    grey_900: string;
    primary_spinach: string;
    grey_600: string;
    secondary_curcuma: string;
    secondary_avocado: string;
    black: string;
    black_900: string;
    grey_800: string;
    grey_100: string;
    grey_700: string;
    grays_black: string;
    AEC323: string;
    DAEBB6: string;
    neutral_black: string;
    primary_cream: string;
    secondary_warm_grey: string;
    secondary_berry: string;
    green_477C7C: string;
    transparent: string;
    black_50_p: string;
    transparent: string;
    red_500: string;
    grey_200: string;
    grey_7A7A7A: string;
    grey_0000000F: string;
    secondary_curry: string;
    red_gradient: string[];
    yellow_gradient: string[];
    red_600: string;
    primary_spinach_10_p: string;
    black_30_p: string;
    grey_400: string;
    secondary_warm_grey_40_p: string;
    FFDDD199: string;
    cyan_gradient: string[];
  }

  export interface ThemeState {
    currentTheme: theme;
    colors: Record<theme, colors>; // Use Record to map theme to colors
  }

  export interface ThemeSliceActions {
    toggleTheme: () => void;
    setTheme: (theme: theme) => void;
    setColors: (colors: colors) => void;
  }
}
