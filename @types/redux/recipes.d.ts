declare module 'recipe-slice' {
  import { Lanobj } from 'app-actions';
  import { MealSelectedVariantsProps } from 'delivery-slice';
  export interface bodyMetricsProps {
    goal: string;
    activity: string;
    gender: string;
    weight: string;
    height: string;
    birthdate: string;
  }
  interface meal {
    avg_rating: string;
    total_ratings: string;
    recipe_id: string;
    meal_category: string;
    dish_name: string;
    dish_name_tl: Lanobj;
    category_type: string;
    cuisine: string;
    dish_type: string[];
    description: string;
    description_tl: Lanobj;
    ingredients: string[];
    website_image: string[];
    variants: Array<MealSelectedVariantsProps>;
    spice_level: string;
    cooking_complexity: string;
    plating_complexity: string;
    highly_perishable: boolean;
    protein_category_info: Array<{
      category: string;
      description: string;
      description_tl: Lanobj;
      dish_name: string;
      dish_name_tl: Lanobj;
    }>;
  }
  export interface recipesSlicesProps {
    loaders: string[];
    weeklyRecipes?: {
      meal: Array<meal>;
      breakfast: Array<meal>;
      snack: Array<meal>;
    };
  }
}
