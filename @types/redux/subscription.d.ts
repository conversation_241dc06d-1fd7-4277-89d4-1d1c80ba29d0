declare module 'subscription-slice' {
  import { kcal_range, meal_type, protein_category } from "delivery-slice";
  export interface subscriptionDetailsState {
    end_date?: string;
    kcal?: string;
    status?: string;
    instruction?: Array<string>;
    createdAt?: string;
    is_refundable?: boolean;
    _id?: string;
    customer_id?: string;
    address_id?: {
      _id?: string;
      type?: string;
      full_address?: string;
      city?: string;
      province?: string;
      country?: string;
    };
    order_id?: string;
    plan_type?: string;
    delivery_days?: string;
    plan_duration_in_days?: string;
    is_vegetarian?: boolean;
    avoid_ingredients?: Array<string>;
    avoid_category?: Array<string>;
    kcal_range?: kcal_range;
    selected_meal_type?: Array<{
      meal_type: meal_type;
      kcal_range: kcal_range;
      kcal: string;
      qty: number;
      protein_category: protein_category;
    }>;
    selected_meal: Array<string> | [];
    price?: number;
    delivery_start_date?: string;
    slot?: string;
    delivery_note?: string;
    week_address?: Array<{
      key:
        | 'monday'
        | 'tuesday'
        | 'wednesday'
        | 'thursday'
        | 'friday'
        | 'saturday';
      _id: string;
      address_id: string;
      slot: string;
      instruction: string[];
    }>;
    no_of_meals?: number;
    no_of_breakfast?: number;
    no_of_snacks?: number;
    macros?: {
      calories: number,
      protein: number,
      fat: number,
      carbs: number,
    },
  }

  export interface subscriptionSlicesProps {
    loader: boolean;
    subscriptionDetails: Array<subscriptionDetailsState>;
    priceData?: {
      price_type?: number;
      subscription_price?: Array<
        {
          protein_category: string;
          _id: string;
          number_of_meal: string;
          meal_type: string;
          meal_tag: string;
          day_5: number;
          day_6: number;
          day_20: number;
          day_24: number;
          price_type: number;
          is_active: boolean;
          box_deposite: number;
          refundable_deposite: number;
          box_deposite_monthly: number;
          box_deposite_trial: number;
          is_mandatory: boolean;
          [key: string]: any;
        } & { [key in `day_${number | string}`]: number }
      >;
      subscription_fix_price?: {
        breakfast: string;
        evening_snack: string;
        morning_snack: string;
        durations: Array<{ week_day: number; days: Array<number> }>;
      };
    };
  }
}

interface KcalRange {
  start: number;
  end: number;
}

export interface SizeInfo {
  size: string;
  kcalRange: KcalRange;
}

interface Range {
  start: number;
  end: number;
}

interface Macro {
  range: Range;
  color: string;
}

interface Portion {
  portion: string; // e.g. "Carb", "Fat", "Protein"
  macro: Macro[];
}

interface KcalRange {
  start: number;
  end: number;
}

interface Size {
  size: string; // e.g. "standard", "medium", "large", ...
  kcalRange: KcalRange;
}

export interface MealPlanItem {
  name: string; // "Meal", "Breakfast", "Snack"
  portioning: Portion[];
  sizes: Size[];
}
