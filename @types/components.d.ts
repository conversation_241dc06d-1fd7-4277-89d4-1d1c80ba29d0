declare module 'componentsProps' {
  import {
    ColorValue,
    KeyboardTypeOptions,
    StyleProp,
    TextStyle,
    ViewStyle,
  } from 'react-native';
  import { lightTheme } from '../src/theme/colors';
  export type BottomSheetAnimationType =
    | 'fade'
    | 'slide-up'
    | 'scale'
    | 'bounce'
    | 'zoom-in';
  export interface GorhomBottomSheetProps {
    sheetOpen: Boolean;
    sheetClose?: () => void;
    children: PropTypes.ReactNodeLike | PropTypes.ReactElementLike;
    footer?: ReactNode | ReactNode[];
    closeOnBackdrop?: boolean;
    modalBackgroundColor?: ColorValue;
    closeOnPressBack?: boolean;
    title: string;
    desc?: string;
    descStyle?: StyleProp<TextStyle>;
    customRightIcon?: ReactNode;
    hideCloseIcon?: boolean;
    animationType?: BottomSheetAnimationType;
  }
  export interface PrimaryBtnProps {
    textStyle?: StyleProp<TextStyle>;
    leftIcon?: React.ReactNode;
    rightIcon?: React.ReactNode;
    iconStyle?: StyleProp<ViewStyle>;
    text?: string;
    backgroundColor?: string;
    onPress?: ((event: GestureResponderEvent) => void) | null;
    style?: StyleProp<ViewStyle> | undefined;
    isSelected?: boolean;
    disabledColor?: string;
    disabledColor?: string;
    focusColor?: string;
    disabled?: boolean | null | undefined;
    isLoading?: boolean;
  }
  export interface DividerProps {
    color?: string;
    thickness?: number;
    margin?: number;
    style?: StyleProp<ViewStyle>;
  }

  export interface SelectableChipProps {
    textStyle?: StyleProp<TextStyle>;
    text: string;
    onPress?: ((event: GestureResponderEvent) => void) | null;
    style?: StyleProp<ViewStyle> | undefined;
    isSelected?: boolean;
    disabled?: boolean | null | undefined;
    isLoading?: boolean;
    rightIcon?: ReactNode;
    leftIcon?: ReactNode;
  }
  export interface warning_chip_props {
    textStyle?: StyleProp<TextStyle>;
    text: string;
    style?: StyleProp<ViewStyle> | undefined;
  }

  export interface InfoCardProps {
    cardStyle?: StyleProp<ViewStyle>;
    textStyle?: TextStyle;
    message: string;
  }
  export interface SelectBottomSheetprops {
    labelField?: string;
    style?: StyleProp<ViewStyle>;
    placeholder?: string;
    containerStyle?: ViewStyle;
    option?: Array<{
      label?: string;
      value?: string;
    }>;
    error?: string | undefined;
    touched?: boolean;
    value: string | undefined;
    onChange?: (e: string) => void;
    header?: string;
    headerStyle?: StyleProp<TextStyle>;
    disable?: boolean | undefined;
    error?: string | undefined;
    bottomSheetTitle?: string;
  }

  export interface AddressSelectBottomSheetProps {
    option?: Array<{
      label?: string;
      value?: string;
    }>;
    value?: string | undefined;
    sheetOpen: boolean;
    title?: string;
    sheetClose?: () => void;
    onChange?: (e: string) => void;
  }

  export interface BottomFloatingCardProps {
    btnText?: string;
    onPress?: (() => void) | null;
    isLoading?: boolean;
  }

  export interface ModalLoaderProps {
    message: string;
  }

  export interface HeaderProps {
    headerTitle?: string;
    showDivider?: boolean;
    textStyle?: StyleProp<TextStyle>;
  }

  export interface CustomDividerProps {
    color?: keyof typeof lightTheme;
    height?: number;
    margin?: number;
    padding?: number;
    text?: string;
    textStyle?: StyleProp<TextStyle>[];
    style?: StyleProp<ViewStyle>[];
  }

  export interface OtpInputProps {
    length?: number;
    onComplete?: (otp: string) => void;
  }

  export interface FormInputProps {
    error?: string | undefined;
    touched?: boolean;
    wrapperStyle?: StyleProp<ViewStyle>;
    headerStyle?: StyleProp<ViewStyle>;
    inputStyles?: StyleProp<TextStyle>;
    containerStyle?: StyleProp<ViewStyle>;
    placeholder?: string;
    value?: string | undefined;
    onChangeText?: ((text: string) => void) | undefined;
    leftIcon?: React.ReactNode;
    header?: string;
    numberOfLines?: number;
    keyboardType?: KeyboardTypeOptions;
    disabled?: boolean;
    autoFocus?: boolean;
    numberOfLines?: number;
  }
  export interface DropDownprops {
    labelField?: string;
    style?: StyleProp<ViewStyle>;
    valueField?: string;
    placeholder?: string;
    containerStyle?: ViewStyle;
    option?: Array<{
      [key: string]: string;
    }>;
    value: string | null | undefined;
    onChange: (e: string) => void;
    header?: string;
    headerStyle?: StyleProp<TextStyle>;
    disable?: boolean | undefined;
    error?: string | undefined;
    touched?: boolean;
  }
  export interface MultiSelectProps {
    labelField?: string;
    style?: StyleProp<ViewStyle>;
    valueField?: string;
    placeholder?: string;
    containerStyle?: ViewStyle;
    option?: Array<{
      [key: string]: string;
    }>;
    value: string[] | null | undefined;
    onChange: (e: string[]) => void;
    header?: string;
    headerStyle?: StyleProp<TextStyle>;
    disable?: boolean | undefined;
    error?: string | undefined;
    touched?: boolean;
    renderLeftIcon?: () => React.ReactElement | null;
  }
  export interface DropDownprops {
    labelField?: string;
    style?: StyleProp<ViewStyle>;
    valueField?: string;
    placeholder?: string;
    containerStyle?: ViewStyle;
    option?: Array<{
      [key: string]: string;
    }>;
    value: string | null | undefined;
    onChange: (e: string) => void;
    header?: string;
    headerStyle?: StyleProp<TextStyle>;
    disable?: boolean | undefined;
    error?: string | undefined;
    touched?: boolean;
    renderLeftIcon?: () => React.ReactElement | null;
  }
  export interface CalendarModalProps {
    value?: string | undefined;
    onChangeText?: ((text: string) => void) | undefined;
    maximumDate?: Date | undefined;
    minimumDate?: Date;
    isWeekday?: () => number[];
    onclose: () => void;
    open: boolean;
    title?: string;
    titleStyle?: TextStyle;
  }
  export interface MultiStepModalProps {
    onclose: () => void;
    open: boolean;
    loading?: boolean;
    setLoading?: (v: boolean) => void;
  }

  export interface DateInputProps {
    error?: FieldError | undefined;
    placeholder?: string;
    title?: string;
    value?: string | undefined;
    onChangeText?: ((text: string) => void) | undefined;
    leftIcon?: React.ReactNode;
    maximumDate?: Date | undefined;
    minimumDate?: Date;
    isWeekday?: () => number[];
  }
}
