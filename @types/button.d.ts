import { StyleProp, TextStyle, ViewStyle } from 'react-native';

export interface MultiSelectPillGroupProps {
  options: { label: string; value?: string }[];
  selectedValues?: string[] | string;
  onSelect?: (value: string) => void;
  containerStyle?: ViewStyle;
  buttonStyle?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
  titleStyle?: TextStyle;
  isRTL?: boolean;
  isScrollable?: boolean;
  title?: string;
}
