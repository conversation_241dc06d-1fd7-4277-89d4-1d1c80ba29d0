declare module 'actions' {
  import { MealType } from 'delivery-slice';

  export type editDeliveryAddressPayload =
    {
      customer_id: string;
      type: "all";
      address_id: string;
      slot: string;
      subscription_id: string;
      instruction?: string[];
    }
    | {
      customer_id: string;
      type: "week_day";
      address_id: string;
      slot: string;
      week_day: string; // Add week_day field
      instruction?: string[];
      start_from: string
    }
    | {
      customer_id: string;
      type: "perticuler_date";
      address_id: string;
      slot: string;
      delivery_id: string;
      instruction?: string[];
    };
  export interface AddEditAddressPayload {
    address_type: string;
    full_address: string;
    city: string;
    province: string;
    country: string;
    customer_id: string;
    instruction?: string[];
  }
  export interface getWeekWiseDeliveryPayload {
    customer_id: string | undefined;
    week: 'current_week' | 'next_week';
  }
  export interface GetDeliverySlotProps {
    city: string;
    area: string;
  }

  export interface GetMealPreferencesPayload {
    key: string | undefined;
  }

  export interface FcmTokenPayload {
    fcm_token: string[];
  }

  export interface GetDeliveryByDatePayload {
    date: string;
    customer_id: string;
  }

  export interface SetMealLogPayload {
    delivery_id: string;
    unique_id: string;
  }

  export interface RecipeRatingPayload {
    customer_id: string;
    type: MealType;
    delivery_id: string;
    unique_id: string;
    count: number;
    comment: string;
    review: string[];
  }
  export interface BodyMetricsPayload {
    body_metrics: {
      weight: number;
      weight_unit: string;
      height: number;
      height_unit: string;
      activity_level: string;
      goal: string;
      gender: string;
      birth_date: string; // ISO date string: YYYY-MM-DD
    };
  };
  export interface RestartDeliveryPayload {
    customer_id: string;
    restart_dates: string[];
    subscription_id: string;
  }
  export interface addUserProflieProps {
    first_name: string;
    last_name: string;
    email: string;
    phone_number: string;
    country_code: string;
    whatsapp_country_code: string;
    whatsapp_number: string;
  }
  export interface renewSubscriptionPayload{
    customer_id: string;
    subscription_id: string;
  }
  export interface getAvailableDeliveryPayload{
    delivery_days: number,
    customer_id:string,
    delivery_type: 'subscription'
  }

  export interface createOrderPayload{
    "cartId": string,
    customer_id: string,
    payment_type: 'checkout',
    card_type?: 'save'| 'new',
    src_id?: string,
  }
}
