import { FormikProps } from 'formik';
import { cartFormProps } from 'forms';
import { KeyValueProps, Portion } from './redux/cart';

interface SelectPreferredDietProps {
  cartFormik: FormikProps<cartFormProps>;
  cartOnchange: (
    field: keyof KeyValueProps | Partial<KeyValueProps>,
    value?: KeyValueProps[keyof KeyValueProps],
  ) => void;
  addAllNonVegIng: () => {
    avoid_category: string[];
    avoid_ingredients: string[];
  };
  removeAllNonVegIng: () => {
    avoid_category: string[];
    avoid_ingredients: string[];
  };
}

interface SelectDailyMealProps {
  cartFormik: FormikProps<cartFormProps>;
  cartOnchange: (
    field: keyof KeyValueProps | Partial<KeyValueProps>,
    value?: KeyValueProps[keyof KeyValueProps],
  ) => void;
}

interface SelectCalorieProps {
  cartFormik: FormikProps<cartFormProps>;
  cartOnchange: (field: string | {}, value?: any) => void;
  totalKcalInMeal: (size: string) => {
    totalKcalStart: number;
    totalKcalEnd: number;
    portioning: Portion[];
  };
}

export interface KcalRangeProps {
  color: string;
  range: {
    start: number;
    end: number;
  };
}

interface Range {
  start: number;
  end: number;
}

interface Macro {
  range: Range;
  color: string;
}

export interface PortionMacroProps {
  portion: string;
  macro: KcalRangeProps[];
}
