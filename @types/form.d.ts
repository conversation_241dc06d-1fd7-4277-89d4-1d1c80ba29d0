declare module 'forms' {
  export interface cartFormProps {
    is_recommended: boolean;
    avoid_ingredients: Array<string>;
    avoid_category: Array<string>;
    is_vegetarian: boolean;
    body_metrics: any;
    delivery_start_date: string; // Date,
    delivery_details: {
      city: string;
      province: string;
      slot: string;
      instruction: string[];
    };
    protein_category: string;
    no_of_meals: number;
    no_of_breakfast: numbert;
    no_of_snacks: number;
    plan_duration_in_days: number;
    delivery_days: number;
    qty: 1;
    price: number;
    per_day_price: number;
    selected_meal: string[];
    kcal_range: string;
    kcal: string;
    dayArray: number[];
  }
  export interface bodyMetricsProps {
    goal: string;
    activity: string;
    gender: string;
    weight: string;
    height: string;
    birthdate: string;
  }
}
