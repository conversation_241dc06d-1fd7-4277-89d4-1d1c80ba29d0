import { translation } from 'auth-actions';

export const INIT_translation: translation = {
  KCAL: 'Kcal',
  PRO: 'Pro',
  PROTEIN: 'Protein',
  CARB: 'Carb',
  FAT: 'Fat',
  YOUR_WELLNESS_JOURNEY_STARTS_HERE: 'Your wellness journey starts here',
  GET_STARTED: 'Get Started',
  PURCHASED_ON_WEB: 'Purchased on web ?',
  LOGIN: 'Log In',
  BUILD_YOUR_PLAN: 'Build your plan',
  SELECT_YOUR_PREFERRED_DIET: 'Select your preferred diet',
  MOST_POPULAR: 'Most Popular',
  NOT_SURE_WHAT_TO_PICK: 'Not sure what to pick?',
  NOT_SURE_WHAT_TO_PICK_DESC:
    'Get a personalized recommendation based on your goals',
  SELECT_MEALS_DAILY: 'Select the meals you need daily',
  SELECT_CALORIE_RANGE_DAILY: 'Select calorie range you need per day',
  SELECT_PLAN_DURATION: 'Select duration of the plan',
  START_PLAN_ON: 'Start your plan on',
  START_PLAN_ON_NOTE:
    'You can customise, pause or skip a meal anytime after checkout.',
  DELIVERY_SLOT_TITLE: 'Delivery slot',
  HELP_TEXT: 'Help text goes here',
  AVAILABLE_SLOTS: 'Available slots',
  WHAT_MAKES_DELICUT_BETTER: 'What makes Delicut better?',
  SELECT_INGREDIENTS_YOU_DISLIKE: 'Select ingredients you dislike',
  FINE_TUNE_PLAN_AFTER_ORDER:
    "You'll be able to fine-tune your plan even after placing the order.",
  DELIVERY_DETAILS: 'Delivery details',
  CHANGE: 'Change',
  CHANGE_DELIVERY_DETAILS: 'Change delivery details',
  CONTACT_DETAILS: 'Contact Details',
  SELECTED_DELIVERY_DAYS_AND_SLOT: 'Selected delivery days & slot',
  ADD_SPECIAL_INSTRUCTIONS: 'Add special instructions',
  LOGIN_TO_DELICUT: 'Login to delicut',
  LOGIN_WITH_GOOGLE: 'Login with Google',
  OR: 'or',
  LOGIN_USING_PHONE_NUMBER: 'Login using phone number',
  CONTINUE: 'Continue',
  BY_CONTINUING_YOU_AGREE_TO_DELICUT_S: 'By continuing you agree to Delicut’s',
  TERMS_AND_CONDITIONS: 'Terms & Conditions',
  PRIVACY_POLICY: 'Privacy Policy',
  AND: 'and',
  VERIFY_CODE: 'Verify code',
  ENTER_6_DIGIT_CODE_SENT_TO: 'Enter 6 digit code sent to ',
  VERIFYING_YOUR_OTP: 'Verifying your OTP',
  CODE_VERIFIED_SUCCESSFULLY: 'Your code has been verified successfully!',
  GOOD_AFTERNOON: 'Good Afternoon!',
  GOOD_EVENING: 'Good Evening!',
  GOOD_MORNING: 'Good Morning!',
  ADD_NEW_ADDRESS: 'Add new address',
  SKIPPED: 'Skipped',
  BENEFITS_CARD_TITLE: 'Delicut Benefits',
  BENEFITS_LIST:
    'Stay consistent with your goals – no prep, no stress\nCurated menus that adapt to your tastes\nTrack meals, rate them, and personalize your plan\nFlexible plans. Pause, skip, or swap meals anytime',
  EXPECT_NEXT_CARD_TITLE: 'What to expect next?',
  EXPECT_NEXT_LIST:
    'Get notified a day before your first meal is delivered.\nPreview your meals for the week and make changes before cutoff.\nRate meals once deliveries begin to personalize your plan.',
  ENDED_SUBSCRIPTION_TITLE: 'Your subscription has ended',
  ENDED_SUBSCRIPTION_SUB_TITLE:
    'You are no longer receiving meals. Renew to continue your healthy journey.',
  ADDRESS: 'Address',
  REQUIRED: 'Required',
  EDIT: 'Edit',
  SHARE_MESSAGE: `Hi 👋🏼\n
    Delicut is a game-changer for everyday meals.🍲✨\n
    No cooking stress- just clean, daily meals.\n
    Tap the link to place your order now and get 15% OFF on your first order.\n
    Use code “DELICUT15”.
    `,
  SHARE_TITLE: 'Share a link & earn',
};
