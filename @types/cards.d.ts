declare module 'card-props' {
  import {
    deleveryItemProps,
    meal_type,
    protein_category,
    weekDelivryItemProps,
  } from 'delivery-slice';
  import { FC, ReactNode } from 'react';
  import { FieldError, UseFormSetValue, UseFormWatch } from 'react-hook-form';
  import {
    GestureResponderEvent,
    ImageSourcePropType,
    StyleProp,
    TextStyle,
    ViewStyle,
  } from 'react-native';
  import { SvgProps } from 'react-native-svg';
  import { liveRecipes, recipesVariant } from 'recipe-slice';
  import { MealplanFormFormValue } from '../src/FormContext/mealplanContext';

  export interface CommonCardProps {
    style?: StyleProp<ViewStyle>;
    titleStyle?: StyleProp<TextStyle>;
    title?: string;
    children: ReactNode;
  }

  export interface CustomCardProps {
    style?: StyleProp<ViewStyle>[];
    title?: string;
    titleStyle?: StyleProp<TextStyle>[];
    children: ReactNode;
    showCloseIcon?: boolean;
    onClose?: () => void;
    leftIconClick?: () => void;
    ShowTitleLeftIcon?: FC<SvgProps> | string;
  }

  export interface KnowTragetGreenCardProps {
    title?: string;
    desc?: string;
    titleStyle?: StyleProp<TextStyle>;
    cardStyle?: StyleProp<ViewStyle>;
    onPress?: ((event: GestureResponderEvent) => void) | null;
    imageSource?: React.ReactNode;
    children?: React.ReactNode;
    linearGradientColors?: string[];
  }

  export interface CallToActionBannerProps {
    title: string;
    imageSource: ImageSourcePropType;
    titleStyle?: StyleProp<TextStyle>;
  }

  export interface WelcomeCardProps {
    setSteps: (step: number) => void;
  }

  export interface VerifyCardProps extends WelcomeCardProps {
    phoneNumber: string;
  }

  export interface LoginCardProps extends VerifyCardProps {
    setPhoneNumber: (phoneNumber: string) => void;
  }

  export interface PrimaryBtnProps {
    textStyle?: StyleProp<TextStyle>;
    leftIcon?: React.ReactNode;
    rightIcon?: React.ReactNode;
    iconStyle?: StyleProp<ViewStyle>;
    text?: string;
    onPress?: ((event: GestureResponderEvent) => void) | null;
    style?: StyleProp<ViewStyle> | undefined;
    isSelected?: boolean;
    disabled?: boolean | null | undefined;
    isLoading?: boolean;
  }

  export interface CheckboxProps {
    onChange?: (e: boolean) => void;
    value?: boolean;
    disabled?: boolean;
  }

  export interface WarningIngCardProps {
    value: string;
  }

  export interface SkeletonLoaderProps {
    width: number;
    height: number;
    borderRadius?: number;
  }

  export interface BarStepperViewProps {
    noOfSteps: number;
    activeStep: number;
    onPress?: ((event: number) => void) | null | undefined;
  }

  export interface AccordionProps {
    title: string;
    onPress?: () => void | null;
    isOpen?: boolean;
    showMoreHorizontalIcon?: boolean;
    showSkipBadge?: boolean;
    showReferAndEarnBadge?: boolean;
    children: React.ReactNode;
    onPressMore?: (() => void) | null;
  }

  export interface MealCardProps {
    title?: string;
    Kcal: number;
    Carb: number;
    Protein: number;
    Fat: number;
    mealName?: string;
    onPressDetails?: () => void;
    onPressSwap?: () => void;
    canChnage?: boolean;
    protein_option: string;
    avoidIng: string[];
    setVarient?: React.Dispatch<
      React.SetStateAction<recipesVariant | undefined>
    >;
    selectVarient?: recipesVariant | undefined;
    setRecipes?: React.Dispatch<React.SetStateAction<string | undefined>>;
    selectRecipes?: string | undefined;
    uniqueId?: string;
    deliveryId?: string;
    showRating?: boolean;
    rating?: number;
  }

  export interface MealSelectionCardProps {
    title?: string;
    desc?: string;
    customIcon?: React.ReactNode;
    ingredientsWrapperStyles?: StyleProp<ViewStyle>;
    ingredientsTextStyle?: StyleProp<TextStyle>;
    titleStyle?: StyleProp<TextStyle>;
    descStyle?: StyleProp<TextStyle>;
    cardStyle?: StyleProp<ViewStyle>;
    isSelected?: boolean;
    leftIcon?: boolean;
    onPress?: ((event: GestureResponderEvent) => void) | null;
    badgeStyle?: StyleProp<ViewStyle>;
    renderFooter?: React.ReactNode;
    showBadge?: React.ReactNode;
    customDesc?: React.ReactNode;
    numberOfLinesDesc?: number;
  }

  export interface MealsCardsProps {
    title?: string;
    isSelected?: boolean;
    onPress?: ((event: GestureResponderEvent) => void) | null;
    style?: StyleProp<ViewStyle>;
  }

  export interface MacrosCardProps {
    title?: string;
    Kcal: number;
    Carb: number;
    Protein: number;
    Fat: number;
  }

  export interface TabsProps {
    text: string;
    onPress?: ((event: GestureResponderEvent) => void) | null;
    isSelected?: boolean;
  }

  export interface errorCardProps {
    messgae?: string;
  }

  export interface DividerProps {
    color?: string;
    thickness?: number;
    margin?: number;
  }

  export interface OffersForYouCardProps {
    title: string;
    offerTitle: string;
    description?: string;
    Validity?: string;
    btnTitle: string;
    onPress?: ((event: GestureResponderEvent) => void) | null;
  }

  export interface OfferCardProps {
    mealName?: string;
    discount?: number;
    total_price?: number;
    discounted_price?: number;
    isSelected?: boolean;
    onSelect?: () => void;
  }

  export interface ExcludeIngredientsCardProps {
    isSelected: boolean;
    onSelect: (v: boolean) => void;
  }

  export interface SelectFeedbackProps {
    title: string;
    isSelected: boolean;
    onSelect: (v: boolean) => void;
    wrapperStyles?: StyleProp<ViewStyle>;
    titleStyle?: StyleProp<TextStyle>;
  }

  export interface RateThisMealCardProps {
    title?: string;
    onPressRating?: (v: number) => void;
    counts?: number;
  }

  export interface OfferSheetProps {
    isOpen: boolean;
    setClose: () => void;
    title?: string;
  }

  export interface ReferAndEarnSheetProps {
    isOpen: boolean;
    setClose: () => void;
    title?: string;
  }

  export interface ConfirmationSheetProps {
    isOpen: boolean;
    onPressUpdate?: ((event: GestureResponderEvent) => void) | null;
    setClose: () => void;
    title?: string;
  }

  export interface CorporateOtpSheetProps {
    isOpen: boolean;
    setClose: () => void;
    title?: string;
    createOrder?: () => void;
  }

  export interface SelectMealsProps {
    setValue: UseFormSetValue<MealplanFormFormValue>;
    watch: UseFormWatch<MealplanFormFormValue>;
  }

  export interface SelectDietTypeProps {
    setValue: UseFormSetValue<MealplanFormFormValue>;
    watch: UseFormWatch<MealplanFormFormValue>;
  }

  export interface SelectDietTypeSheetProps {
    totalKcalInMeal: (size: string) => {
      totalKcalStart: number;
      totalKcalEnd: number;
      portioning: Portion[];
    };
    isOpen: boolean;
    setClose: () => void;
    title?: string;
  }

  export interface LeaveFeedbackSheetProps {
    isOpen: boolean;
    setClose: () => void;
    title?: string;
    unique_id?: string;
    delivery_id?: string;
    type?: string;
    count: number;
  }

  export interface DeliveryOptionsSheetProps {
    isOpen: boolean;
    setClose: () => void;
    date: string;
    state: {
      isSkipDelivery: boolean;
      isSkipDeliveryMultiple: boolean;
      isAddress: boolean;
      isAddAddress: boolean;
    };
    setStates: React.Dispatch<
      React.SetStateAction<{
        isSkipDelivery: boolean;
        isSkipDeliveryMultiple: boolean;
        isAddress: boolean;
        isAddAddress: boolean;
      }>
    >;
  }

  export interface DeliveryOptionsWrapperProps {
    onSkipClick?: () => void;
    onAddressChange?: () => void;
  }

  export interface PerticulerAddressChangeProps {
    setClose: () => void;
    onAddAddress: () => void;
    setStates: React.Dispatch<
      React.SetStateAction<{
        isSkipDelivery: boolean;
        isSkipDeliveryMultiple: boolean;
        isAddress: boolean;
        isAddAddress: boolean;
      }>
    >;
  }

  export interface SkipOptionWrapperProps {
    date: string;
    onMultiple?: () => void;
    setClose: () => void;
  }

  export interface DateSelectProps {
    title: string;
    isSelected: boolean;
    onSelect: () => void;
  }

  export interface DeliveryItemDetailSheetProps {
    setClose: () => void;
    item?: deleveryItemProps;
    DeliveryItem: weekDelivryItemProps;
  }

  export interface MealDeatilsCardProps {
    item: deleveryItemProps;
    DeliveryItem: weekDelivryItemProps;
    setClose: () => void;
  }

  export interface liveRecipesProps {
    item: liveRecipes;
    delivery_id: string;
    unique_id: string;
    meal_type: meal_type;
    setVarient: React.Dispatch<
      React.SetStateAction<recipesVariant | undefined>
    >;
    selectVarient: recipesVariant | undefined;
    setRecipes: React.Dispatch<React.SetStateAction<string | undefined>>;
    selectRecipes: string | undefined;
  }

  export interface ResendOtpTimerProps {
    timerSeconds?: number;
    values?: { email: string; phone_number?: string; otp: string };
    showPhoneNoField?: boolean;
  }

  export interface CountryProps {
    label: string;
    value: string;
    code: string;
  }

  export interface DateInputProps {
    error?: FieldError | undefined;
    placeholder?: string;
    title?: string;
    value?: string | undefined;
    onChangeText?: ((text: string) => void) | undefined;
    leftIcon?: React.ReactNode;
    minimumDate?: Date;
    isWeekday?: () => number[];
  }

  export interface CalendarModalProps {
    value?: string | undefined;
    onChangeText?: ((text: string) => void) | undefined;
    minimumDate?: Date;
    isWeekday?: () => number[];
    onclose: () => void;
    open: boolean;
    title?: string;
    titleStyle?: TextStyle;
  }

  export interface DropDownprops {
    labelField?: string;
    style?: StyleProp<ViewStyle>;
    valueField?: string;
    placeholder?: string;
    containerStyle?: ViewStyle;
    option?: Array<{ [key: string]: string }>;
    value: string | null | undefined;
    onChange: (e: string) => void;
    header?: string;
    headerStyle?: StyleProp<TextStyle>;
    disable?: boolean | undefined;
    error?: string | undefined;
  }
  export interface PhoneInputProps {
    error?: FieldError | undefined;
    placeholder?: string;
    value?: string | undefined;
    onChangeText?: ((text: string) => void) | undefined;
    onChangeCodeText?: ((text: string) => void) | undefined;
    codeValue?: string | undefined;
    header?: string;
    disable?: boolean;
    disableCode?: boolean;
    autoFocus?: boolean;
    maxLength?: number;
  }

  export interface ScreenHeaderProps {
    title?: string;
    titleStyle?: StyleProp<TextStyle>;
    desc?: string;
    isBack?: boolean;
    onPressBack?: (event: GestureResponderEvent) => void;
    wrapperStyles?: StyleProp<ViewStyle>;
    subTitle?: ReactNode | ReactNode[];
  }

  export interface ListItemCardProps {
    title?: string;
    titleStyle?: StyleProp<TextStyle>;
    subTitle?: string;
    subTitleStyle?: StyleProp<TextStyle>;
    onPress?: () => void;
    needBorder?: boolean;
    titleWrapperStyles?: StyleProp<ViewStyle>;
    cardStyles?: StyleProp<ViewStyle>;
    LeftIcon?: React.ReactNode;
    RightIcon?: React.ReactNode;
    showChevronRightIcon?: boolean;
    titileColor?: string;
  }

  export interface DurationCardProps {
    services?: string[];
    days?: number;
    saveAED?: number;
    price?: string;
    cardStyles?: StyleProp<ViewStyle>;
    isSelected?: boolean;
    showDiscountBadge?: boolean;
    onPress?: () => void | null;
    perDayPrice?: number | string;
  }

  export interface SelectCalorieRangeProps {
    totalKcalInMeal: (size: string) => {
      totalKcalStart: number;
      totalKcalEnd: number;
      portioning: Portion[];
    };
    setValue: UseFormSetValue<MealplanFormFormValue>;
    watch: UseFormWatch<MealplanFormFormValue>;
  }

  export interface DeliveryDaysProps {
    updateCart?: () => void;
    getTotalPrice: (
      selectedPrice: number,
      no_of_breakfast: number,
      no_of_snacks: number,
      plan_duration_in_days: number,
    ) => number;
    getPriceObj: (
      kcal_range: string,
      no_of_meals: string,
      protein_category: protein_category,
    ) => SubscriptionPriceWithDays | undefined;
    hideDays?: boolean;
  }

  export interface DeliveryCardProps {
    item: weekDelivryItemProps;
    onPress: () => void;
    isOpen: boolean;
  }

  export interface AddressChangeBottomSheetProps {
    isOpen: boolean;
    setClose: () => void;
  }

  export interface CartAddressChangeProps {
    onAddPress?: () => void;
    onClose?: () => void;
  }

  export interface AllAddressChangeProps {
    setSheets: React.Dispatch<
      React.SetStateAction<{
        isFull: boolean;
        isAddEdit: boolean;
        isweek: boolean;
      }>
    >;
    sheets: {
      isFull: boolean;
      isAddEdit: boolean;
      isweek: boolean;
    };
  }

  export interface AddEditAddressProps {
    setSheets: React.Dispatch<
      React.SetStateAction<{
        isFull: boolean;
        isAddEdit: boolean;
        isweek: boolean;
      }>
    >;
  }

  export interface AddressTypeProps {
    value: string;
    onChange: (e: string) => void;
  }

  export interface WeekAddressCardProps {
    title: string;
    address_id: string;
    slot: string;
    titleStyle?: StyleProp<TextStyle>;
    cardStyles?: StyleProp<ViewStyle>;
    showEdit?: boolean;
    isSelected?: boolean;
    onPress?: ((event: GestureResponderEvent) => void) | null;
    start_from: string;
    instruction: string[];
  }

  export interface PlanDetailsCardProps {
    onPressDietType?: () => void;
    onPressAddressType?: () => void;
    onPressDate?: () => void;
    onPressDays?: () => void;
    isSunday?: boolean;
  }

  export interface PriceListMenuProps {
    title: string;
    value: string;
    color?: string;
    titleStyle?: TextStyle;
    valueStyle?: TextStyle;
  }

  export interface cartStepers {
    nextStep: () => void;
    preStep: () => void;
  }

  export interface UserDetailsBottomSheetProps {
    isOpen?: boolean;
    setClose?: () => void;
    closeOnBackdrop?: boolean;
    closeOnPressBack?: boolean;
  }

  export interface TransactionsCardProps {
    type: string;
    amount: string | number;
    date: string;
    points: string | number;
    isCredit?: boolean;
  }

  export interface FeatureCarouselItemProps {
    title?: string;
    subtitle?: string;
    price?: string;
    image?: ImageSourcePropType | undefined;
    rating?: string;
    review?: string;
  }

  export interface DeliveryOptionCardProps {
    title?: string;
    description?: string;
    imgSrc?: ImageSourcePropType | undefined;
    width?: number;
    height?: number;
    isSelected?: boolean;
    onSelect?: () => void;
  }

  export interface CoolerBoxSheetProps {
    isOpen: boolean;
    setClose: () => void;
    onSuccess: () => void;
  }

  export interface CardBorderProps {
    style?: StyleProp<ViewStyle>;
    children: ReactNode;
  }

  export interface CardTitleProps {
    title: string;
  }

  export interface CardContentProps {
    Icon: FC<SvgProps>;
    text: string;
    showBorder?: boolean;
  }
}
