image: openjdk:17
# image: reactnativecommunity/react-native-android
stages:
  - install
  - build
  - notify

# before_script:
#   - apt-get update -qq && apt-get install -y openjdk-17-jdk zip unzip
#   - export JAVA_HOME=/usr/lib/jvm/java-17-openjdk-amd64
#   - node -v
#   - java -version
#   - echo "Setting environment variables..."
#   - echo "$ENV_PROD_FILE" > .env.prod

# install_dependencies:
#   stage: install
#   script:
#     - npm install

build_apk:
  stage: build
  script:
    # - apt-get update -qq && apt-get install -y openjdk-17-jdk zip unzip
    # - export JAVA_HOME=/usr/lib/jvm/java-17-openjdk-amd64
    # - node -v
    # - java -version
    - apt-get update -qq && apt-get install -y curl unzip wget git nodejs npm
    - node -v
    - npm -v
    - java -version

  # Install Android SDK
    - mkdir -p "$ANDROID_HOME/cmdline-tools"
    - cd "$ANDROID_HOME/cmdline-tools"
    - wget https://dl.google.com/android/repository/commandlinetools-linux-11076708_latest.zip -O tools.zip
    - unzip tools.zip -d tools
    - yes | sdkmanager --licenses
    - sdkmanager "platform-tools" "platforms;android-33" "build-tools;33.0.2"

  # Return to project root
    - cd "$CI_PROJECT_DIR"
    # Create .env.prod and show contents
    - cp "$ENV_PROD_FILE" .env.prod
    - echo "Contents of .env.prod:"
    - cp "$ENV_LOCAL_FILE" .env
    - echo "Contents of .env:"

    # Install dependencies
    - npm install

    # Build APK
    - npm run build:android:prod # Replace with your actual build script
  artifacts:
    paths:
      - android/app/build/outputs/apk/release/app-release.apk
    expire_in: 1 week
  only:
    - feature/fitness
  environment:
    name : non-prod

# send_notification:
#   stage: notify
#   script:
#     - echo "Build completed. Sending notification..."
    # You can use curl or a Node.js script to send an email/slack message here
